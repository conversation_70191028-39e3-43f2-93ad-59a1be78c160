import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

PATH1 = 'For_2025_07_15/StableCox_main/processed_equipment_data_new.csv'
PATH2 = 'For_2025_07_15/StableCox_main/processed_equipment_data_all_new.csv'
duration_col = 'duration'  # 修改为实际列名

problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']

SN_col = 'SN_Common'

df1 = pd.read_csv(PATH1)
df2 = pd.read_csv(PATH2)
df1 = df1.drop(columns=problematic_cols)
df2 = df2.drop(columns=problematic_cols)

df3 = df2[~df2['SN_Common'].isin(df1['SN_Common'])]
df1 = df1.drop(columns=[SN_col])
df2 = df2.drop(columns=[SN_col])
df3 = df3.drop(columns=[SN_col])

plt.figure(figsize=(8, 5))
sns.kdeplot(df1[duration_col], label='Data with filtration', fill=True, alpha=0.4)
sns.kdeplot(df3[duration_col], label='Data without filtration', fill=True, alpha=0.4)
plt.xlabel('Duration')
plt.ylabel('Density')
plt.title(f'Distribution of {duration_col}')
plt.legend()
plt.show()

col_show =  '100A_Flattop_Negative'#'EPI_700V_X2_LOW' #"Heat_Sink_Temp_Amplifier_Lavaflex"

plt.figure(figsize=(8, 5))
sns.kdeplot(df1[col_show], label='Data with filtration', fill=True, alpha=0.4)
sns.kdeplot(df3[col_show], label='Data without filtration', fill=True, alpha=0.4)
plt.xlabel('Duration')
plt.ylabel('Density')
plt.title(f'Distribution of {col_show}')
plt.legend()
plt.show()

# 排除duration和event列
from scipy.stats import ks_2samp

exclude_cols = ['duration', 'event']  # 如果event列名不同请修改
feature_cols = [col for col in df1.columns if col not in exclude_cols]

# 1. 均值和标准差对比
mean_std_df = pd.DataFrame({
    'mean_df1': df1[feature_cols].mean(),
    'std_df1': df1[feature_cols].std(),
    'mean_df3': df3[feature_cols].mean(),
    'std_df3': df3[feature_cols].std()
})
print("均值和标准差对比：")
print(mean_std_df)

# 2. 协方差矩阵对比
print("\n协方差矩阵（df1）：")
print(df1[feature_cols].cov())
print("\n协方差矩阵（df3）：")
print(df3[feature_cols].cov())

# 3. KS检验
ks_results = {}
for col in feature_cols:
    stat, p = ks_2samp(df1[col], df3[col])
    ks_results[col] = {'KS_stat': stat, 'p_value': p}
ks_df = pd.DataFrame(ks_results).T
print("\nKS检验结果：")
print(ks_df)

# 4. 可选：相关系数矩阵对比
print("\n相关系数矩阵（df1）：")
print(df1[feature_cols].corr())
print("\n相关系数矩阵（df3）：")
print(df3[feature_cols].corr())
