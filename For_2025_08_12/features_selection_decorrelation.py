import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import VarianceThreshold
import seaborn as sns
import numpy as np
import matplotlib.pyplot as plt


def remove_highly_correlated_features(df, columns, threshold=0.8, plot=True):
    """
    移除高度相关的特征，并提供详细的分析报告

    参数:
    df: DataFrame
    columns: 要分析的列名列表
    threshold: 相关性阈值，默认0.8
    plot: 是否显示可视化，默认True

    返回:
    selected_cols: 保留的列名列表
    """
    print(f"原始特征数量: {len(columns)}")

    # 1. 计算相关性矩阵
    corr_matrix = df[columns].corr().abs()

    # 2. 获取上三角矩阵
    upper_triangle = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))

    # 3. 找出高相关性的特征对
    high_corr_pairs = []
    for column in upper_triangle.columns:
        high_corr = upper_triangle[column][upper_triangle[column] > threshold]
        for idx, value in high_corr.items():
            high_corr_pairs.append((column, idx, value))

    # 4. 按相关性大小排序
    high_corr_pairs = sorted(high_corr_pairs, key=lambda x: x[2], reverse=True)

    # 5. 打印高相关性特征对
    if len(high_corr_pairs) > 0:
        print("\n高相关性特征对 (相关系数 > {})：".format(threshold))
        for feat1, feat2, corr in high_corr_pairs:
            print(f"{feat1} -- {feat2}: {corr:.3f}")

    # 6. 确定要移除的列
    high_corr_cols = []
    seen = set()
    for col1, col2, corr in high_corr_pairs:
        if col1 not in seen and col2 not in seen:
            # 根据方差选择保留哪个特征
            var1 = df[col1].var()
            var2 = df[col2].var()
            if var1 >= var2:
                high_corr_cols.append(col2)
                seen.add(col2)
            else:
                high_corr_cols.append(col1)
                seen.add(col1)
        elif col1 not in seen:
            high_corr_cols.append(col1)
            seen.add(col1)
        elif col2 not in seen:
            high_corr_cols.append(col2)
            seen.add(col2)

    # 7. 获取保留的列
    selected_cols = [col for col in columns if col not in high_corr_cols]

    # 8. 打印统计信息
    print(f"\n移除的特征数量: {len(high_corr_cols)}")
    print(f"保留的特征数量: {len(selected_cols)}")

    if plot:
        # 9. 可视化相关性矩阵
        plt.figure(figsize=(12, 8))
        sns.heatmap(df[selected_cols].corr().abs(),
                   cmap='RdYlBu_r',
                   vmin=0,
                   vmax=1,
                   center=0.5)
        plt.title('Heatmap of Selected Features')
        plt.tight_layout()
        plt.show()

        # 10. 特征相关性分布
        plt.figure(figsize=(10, 6))
        sns.histplot(data=upper_triangle.values.flatten(),
                    bins=50,
                    kde=True)
        plt.axvline(x=threshold, color='r', linestyle='--',
                   label=f'Threshold: ({threshold})')
        plt.title('Distribution of Correlation Coefficients')
        plt.xlabel('Correlation Coefficient')
        plt.ylabel('Frequency')
        plt.legend()
        plt.show()

    # 11. 验证结果
    new_corr = df[selected_cols].corr().abs()
    new_upper = new_corr.where(np.triu(np.ones(new_corr.shape), k=1).astype(bool))
    max_corr = new_upper.max().max()
    print(f"\n验证: 保留特征中的最大相关系数为 {max_corr:.3f}")

    if max_corr > threshold:
        print("警告：仍然存在高相关性特征！")

    return selected_cols

if __name__ == '__main__':

    # Try to import all_measurement_cols, handle if not found.
    try:
        from For_2025_07_07.Stable_Cox_Proj.model.COL import pulse_cols, power_on_cols_A, power_on_cols_B, burnin_cols, \
            pretest_cols

        all_measurement_cols = sorted(list(set(
            pulse_cols + power_on_cols_A + power_on_cols_B + burnin_cols + pretest_cols
        )))
    except:
        raise ValueError("Cannot import all_measurement_cols from COL.py")

    df_merged1 = pd.read_csv("../For_2025_07_15/StableCox_main/processed_equipment_data_new.csv")


    # Step 1: Standardize all_measurement_cols
    scaler = StandardScaler()
    df_merged1[all_measurement_cols] = scaler.fit_transform(df_merged1[all_measurement_cols])

    # Step 2: Remove low variance features
    # 1. 首先计算和查看原始方差
    original_variances = df_merged1[all_measurement_cols].var()
    print("原始方差分布：")
    print(original_variances.describe())

    # 2. 使用VarianceThreshold进行特征选择
    selector = VarianceThreshold(threshold=0.05)
    X_var = selector.fit_transform(df_merged1[all_measurement_cols])

    # 3. 获取保留的列名
    selected_cols_var = [col for i, col in enumerate(all_measurement_cols) if selector.get_support()[i]]

    # 4. 获取被移除的列名
    removed_cols = [col for col in all_measurement_cols if col not in selected_cols_var]
    print(f"Removed {len(removed_cols)} low variance columns: {removed_cols[:10]}{'...' if len(removed_cols) > 10 else ''}")

    # 5. 打印详细信息
    print(f"\n总特征数: {len(all_measurement_cols)}")
    print(f"保留特征数: {len(selected_cols_var)}")
    print(f"移除特征数: {len(removed_cols)}")

    # 6. 验证被移除的列确实是低方差的
    print("\n被移除列的方差:")
    for col in removed_cols:
        var = df_merged1[col].var()
        print(f"{col}: {var:.6f}")

    # 7. 验证保留的列确实是高方差的
    print("\n随机抽样5个保留列的方差:")
    for col in np.random.choice(selected_cols_var, min(5, len(selected_cols_var)), replace=False):
        var = df_merged1[col].var()
        print(f"{col}: {var:.6f}")

    # 8. 双重检查
    verification = df_merged1[selected_cols_var].var() >= 0.05
    if not verification.all():
        print("\n警告：有些保留的列方差可能低于阈值！")
        problematic_cols = verification[~verification].index
        print("问题列：", problematic_cols)

    # 使用示例
    selected_cols_uncorrelated = remove_highly_correlated_features(
        df_merged1,
        selected_cols_var,
        threshold=0.35
    )

    # grid_result = pd.DataFrame(columns=['threshold', 'num_features', 'features'])
    # thresholds = np.arange(0.15, 0.5, 0.03)
    # for threshold in thresholds:
    #     selected_cols_uncorrelated = remove_highly_correlated_features(
    #         df_merged1,
    #         selected_cols_var,
    #         threshold=threshold
    #     )
    #     tmp = pd.DataFrame({
    #         'threshold': [threshold],
    #         'num_features': [len(selected_cols_uncorrelated)],
    #         'features': [selected_cols_uncorrelated]
    #     })
    #     grid_result = pd.concat([grid_result, tmp], ignore_index=True)

    # 保存结果（可选）
    # grid_result.to_csv(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_15\selected_features.csv', index=False)