import pandas as pd
import numpy as np
import importlib.util
import sys
sys.path.append(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_15')
from For_2025_07_07.Stable_Cox_Proj.model.COL import pulse_cols, pretest_cols, burnin_cols, power_on_cols_A, power_on_cols_B


# 1. 导入 COL.py，获取特征组
feature_groups = {
    'pulse_cols': pulse_cols,
    'pretest_cols': pretest_cols,
    'burnin_cols': burnin_cols,
    'power_on_cols_A': power_on_cols_A,
    'power_on_cols_B': power_on_cols_B
}

# 2. 读取数据
merged = pd.read_csv(r"C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_15\StableCox_main\merged_tests_aggregated.csv")
sssa = pd.read_csv(r"C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_15\StableCox_main\SSSA_filled.csv")

# 3. 找到 Time_Series==0 的行
target_rows = sssa[sssa['Time_Series'] == 0].copy()
sssa = sssa[sssa['Time_Series'] != 0].copy()  # 删除原始

# 4. 优化的填充策略
for idx in target_rows.index:
    # 4.1 首先检查哪些特征组需要填充
    groups_to_fill = {}
    all_cols_to_fill = []

    for group_name, cols in feature_groups.items():
        valid_cols = [c for c in cols if c in merged.columns and c in target_rows.columns]
        if valid_cols:
            # 检查这个特征组是否有缺失值需要填充
            needs_filling = [c for c in valid_cols if pd.isna(target_rows.at[idx, c])]
            if needs_filling:
                groups_to_fill[group_name] = valid_cols
                all_cols_to_fill.extend(valid_cols)

    # 4.2 如果有多个特征组需要填充，尽量从同一行采样
    if len(groups_to_fill) > 1:
        # 找到包含尽可能多特征的行
        available_cols = [c for c in all_cols_to_fill if c in merged.columns]

        if available_cols:
            # 计算每行有多少个目标列是非空的
            merged_subset = merged[available_cols].copy()
            coverage_scores = merged_subset.notna().sum(axis=1)

            # 选择覆盖度最高的行进行采样
            best_rows = merged_subset[coverage_scores >= coverage_scores.quantile(0.75)]
            if len(best_rows) > 0:
                sample_row = best_rows.sample(n=1, random_state=np.random.randint(0, 100000)).iloc[0]

                # 从这一行填充尽可能多的特征
                filled_groups = set()
                for group_name, valid_cols in groups_to_fill.items():
                    group_filled = True
                    for col_name in valid_cols:
                        if col_name in sample_row.index and pd.notna(sample_row[col_name]):
                            value = sample_row[col_name]
                            if np.issubdtype(type(value), np.number):
                                noise = np.random.normal(0, 1e-5)  # 极小扰动
                                value = value + noise
                            target_rows.at[idx, col_name] = value
                        else:
                            group_filled = False

                    if group_filled:
                        filled_groups.add(group_name)

                # 对于未能完全填充的特征组，单独处理
                remaining_groups = {k: v for k, v in groups_to_fill.items() if k not in filled_groups}
                groups_to_fill = remaining_groups

    # 4.3 对于剩余的特征组，按组进行采样
    for group_name, valid_cols in groups_to_fill.items():
        # 检查哪些列还需要填充
        cols_still_missing = [c for c in valid_cols if pd.isna(target_rows.at[idx, c])]

        if cols_still_missing:
            # 为这个特征组采样一行
            group_sample = merged[valid_cols].dropna().sample(n=1, random_state=np.random.randint(0, 100000))
            if len(group_sample) > 0:
                group_sample_row = group_sample.iloc[0]

                for col_name in cols_still_missing:
                    if col_name in group_sample_row.index:
                        value = group_sample_row[col_name]
                        if np.issubdtype(type(value), np.number):
                            noise = np.random.normal(0, 1e-5)  # 极小扰动
                            value = value + noise
                        target_rows.at[idx, col_name] = value

    # 4.4 对于仍有NA的列，单独采样补全
    for col_name in target_rows.columns:
        if pd.isna(target_rows.at[idx, col_name]) and col_name in merged.columns:
            value = merged[col_name].dropna().sample(n=1).iloc[0]
            if np.issubdtype(type(value), np.number):
                value = value + np.random.normal(0, 1e-5)
            target_rows.at[idx, col_name] = value

# 5. 合并并保存
final_df = pd.concat([sssa, target_rows], ignore_index=True)
final_df.to_csv("SSSA_filled_new.csv", index=False)
print("优化填充完成，已保存为 SSSA_filled_new.csv")
print(f"处理了 {len(target_rows)} 行 Time_Series==0 的数据")
