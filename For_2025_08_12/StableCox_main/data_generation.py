# Base on StableCox
# import sys
#
# sys.path.append(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_06_23\StableCox_main')

from data.selection_bias import gen_selection_bias_data
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
import numpy as np
import argparse
import os
import torch
import pandas as pd



def get_args():
    parser = argparse.ArgumentParser(description="Script to launch sample reweighting experiments",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)

    # data generation
    parser.add_argument("--p", type=int, default=200, help="Input dim")
    parser.add_argument("--n", type=int, default=2000, help="Sample size")
    parser.add_argument("--V_ratio", type=float, default=0.2)
    parser.add_argument("--Vb_ratio", type=float, default=0.1)
    parser.add_argument("--true_func", choices=["linear", ], default="linear")
    parser.add_argument("--mode", choices=["S_|_V", "S->V", "V->S", "collinearity"], default="S_|_V")
    parser.add_argument("--misspe", choices=["poly", "exp", "None"], default="poly")
    parser.add_argument("--corr_s", type=float, default=0.9)
    parser.add_argument("--corr_v", type=float, default=0.1)
    parser.add_argument("--mms_strength", type=float, default=1.0, help="model misspecifction strength")
    parser.add_argument("--spurious", choices=["nonlinear", "linear"], default="nonlinear")
    parser.add_argument("--r_train", type=float, default=2.5, help="Input dim")
    parser.add_argument("--r_list", type=float, nargs="+", default=[-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3])
    parser.add_argument("--noise_variance", type=float, default=0.1)

    # frontend reweighting
    parser.add_argument("--reweighting", choices=["None", "DWR", "SRDO"], default="SRDO")
    parser.add_argument("--decorrelation_type", choices=["global", "group"], default="global")
    parser.add_argument("--order", type=int, default=1)
    parser.add_argument("--iters_balance", type=int, default=2000)

    parser.add_argument("--topN", type=int, default=5)
    # backend model
    parser.add_argument("--backend", choices=["OLS", "Lasso", "Ridge", "Weighted_cox", "LogLogistic"],
                        default="Weighted_cox")
    parser.add_argument("--paradigm", choices=["regr", "fs", ], default="regr")
    parser.add_argument("--iters_train", type=int, default=1000)
    parser.add_argument("--lam_backend", type=float, default=0.01)  # regularizer coefficient
    parser.add_argument("--fs_type", choices=["oracle", "None", "given", "STG"], default="STG")
    parser.add_argument("--mask_given", type=int, nargs="+", default=[1, 1, 1, 1, 1, 0, 0, 0, 0, 0])
    parser.add_argument("--mask_threshold", type=float, default=0.2)
    parser.add_argument("--lam_STG", type=float, default=3)
    parser.add_argument("--sigma_STG", type=float, default=0.1)
    parser.add_argument("--metrics", nargs="+", default=["L1_beta_error", "L2_beta_error"])
    parser.add_argument("--bv_analysis", action="store_true")

    parser.add_argument("--gener_method", choices=["cox_exp", "cox_weibull", "poly", "cox_Gompertz", "exp_T", "log_T"],
                        default="cox_Gompertz")
    # others
    parser.add_argument("--seed", type=int, default=3)
    parser.add_argument("--times", type=int, default=1)
    parser.add_argument("--result_dir", default="results")

    return parser.parse_args()


def generate_indicator(Y, cencored_rate=0.1):
    n = Y.shape[0]
    num_elements = int(n * cencored_rate)
    indices = np.random.choice(n, size=num_elements, replace=False)
    random_values = np.random.uniform(0, Y[indices])
    Y[indices] = random_values
    indicator = np.ones_like(Y)
    indicator[indices] = 0
    return Y, indicator


def main(args, round, logger):
    setup_seed(args.seed + round)
    p = args.p
    p_v = int(p * args.V_ratio)
    p_s = p - p_v
    n = args.n
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    oracle_mask = [True, ] * p_s + [False, ] * p_v

    # generate train data
    X_train, S_train, V_train, fs_train, Y_train = gen_selection_bias_data({**vars(args), **{"r": args.r_train}})

    Y_train, Y_censored = generate_indicator(Y_train, cencored_rate=0.25)
    X_train_pd = pd.DataFrame(np.concatenate((Y_train.reshape((-1, 1)), Y_censored.reshape((-1, 1)), X_train), axis=1),
                              columns=["duration", "event"] + list(range(0, X_train.shape[1])))

    X_train_pd.to_csv(r"C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_08_12\StableCox_main\Generated_data.csv", index=False)

    # beta_s = get_beta_s(p_s)
    # beta_v = np.zeros(p_v)
    # beta = np.concatenate([beta_s, beta_v])
    #
    # linear_var, nonlinear_var, total_var = calc_var(beta_s, S_train, fs_train)
    # logger.info(
    #     "Linear term var: %.3f, Nonlinear term var: %.3f, total var: %.3f" % (linear_var, nonlinear_var, total_var))
    #
    # # generate test data
    # test_data = dict()
    # for r_test in args.r_list:
    #     X_test, S_test, V_test, fs_test, Y_test = gen_selection_bias_data({**vars(args), **{"r": r_test}})
    #     Y_test, Y_censored = generate_indicator(Y_test, cencored_rate=0.76)
    #     X_test_pd = pd.DataFrame(np.concatenate((Y_test.reshape((-1, 1)), Y_censored.reshape((-1, 1)), X_test), axis=1),
    #                              columns=["Survival.months", "Survival.status"] + list(range(0, X_test.shape[1])))
    #     test_data[r_test] = (X_test, S_test, V_test, fs_test, Y_test, X_test_pd)
    #
    # for r_test, test in test_data.items():
    #     print("test ratio:", r_test)
    #     X_test, S_test, V_test, fs_test, Y_test, X_test_pd = test
    #     # X_test_pd = X_test_pd[list(X_test_pd.columns[:2])+list(fs_sorted_indices)]
    #     columns = X_test_pd.columns
    #     tmp = np.concatenate((X_test_pd, np.ones((n, 1))), axis=1)
    #     X_test_pd = pd.DataFrame(tmp, columns=list(columns) + ["Weights"])

if __name__ == "__main__":
    args = get_args()
    setup_seed(args.seed)
    expname = get_expname(args)
    os.makedirs(os.path.join(args.result_dir, expname), exist_ok=True)
    logger = Logger(args)
    logger.log_args(args)

    p = args.p
    p_v = int(p * args.V_ratio)
    p_s = p - p_v
    beta_s = get_beta_s(p_s)
    beta_v = np.zeros(p_v)
    beta = np.concatenate([beta_s, beta_v])

    main(args, 1, logger)


