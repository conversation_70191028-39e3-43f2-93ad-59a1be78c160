# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import <PERSON>P<PERSON>itter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings

warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import StratifiedKFold
import optuna
from optuna.samplers import TPESampler
import io
import contextlib
import re
from SurvivalEVAL.Evaluator import LifelinesEvaluator

# STG相关导入
try:
    from stg import STG
    import stg.utils as stg_utils

    STG_AVAILABLE = True
except ImportError:
    logging.warning("STG package not found. Please install stg package to use this functionality.")
    STG_AVAILABLE = False

# 兼容性修复
import collections
import collections.abc

collections.Sequence = collections.abc.Sequence
collections.Mapping = collections.abc.Mapping
collections.Set = collections.abc.Set

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

# device = 'cuda' if torch.cuda.is_available() else 'cpu'
device = 'cpu'

class STGEarlyStopping:
    """
    Early stopping for STG model training - 简化版本，不依赖state_dict
    """

    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0

    def __call__(self, score):
        if self.best_score is None:
            self.best_score = score
            return False
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = score
            self.counter = 0
        return False


class STGNativeEarlyStopping:
    """
    Early stopping hook for STG's fit method.
    Performs validation internally to decide when to stop.
    """

    def __init__(self, stg_instance, val_data, patience=20, min_delta=0.001):
        self.stg_instance = stg_instance
        self.val_data = val_data
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None

    def __call__(self, model):  # model is the internal torch model, not the STG wrapper
        # Use the stg_instance to call public methods
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            # Use the validation data provided during initialization
            self.stg_instance.evaluate(self.val_data['X'], {'E': self.val_data['E'], 'T': self.val_data['T']})
        output = f.getvalue()

        match = re.search(r"test_CI=([0-9.]+)", output)
        if not match:
            return False  # Continue if score not found

        score = float(match.group(1))

        if self.best_score is None:
            self.best_score = score
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                logging.info(f"Early stopping triggered. Best score: {self.best_score:.4f}")
                return True  # Stop
        else:
            self.best_score = score
            self.counter = 0

        return False  # Continue


def stg_survival_function_prediction(estimator, X, time_points=None):
    """
    Convert STG predictions to survival function format for SurvivalEVAL
    由于STG模型主要设计用于Cox回归的C-index优化，没有直接的风险预测方法，
    我们需要通过其内部结构来构造合理的生存函数
    """
    try:
        # 确保输入数据格式正确
        if isinstance(X, pd.DataFrame):
            X_np = X.values.astype(np.float32)
        else:
            X_np = X.astype(np.float32)

        # 如果没有指定时间点，创建合理的时间范围
        if time_points is None:
            time_points = np.linspace(0.1, 100, 50)

        # STG模型的风险预测策略
        risk_scores = None

        try:
            # 方法1: 尝试使用STG内部模型直接预测
            if hasattr(estimator, 'model') and estimator.model is not None:
                X_tensor = torch.FloatTensor(X_np).to(estimator.device)
                estimator.model.eval()
                with torch.no_grad():
                    # STG的前向传播通常返回logits
                    outputs = estimator.model(X_tensor)

                    # 处理不同的输出格式
                    if isinstance(outputs, tuple):
                        # 如果是tuple，通常第一个是主要输出
                        risk_logits = outputs[0]
                    else:
                        risk_logits = outputs

                    # 转换为numpy并确保形状正确
                    if hasattr(risk_logits, 'detach'):
                        risk_scores = risk_logits.detach().cpu().numpy()
                    else:
                        risk_scores = risk_logits

                    risk_scores = np.array(risk_scores).flatten()

                    # 确保长度匹配
                    if len(risk_scores) != X_np.shape[0]:
                        logging.warning(f"Risk scores length mismatch, reshaping from {len(risk_scores)} to {X_np.shape[0]}")
                        if len(risk_scores) > X_np.shape[0]:
                            risk_scores = risk_scores[:X_np.shape[0]]
                        else:
                            # 如果长度不够，重复或插值
                            risk_scores = np.resize(risk_scores, X_np.shape[0])

                    logging.info(f"Successfully extracted risk scores from STG model: shape={risk_scores.shape}, range=[{risk_scores.min():.4f}, {risk_scores.max():.4f}]")

        except Exception as e:
            logging.warning(f"Failed to extract risk scores from STG model: {str(e)}")
            risk_scores = None

        # 方法2: 如果直接预测失败，使用基于特征的简单风险模型
        if risk_scores is None:
            try:
                # 获取特征重要性
                feature_gates = None
                try:
                    feature_gates = estimator.get_gates(mode='prob')
                    if hasattr(feature_gates, 'detach'):
                        feature_gates = feature_gates.detach().cpu().numpy()
                    feature_gates = np.array(feature_gates).flatten()
                except:
                    try:
                        feature_gates = estimator.get_gates()
                        if hasattr(feature_gates, 'detach'):
                            feature_gates = feature_gates.detach().cpu().numpy()
                        feature_gates = np.array(feature_gates).flatten()
                    except:
                        feature_gates = None

                if feature_gates is not None and len(feature_gates) == X_np.shape[1]:
                    # 使用特征重要性加权计算风险分数
                    # 归一化特征重要性
                    feature_weights = feature_gates / (np.sum(feature_gates) + 1e-8)

                    # 计算加权风险分数
                    risk_scores = np.sum(X_np * feature_weights, axis=1)

                    # 添加一些随机性以避免所有样本风险相同
                    risk_scores += np.random.normal(0, 0.1, size=risk_scores.shape)

                    logging.info(f"Used feature gates to compute risk scores: shape={risk_scores.shape}")
                else:
                    # 最后的fallback：基于特征均值的简单风险分数
                    risk_scores = np.mean(X_np, axis=1) + np.random.normal(0, 0.1, size=X_np.shape[0])
                    logging.warning("Used feature mean as fallback risk scores")

            except Exception as e:
                logging.warning(f"Feature-based risk calculation failed: {str(e)}")
                # 最终fallback
                risk_scores = np.random.normal(0, 1, size=X_np.shape[0])
                logging.warning("Used random risk scores as final fallback")

        # 确保risk_scores有效
        if risk_scores is None:
            risk_scores = np.random.normal(0, 1, size=X_np.shape[0])
            logging.warning("Risk scores is None, using random values")

        risk_scores = np.array(risk_scores).flatten()

        # 将风险分数转换为生存函数
        survival_probs = []

        # 标准化风险分数为正的hazard rates
        # 确保hazard rates为正值
        risk_scores_centered = risk_scores - np.min(risk_scores) + 0.01

        # 使用相对风险的概念：高风险分数 -> 高hazard rate -> 低生存概率
        # 使用中位生存时间来校准hazard rates
        median_time = np.median(time_points)

        # 将风险分数映射到合理的hazard rate范围
        # 使用sigmoid变换确保hazard rates在合理范围内
        normalized_risks = (risk_scores_centered - np.mean(risk_scores_centered)) / (np.std(risk_scores_centered) + 1e-8)
        hazard_rates = 0.01 + 0.1 * (1 / (1 + np.exp(-normalized_risks)))  # hazard rates在[0.01, 0.11]范围内

        for hazard_rate in hazard_rates:
            # 生成生存曲线: S(t) = exp(-hazard_rate * t)
            survival_curve = np.exp(-hazard_rate * time_points)
            # 确保生存概率在有效范围内
            survival_curve = np.clip(survival_curve, 1e-10, 1.0)
            survival_probs.append(survival_curve)

        # 创建类似lifelines格式的DataFrame
        survival_df = pd.DataFrame(
            np.array(survival_probs).T,
            index=time_points,
            columns=range(len(risk_scores))
        )

        logging.info(f"Generated survival functions: {survival_df.shape}, risk_scores range=[{risk_scores.min():.4f}, {risk_scores.max():.4f}]")
        return survival_df

    except Exception as e:
        logging.error(f"Error in STG survival function prediction: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        # 返回默认生存曲线
        if time_points is None:
            time_points = np.linspace(0.1, 100, 50)

        # 确定样本数量
        try:
            n_samples = X.shape[0] if hasattr(X, 'shape') else len(X)
        except:
            n_samples = 1

        # 创建默认的指数衰减生存曲线，每个样本略有不同
        default_survival_probs = []
        for i in range(n_samples):
            # 为每个样本创建略有不同的衰减率
            decay_rate = 0.01 + 0.005 * (i % 10) / 10  # 在0.01到0.015之间变化
            survival_curve = np.exp(-decay_rate * time_points)
            default_survival_probs.append(survival_curve)

        survival_df = pd.DataFrame(
            np.array(default_survival_probs).T,
            index=time_points,
            columns=range(n_samples)
        )

        logging.warning(f"Returned default survival curves: {survival_df.shape}")
        return survival_df


def evaluate_stg_with_survivaleval(estimator, X_train, y_train, X_val, y_val):
    """
    Evaluate STG model using SurvivalEVAL metrics
    Returns: (c_index, auc, ibs, mae)
    """
    try:
        # 转换数据格式
        train_times = y_train['t'] if isinstance(y_train, dict) else y_train['duration']
        train_events = y_train['e'] if isinstance(y_train, dict) else y_train['event']
        val_times = y_val['t'] if isinstance(y_val, dict) else y_val['duration']
        val_events = y_val['e'] if isinstance(y_val, dict) else y_val['event']

        # 获取生存函数预测
        survival_curves = stg_survival_function_prediction(estimator, X_val)

        # 创建SurvivalEVAL评估器
        evl = LifelinesEvaluator(survival_curves, val_times, val_events,
                               train_times, train_events)

        # 计算指标
        c_index, _, _ = evl.concordance()

        # 改进AUC计算逻辑 - 使用中位时间但增强诊断
        auc = 0.5  # 默认值
        try:
            # 使用事件时间的中位数
            event_times = val_times[val_events == 1]
            if len(event_times) > 0:
                target_time = np.median(event_times)
                if not np.isnan(target_time) and target_time > 0:
                    auc = evl.auc(target_time)
                else:
                    auc = 0.5
                    logging.warning(f"Invalid target time: {target_time}, using default AUC 0.5")


        except Exception as auc_error:
            logging.warning(f"AUC calculation completely failed: {str(auc_error)}")
            auc = 0.5

        # 计算IBS
        try:
            max_time = int(np.max(val_times))
            num_points = min(max_time + 1, 50)
            ibs = evl.integrated_brier_score(num_points=num_points, draw_figure=False)
        except:
            ibs = 1.0

        # 计算MAE
        try:
            mae = evl.mae(method="Hinge")
            if np.isnan(mae) or np.isinf(mae):
                logging.warning(f"MAE is NaN or Inf: {mae}")
                mae = 1000.0
        except:
            mae = 1000.0

        logging.info(f"SurvivalEVAL metrics - C-index: {c_index:.4f}, AUC: {auc:.4f}, IBS: {ibs:.4f}, MAE: {mae:.4f}")
        return c_index, auc, ibs, mae

    except Exception as e:
        logging.warning(f"SurvivalEVAL evaluation failed: {str(e)}")
        return 0.5, 0.5, 1.0, 1000.0


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集
    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler split with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols


def cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx):
    """
    Stage 1: STG hyperparameter optimization using Optuna with cross-validation
    Returns multiple metrics for multi-objective optimization
    """
    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return (0.5, 0.5, -1.0, -1000.0)

    # Optuna超参数建议
    n_layers = trial.suggest_int('n_layers', 1, 4)
    hidden_dims = []
    for i in range(n_layers):
        dim = trial.suggest_int(f'hidden_dim_{i + 1}', 1, 64)
        hidden_dims.append(dim)

    activation = trial.suggest_categorical('activation', ['relu', 'selu', 'tanh', 'sigmoid'])
    optimizer = trial.suggest_categorical('optimizer', ['Adam', 'SGD', 'Adagrad'])
    learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-1, log=True)
    sigma = trial.suggest_float('sigma', 0.01, 2.0, log=True)
    lam = trial.suggest_float('lam', 1e-3, 10, log=True)

    # 早停参数
    patience = trial.suggest_int('patience', 20, 50)

    logging.info(f"=== Starting STG cross-validation optimization ===")
    logging.info(f"Hidden dims: {hidden_dims}, activation: {activation}, optimizer: {optimizer}")

    # 交叉验证设置
    cv_splits = params.get('n_splits', 5)
    min_samples_per_fold = 20
    if len(df_trainval) < cv_splits * min_samples_per_fold:
        cv_splits = max(2, len(df_trainval) // min_samples_per_fold)
        logging.warning(f"Reducing CV folds to {cv_splits} due to small dataset size")

    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
        random_state=params['seed'] + round_idx
    )

    # 存储所有指标
    c_indices_val = []
    aucs_val = []
    ibs_val = []
    mae_val = []

    for fold_idx, (train_idx, val_idx) in enumerate(cv.split(df_trainval, df_trainval[event_col])):
        try:
            # 分割数据
            train_fold = df_trainval.iloc[train_idx].copy()
            val_fold = df_trainval.iloc[val_idx].copy()

            # 检查数据质量
            train_events = train_fold[event_col].sum()
            val_events = val_fold[event_col].sum()

            if train_events == 0 or train_events == len(train_fold):
                logging.warning(f"Fold {fold_idx}: Training set has only one type of outcome, skipping")
                continue

            if val_events == 0 or val_events == len(val_fold):
                logging.warning(f"Fold {fold_idx}: Validation set has only one type of outcome, skipping")
                continue

            if len(val_fold) < 5:
                logging.warning(f"Fold {fold_idx}: Validation set too small ({len(val_fold)} samples), skipping")
                continue

            train_fold_scaled = train_fold.copy()
            val_fold_scaled = val_fold.copy()

            # 准备STG格式的数据
            train_X = train_fold_scaled[feature_cols].to_numpy()
            train_y = {'e': train_fold_scaled[event_col].to_numpy(), 't': train_fold_scaled[duration_col].to_numpy()}

            val_X = val_fold_scaled[feature_cols].to_numpy()
            val_y = {'e': val_fold_scaled[event_col].to_numpy(), 't': val_fold_scaled[duration_col].to_numpy()}

            # prepare_data for STG
            train_data = {}
            train_data['X'], train_data['E'], train_data['T'] = stg_utils.prepare_data(train_X, train_y)
            train_data['ties'] = 'noties'

            val_data = {}
            val_data['X'], val_data['E'], val_data['T'] = stg_utils.prepare_data(val_X, val_y)
            val_data['ties'] = 'noties'

            # 再次检查prepared数据
            if len(val_data['E']) < 2 or len(np.unique(val_data['E'])) < 2:
                logging.warning(f"Fold {fold_idx}: Insufficient validation data after preparation, skipping")
                continue

            # 构建STG模型
            model = STG(
                task_type='cox',
                input_dim=train_data['X'].shape[1],
                output_dim=1,
                hidden_dims=hidden_dims,
                activation=activation,
                optimizer=optimizer,
                learning_rate=learning_rate,
                batch_size=train_data['X'].shape[0],
                feature_selection=True,
                sigma=sigma,
                lam=lam,
                random_state=params['seed'] + round_idx + fold_idx,
                device=device
            )

            # 早停机制
            early_stop_hook = STGNativeEarlyStopping(model, val_data, patience=patience)

            # 训练模型
            max_epochs = min(params.get('max_epochs', 300), 300)

            model.fit(
                train_data['X'],
                {'E': train_data['E'], 'T': train_data['T']},
                nr_epochs=max_epochs,
                valid_X=val_data['X'],
                valid_y={'E': val_data['E'], 'T': val_data['T']},
                print_interval=max_epochs,
                verbose=False,
                early_stop=early_stop_hook
            )

            # 使用SurvivalEVAL评估
            c_index, auc, ibs, mae = evaluate_stg_with_survivaleval(
                model, train_data['X'], train_y, val_data['X'], val_y
            )

            c_indices_val.append(c_index)
            aucs_val.append(auc)
            ibs_val.append(ibs)
            mae_val.append(mae)

            logging.info(f"Fold {fold_idx}: C-index={c_index:.4f}, AUC={auc:.4f}, IBS={ibs:.4f}, MAE={mae:.4f}")

        except Exception as e:
            logging.warning(f"Error in fold {fold_idx}: {str(e)}")
            continue

    # 如果没有成功的fold，返回很低的分数
    if not c_indices_val:
        return (0.5, 0.5, -1.0, -1000.0)

    # 返回多个指标的平均值供Optuna多目标优化
    # 注意：IBS和MAE取负值，因为Optuna要最大化，而我们要最小化这些指标
    mean_c_index = np.mean(c_indices_val)
    mean_auc = np.mean(aucs_val) if aucs_val else 0.5
    mean_ibs = np.mean(ibs_val) if ibs_val else 1.0
    mean_mae = np.mean(mae_val) if mae_val else 1000.0

    logging.info(f"Trial completed: CV C-index={mean_c_index:.4f}, AUC={mean_auc:.4f}, IBS={mean_ibs:.4f}, MAE={mean_mae:.4f}")
    return (mean_c_index, mean_auc, -mean_ibs, -mean_mae)


def train_final_stg_model_with_best_params(df_trainval, df_test, feature_cols, best_params, round_idx):
    """
    Stage 2: Train final STG model with best hyperparameters on entire trainval set,
    then evaluate on test set using SurvivalEVAL
    """
    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return None, [], None

    logging.info("=== Training final STG model and evaluation ===")

    # 从best_params中提取STG参数
    n_layers = best_params.get('n_layers', 2)
    hidden_dims = []
    for i in range(n_layers):
        dim = best_params.get(f'hidden_dim_{i + 1}', 64)
        hidden_dims.append(dim)

    activation = best_params.get('activation', 'relu')
    optimizer = best_params.get('optimizer', 'Adam')
    learning_rate = best_params.get('learning_rate', 1e-3)
    sigma = best_params.get('sigma', 0.5)
    lam = best_params.get('lam', 0.1)
    patience = best_params.get('patience', 50)

    logging.info(f"Final model parameters - Hidden dims: {hidden_dims}, activation: {activation}")
    logging.info(f"Learning rate: {learning_rate}, sigma: {sigma}, lambda: {lam}")

    df_trainval_scaled = df_trainval.copy()
    df_test_scaled = df_test.copy()

    # 准备STG格式的数据
    trainval_X = df_trainval_scaled[feature_cols].to_numpy()
    trainval_y = {'e': df_trainval_scaled[event_col].to_numpy(), 't': df_trainval_scaled[duration_col].to_numpy()}

    test_X = df_test_scaled[feature_cols].to_numpy()
    test_y = {'e': df_test_scaled[event_col].to_numpy(), 't': df_test_scaled[duration_col].to_numpy()}

    # prepare_data for STG
    trainval_data = {}
    trainval_data['X'], trainval_data['E'], trainval_data['T'] = stg_utils.prepare_data(trainval_X, trainval_y)
    trainval_data['ties'] = 'noties'

    test_data = {}
    test_data['X'], test_data['E'], test_data['T'] = stg_utils.prepare_data(test_X, test_y)
    test_data['ties'] = 'noties'

    # 构建最终STG模型
    final_model = STG(
        task_type='cox',
        input_dim=trainval_data['X'].shape[1],
        output_dim=1,
        hidden_dims=hidden_dims,
        activation=activation,
        optimizer=optimizer,
        learning_rate=learning_rate,
        batch_size=trainval_data['X'].shape[0],
        feature_selection=True,
        sigma=sigma,
        lam=lam,
        random_state=best_params.get('seed', 42) + round_idx,
        device=device
    )

    # 训练最终模型
    max_epochs = best_params.get('max_epochs', 1000)
    print_interval = max(max_epochs // 10, 100)

    logging.info(f"Training final model for {max_epochs} epochs")

    final_model.fit(
        trainval_data['X'],
        {'E': trainval_data['E'], 'T': trainval_data['T']},
        nr_epochs=max_epochs,
        valid_X=test_data['X'],
        valid_y={'E': test_data['E'], 'T': test_data['T']},
        print_interval=print_interval,
        verbose=True,
        early_stop=None
    )

    # 使用SurvivalEVAL在测试集上评估
    try:
        c_index, auc, ibs, mae = evaluate_stg_with_survivaleval(
            final_model,
            trainval_data['X'], trainval_y,
            test_data['X'], test_y
        )

        final_test_metrics = {
            'final_test_c_index': c_index,
            'final_test_time_dependent_auc': auc,
            'final_test_integrated_brier_score': ibs,
            'final_test_duration_rmse': mae  # 使用MAE替代RMSE
        }

        logging.info(f"Final test C-index: {c_index:.4f}")
        logging.info(f"Final test AUC: {auc:.4f}")
        logging.info(f"Final test IBS: {ibs:.4f}")
        logging.info(f"Final test MAE: {mae:.4f}")

    except Exception as eval_error:
        logging.warning(f"SurvivalEVAL evaluation failed: {str(eval_error)}")
        # 回退到基础C-index评估
        try:
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                final_model.evaluate(test_data['X'], {'E': test_data['E'], 'T': test_data['T']})
            output = f.getvalue()

            match = re.search(r"test_CI=([0-9.]+)", output)
            if match:
                final_test_c_index = float(match.group(1))
            else:
                final_test_c_index = 0.5
        except:
            final_test_c_index = 0.5

        final_test_metrics = {
            'final_test_c_index': final_test_c_index,
            'final_test_time_dependent_auc': 0.5,
            'final_test_integrated_brier_score': 1.0,
            'final_test_duration_rmse': 1000.0
        }

    # 获取选择的特征
    try:
        feature_importances = None
        try:
            feature_importances = final_model.get_gates(mode='prob')
            logging.info("Successfully used get_gates(mode='prob')")
        except TypeError:
            try:
                feature_importances = final_model.get_gates()
                logging.info("Successfully used get_gates() without parameters")
            except Exception:
                logging.warning("get_gates() methods failed")

        if feature_importances is None:
            try:
                if hasattr(final_model, 'mu'):
                    feature_importances = final_model.mu + 0.5
                    logging.info("Used internal mu attribute")
                elif hasattr(final_model, 'gates'):
                    feature_importances = final_model.gates
                    logging.info("Used internal gates attribute")
            except:
                pass

        if feature_importances is not None:
            if hasattr(feature_importances, 'detach'):
                feature_importances = feature_importances.detach().cpu().numpy()
            elif hasattr(feature_importances, 'numpy'):
                feature_importances = feature_importances.numpy()
            feature_importances = np.array(feature_importances).flatten()

        if feature_importances is not None and len(feature_importances) > 0:
            threshold = params.get('threshold_gates', 0.5)

            if feature_importances.max() < threshold:
                relative_threshold = max(feature_importances.mean(), np.median(feature_importances))
                selected_indices = np.where(feature_importances >= relative_threshold)[0]
            else:
                selected_indices = np.where(feature_importances > threshold)[0]

            selected_features = [feature_cols[i] for i in selected_indices]

            if len(selected_features) == 0:
                top_k = min(10, len(feature_cols))
                top_indices = np.argsort(feature_importances)[-top_k:]
                selected_features = [feature_cols[i] for i in top_indices]

            logging.info(f"Finally selected {len(selected_features)} features")
        else:
            selected_features = feature_cols[:10]
            logging.info(f"Using default selection: first 10 features")

    except Exception as e:
        logging.warning(f"Could not extract feature selection results: {str(e)}")
        selected_features = feature_cols[:10]

    return final_test_metrics, selected_features, final_model


def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting STG Round {round_idx} ---")

    if not STG_AVAILABLE:
        logging.error("STG package not available. Cannot proceed.")
        return {}

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Multi-objective hyperparameter optimization (using Optuna)
    # ===============================
    logging.info("=== Stage 1: Starting multi-objective hyperparameter optimization ===")

    n_trials_reduced = min(n_trials, 100)  # 限制试验次数
    logging.info(f"Use {n_trials_reduced} Optuna trials for multi-objective optimization")

    # 创建多目标优化study
    study = optuna.create_study(
        directions=['maximize', 'maximize', 'maximize', 'maximize'],  # C-index, AUC, -IBS, -MAE
        sampler=optuna.samplers.NSGAIISampler(seed=params['seed'] + round_idx)
    )

    # 定义优化目标函数
    def objective_wrapper(trial):
        return cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx)

    # 执行超参数优化
    study.optimize(objective_wrapper, n_trials=n_trials_reduced)

    # 获取最佳参数 (选择第一个目标C-index最好的试验)
    best_trial = max(study.trials, key=lambda t: t.values[0] if t.values else -1)
    best_params = params.copy()
    best_params.update(best_trial.params)
    best_cv_metrics = best_trial.values

    logging.info(f"Multi-objective optimization completed!")
    logging.info(f"Best validation scores - C-index: {best_cv_metrics[0]:.4f}, AUC: {best_cv_metrics[1]:.4f}, "
                f"IBS: {-best_cv_metrics[2]:.4f}, MAE: {-best_cv_metrics[3]:.4f}")
    logging.info(f"Best parameters: {best_trial.params}")

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final STG model training and evaluation ===")

    final_test_metrics, selected_features, final_model = train_final_stg_model_with_best_params(
        df_trainval, df_test, feature_cols, best_params, round_idx
    )

    if final_test_metrics is not None:
        logging.info(f"Final test C-index: {final_test_metrics['final_test_c_index']:.4f}")
        logging.info(f"Final test AUC: {final_test_metrics['final_test_time_dependent_auc']:.4f}")
        logging.info(f"Final test IBS: {final_test_metrics['final_test_integrated_brier_score']:.4f}")
        logging.info(f"Final test MAE: {final_test_metrics['final_test_duration_rmse']:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_c_index": best_cv_metrics[0],
            "best_cv_auc": best_cv_metrics[1],
            "best_cv_ibs": -best_cv_metrics[2],
            "best_cv_mae": -best_cv_metrics[3],
            "final_test_c_index": final_test_metrics['final_test_c_index'],
            "final_test_time_dependent_auc": final_test_metrics['final_test_time_dependent_auc'],
            "final_test_integrated_brier_score": final_test_metrics['final_test_integrated_brier_score'],
            "final_test_duration_rmse": final_test_metrics['final_test_duration_rmse'],
            "best_params": best_params,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final STG model training failed")
        return {}


def main(params):
    if not STG_AVAILABLE:
        print("ERROR: STG package not available. Please install the stg package to use this script.")
        exit(1)

    import time
    from datetime import datetime

    # Create logs folder
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # Simplified logging setup: only main process saves logs
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"STG_MultiObjective_{timestamp}.log")

        # Configure dual output to file and console
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # Set format
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Get root logger and add handlers
        logger = logging.getLogger()
        logger.handlers.clear()  # Clear existing handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info("=== STG Survival Analysis with Multi-objective Optimization ===")
        logging.info(f"Logging to: {log_filename}")
        logging.info(f"MPI processes: {size}")
        logging.info(f"Parameters: {params}")

    # Create results directory
    os.makedirs(params['result_dir'], exist_ok=True)

    # Record start time
    start_time = time.time()

    PATH = params['PATH']
    df_trainval, df_test, feature_cols = data_processing(PATH)

    # Run multiple rounds across MPI processes
    local_results = dd(list)
    rounds_per_process = params['times'] // size
    remainder = params['times'] % size

    # Distribute rounds among processes
    if rank < remainder:
        local_rounds = rounds_per_process + 1
        start_round = rank * (rounds_per_process + 1)
    else:
        local_rounds = rounds_per_process
        start_round = rank * rounds_per_process + remainder

    logging.info(f"Process {rank}: Running {local_rounds} rounds starting from round {start_round}")

    for i in range(local_rounds):
        round_idx = start_round + i
        logging.info(f"Process {rank}: Starting round {round_idx + 1}/{params['times']}")

        try:
            result = objective(
                n_trials=params.get('n_trials', 30),
                params=params,
                round_idx=round_idx,
                logger=logging,
                PATH=params['PATH'],
                df_trainval=df_trainval,
                df_test=df_test,
                feature_cols=feature_cols
            )

            if result:
                # 将结果按指标分类存储到字典中
                for k, v in result.items():
                    local_results[k].append(v)
                logging.info(f"Round {round_idx + 1} completed successfully")
            else:
                logging.warning(f"Round {round_idx + 1} failed")

        except Exception as e:
            logging.error(f"Error in round {round_idx + 1}: {str(e)}")

    # Gather results from all processes
    all_results = comm.gather(local_results, root=0)

    # Process and save results (only main process)
    if rank == 0:
        # Flatten results
        final_results_list = dd(list)
        for process_results in all_results:
            for k, v in process_results.items():
                final_results_list[k].extend(v)

        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                if metric_name == 'best_params':
                    continue

                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                        logging.warning("1. Increasing test set size")
                        logging.warning("2. Using more Optuna trials")
                        logging.warning("3. Fixing data splits across runs")
                        logging.warning("4. Ensemble methods")
                    logging.info("-" * 50)

                    final_test_auc_scores = final_results_list['final_test_time_dependent_auc']
                    final_test_auc_scores = [s for s in final_test_auc_scores if isinstance(s, (int, float, np.number))]
                    final_test_ibs = final_results_list['final_test_integrated_brier_score']
                    final_test_ibs = [s for s in final_test_ibs if isinstance(s, (int, float, np.number))]
                    final_test_rmse = final_results_list['final_test_duration_rmse']
                    final_test_rmse = [s for s in final_test_rmse if isinstance(s, (int, float, np.number))]
                    logging.info(
                        f"Final Test AUC: Mean={np.mean(final_test_auc_scores):.4f}, Std={np.std(final_test_auc_scores):.4f}")
                    logging.info(
                        f"Final Test IBS: Mean={np.mean(final_test_ibs):.4f}, Std={np.std(final_test_ibs):.4f}")
                    logging.info(
                        f"Final Test RMSE: Mean={np.mean(final_test_rmse):.4f}, Std={np.std(final_test_rmse):.4f}")

                    logging.info("=" * 70)

            # 保存最佳参数示例
            if 'best_params' in final_results_list and final_results_list['best_params']:
                best_example = final_results_list['best_params'][0]
                logging.info("EXAMPLE OF OPTIMIZED STG PARAMETERS:")
                stg_params = ['n_layers', 'activation', 'optimizer', 'learning_rate', 'sigma', 'lam', 'patience']
                for key in stg_params:
                    if key in best_example:
                        logging.info(f"  {key}: {best_example[key]}")

                # 显示隐藏层维度
                n_layers = best_example.get('n_layers', 0)
                hidden_dims_str = []
                for i in range(n_layers):
                    dim = best_example.get(f'hidden_dim_{i + 1}')
                    if dim is not None:
                        hidden_dims_str.append(str(dim))
                if hidden_dims_str:
                    logging.info(f"  hidden_dims: [{', '.join(hidden_dims_str)}]")
                logging.info("=" * 70)

        else:
            logging.error("No successful results obtained!")

        total_time = time.time() - start_time
        logging.info(f"Total execution time: {total_time:.2f} seconds")
        logging.info("=== Execution completed ===")


if __name__ == "__main__":

    params = {
        'PATH': r'processed_equipment_data_all.csv',
        'seed': 9,  # Fixed base seed
        'n_splits': 10,  # Cross-validation folds
        'times': 5,
        'result_dir': 'results',
        'test_size': 0.1,
        # STG specific parameters
        'max_epochs': 1000,  # Maximum training epochs
        'n_trials': 10,  # Number of Optuna trials
        'threshold_gates': 0.001,
    }

    main(params)

# 运行命令:
# mpiexec -n 4 python ZZ_STG_v4.py
