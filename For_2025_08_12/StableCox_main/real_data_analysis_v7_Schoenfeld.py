# 导入必要的库
import pandas as pd
import numpy as np
from lifelines import CoxPHFitter
from lifelines.statistics import proportional_hazard_test
import matplotlib.pyplot as plt
import logging
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置日志格式和级别
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

def plot_pdp_ice(cph, data, col, original_data=None, scaler=None, grid_points=20, plot_ice=True):
    X = data.copy()

    # 如果提供了原始数据和标准化器，使用原始数据的范围
    if original_data is not None and scaler is not None:
        # 获取原始数据的范围
        original_min = original_data[col].min()
        original_max = original_data[col].max()
        original_values = np.linspace(original_min, original_max, grid_points)

        # 将原始值标准化后用于预测
        original_values_2d = original_values.reshape(-1, 1)
        # 获取该特征在feature_cols中的位置
        feature_cols = [c for c in data.columns if c not in ['duration', 'event']]
        col_idx = feature_cols.index(col)

        # 创建一个临时的完整特征矩阵来进行标准化
        temp_features = original_data[feature_cols].copy()
        scaled_values = []

        for orig_val in original_values:
            temp_features.loc[temp_features.index[0], col] = orig_val
            scaled_feature = scaler.transform(temp_features.iloc[[0]])[0, col_idx]
            scaled_values.append(scaled_feature)

        values_for_plot = original_values  # 用于横坐标显示
        values_for_prediction = scaled_values  # 用于模型预测
    else:
        # 使用标准化后的数据范围
        values_for_plot = values_for_prediction = np.linspace(X[col].min(), X[col].max(), grid_points)

    pdp = []
    ice = []
    for i, v in enumerate(values_for_prediction):
        X_temp = X.copy()
        X_temp[col] = v
        # 预测风险分数（线性预测值，越大风险越高）
        pred = cph.predict_partial_hazard(X_temp)
        pdp.append(pred.mean())
        if plot_ice:
            ice.append(pred.values)

    # PDP
    plt.figure(figsize=(7, 5))
    plt.plot(values_for_plot, pdp, label='PDP', color='black', linewidth=2)
    # ICE
    if plot_ice:
        ice = np.array(ice).T  # shape: (n_samples, grid_points)
        for i in range(min(100, ice.shape[0])):  # 只画前100个样本，防止太密
            plt.plot(values_for_plot, ice[i], color='blue', alpha=0.2)
    # 画一条纵坐标为1的参考线
    plt.axhline(1, color='red', linestyle='--', label='Reference Line (Hazard = 1)')
    plt.xlabel(f'{col} (Original Scale)')
    plt.ylabel('Predicted Partial Hazard')
    plt.title(f'PDP/ICE for {col}')
    plt.legend()
    plt.tight_layout()
    plt.savefig(f'pdp_ice_{col}.png')
    plt.close()
    print(f"{col} 的PDP/ICE图已保存为 pdp_ice_{col}.png")


if __name__ == "__main__":
    # 1. 读取数据
    PATH = r'processed_equipment_data_all.csv'
    try:
        data = pd.read_csv(PATH)
        logging.info(f"成功读取数据: {PATH}, 形状: {data.shape}")
    except FileNotFoundError:
        logging.error(f"数据文件未找到: {PATH}")
        exit(1)
    # 2. 数据预处理（与v6一致）
    if SN_col in data.columns:
        data = data.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in data.columns]
    data = data.drop(columns=cols_to_drop)
    feature_cols = [col for col in data.columns if col not in [duration_col, event_col]]
    if not feature_cols:
        logging.error("无有效特征列，退出。")
        exit(1)

    # 保存原始数据（在标准化之前）
    original_data = data.copy()

    # 标准化特征
    scaler = StandardScaler()
    data[feature_cols] = scaler.fit_transform(data[feature_cols])
    # 3. 拟合Cox比例风险模型
    cph = CoxPHFitter()
    cph.fit(data[[duration_col, event_col] + feature_cols], duration_col=duration_col, event_col=event_col)
    logging.info("Cox比例风险模型拟合完成。")
    logging.info(f"模型C-index: {cph.concordance_index_:.4f}")
    # 4. Schoenfeld残差检验
    results = proportional_hazard_test(cph, data, time_transform='rank')
    print("\nSchoenfeld残差检验结果:")
    print(results.summary)
    # 5. 可视化诊断
    print("\n正在进行比例风险假设可视化诊断...")
    cph.check_assumptions(data, p_value_threshold=0.05, show_plots=True)
    # 6. 可选：保存诊断图（需手动保存或在check_assumptions中设置）
    # plt.savefig('schoenfeld_residuals_check.png')
    # # 7. 检查非线性：绘制马尔可夫残差与每个特征的散点图
    # martingale_resid = cph.compute_residuals(data, kind="martingale")
    # output_dir = "martingale_residuals_plots"
    # import os
    # os.makedirs(output_dir, exist_ok=True)

    # for col in feature_cols:
    #     x, y = data[col].align(martingale_resid.iloc[:, 0], join='inner')
    #     plt.figure(figsize=(6, 4))
    #     plt.scatter(x, y, alpha=0.5)
    #     plt.axhline(0, color='red', linestyle='--')
    #     plt.xlabel(col)
    #     plt.ylabel("Martingale Residuals")
    #     plt.title(f"Martingale Residuals vs {col}")
    #     plt.tight_layout()
    #     plt.savefig(f"{output_dir}/martingale_residuals_{col}.png")
    # #     plt.close()
    # logging.info(f"所有特征的马尔可夫残差散点图已保存到 {output_dir}/")
    # logging.info("分析完成。")


    # 假设你已经有cph, data, feature_cols
    non_proportional_cols = ['Z_CLOSED_LOOP_V280_L_Z', '100V_V280_H_Z', '100V_V700_H_Z','200V_V700_L_Y',
                             '420V_V280_L_Y', '420V_V700_H_Z']
    for col in non_proportional_cols:
        plot_pdp_ice(cph, data, col, original_data=original_data, scaler=scaler, grid_points=20, plot_ice=True)
