import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split

# 读取原始数据
input_path = './processed_equipment_data.csv'
df = pd.read_csv(input_path)

# 删除 SN_Common 列
df = df.drop(columns=['SN_Common'])

# 先将原始数据按event分布分为两半
sample_pool, holdout = train_test_split(df, test_size=0.3, random_state=42, stratify=df['event'])

# 标记采样池
sample_pool = sample_pool.copy()
sample_pool['sampled_pool'] = 1
holdout = holdout.copy()
holdout['sampled_pool'] = 0

# 需要生成的新样本数量
n_new = 3000

# measurement列（去掉duration和event）
measurement_cols = [col for col in df.columns if col not in ['duration', 'event']]

# stratified采样，保持event分布
event_counts = sample_pool['event'].value_counts(normalize=True)
n_new_0 = int(n_new * event_counts.get(0, 0))
n_new_1 = n_new - n_new_0
sampled_0 = sample_pool[sample_pool['event'] == 0].sample(n=n_new_0, replace=True, random_state=42)
sampled_1 = sample_pool[sample_pool['event'] == 1].sample(n=n_new_1, replace=True, random_state=42)
sampled = pd.concat([sampled_0, sampled_1], ignore_index=True).reset_index(drop=True)

# measurement特征加小扰动，保持协方差结构
cov = sample_pool[measurement_cols].cov().values
mean = np.zeros(len(measurement_cols))
noise = np.random.multivariate_normal(mean, cov * 0.01, size=n_new)
sampled[measurement_cols] = sampled[measurement_cols] + noise

# 添加generate列，原始数据为0，生成数据为1
sample_pool['generate'] = 0
holdout['generate'] = 0
sampled['generate'] = 1
sampled['sampled_pool'] = 1  # 生成数据也标记为采样池

# 合并数据
all_data = pd.concat([sample_pool, holdout, sampled], ignore_index=True)

# 保存到新CSV
output_path = './processed_equipment_data_augmented.csv'
all_data.to_csv(output_path, index=False)

print(f'已生成扩增数据，保存至 {output_path}') 