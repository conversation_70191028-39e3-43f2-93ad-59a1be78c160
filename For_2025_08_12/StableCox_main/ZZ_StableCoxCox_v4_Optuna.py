# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.metrics import brier_score, cumulative_dynamic_auc
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings
warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
import optuna
from SurvivalEVAL.Evaluator import LifelinesEvaluator

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols

def cross_validation_objective(trial, df_trainval, feature_cols, params, round_idx):
    """
    Stage 1: Multi-objective function for hyperparameter optimization using SurvivalEVAL
    Perform cross-validation on trainval, return multiple metrics for Optuna optimization
    Returns: (c_index, auc, -ibs, -mae) for maximization
    """
    # Optuna建议的超参数 - 调整搜索范围以避免convergence error
    if params.get('backend') in ["LogLogistic", "Weibull", "LogNormal"]:
        # AFT模型需要更保守的penalizer范围
        penalizer = trial.suggest_float('penalizer', 0.01, 0.5, log=True)
        penalizer2 = trial.suggest_float('penalizer2', 0.01, 0.5, log=True)
    else:
        # Cox模型可以使用更宽的范围
        penalizer = trial.suggest_float('penalizer', 0.0001, 2, log=True)
        penalizer2 = trial.suggest_float('penalizer2', 0.0001, 2, log=True)

    topN = trial.suggest_int('topN', 5, 20)  # 缩小范围

    # 新增：当backend为Weighted_cox时，优化SRDO参数
    if params.get('backend') == 'Weighted_cox' and params.get('reweighting') == 'SRDO':
        # SRDO隐藏层大小优化
        n_layers = trial.suggest_int('n_layers', 1, 3)
        srdo_hidden_layers = ()
        for i in range(n_layers):
            dim = trial.suggest_int(f'hidden_dim_{i + 1}', 2, 128, step=2)
            srdo_hidden_layers += (dim,)

        # 权重裁剪范围优化
        w_clip_min = trial.suggest_float('w_clip_min', 0.001, 0.01, log=True)
        w_clip_max = trial.suggest_float('w_clip_max', 2.0, 5.0)

    else:
        # 使用默认值
        srdo_hidden_layers = (64, 108)
        # AFT模型使用更温和的权重裁剪范围
        if params.get('backend') in ["LogLogistic", "Weibull", "LogNormal"]:
            w_clip_min = 0.1  # 更温和的下限
            w_clip_max = 3.0  # 更温和的上限
        else:
            w_clip_min = 0.004482532833926257
            w_clip_max = 2.6

    # 更新参数
    trial_params = params.copy()
    trial_params.update({
        'penalizer': penalizer,
        'penalizer2': penalizer2,
        'topN': topN,
        'srdo_hidden_layers': srdo_hidden_layers,
        'w_clip_min': w_clip_min,
        'w_clip_max': w_clip_max
    })

    from sklearn.model_selection import StratifiedKFold
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 减少交叉验证折数以减少计算
    n_splits_reduced = trial_params.get('n_splits', 10)
    skf = StratifiedKFold(
        n_splits=n_splits_reduced,
        shuffle=True,
        # random_state=params['seed'] + round_idx
    )
    X_trainval, y_trainval = df_trainval[feature_cols], df_trainval[event_col]

    # 存储所有指标
    c_indices_val = []
    aucs_val = []
    ibs_val = []
    mae_val = []

    for fold, (train_idx, val_idx) in enumerate(skf.split(X_trainval, y_trainval)):
        try:
            # 当前折叠的训练集和验证集
            train_data_fold = df_trainval.iloc[train_idx].copy()
            val_data_fold = df_trainval.iloc[val_idx].copy()

            # 计算权重（优化：减少SRDO的迭代次数）
            X_train_fold_np = train_data_fold[feature_cols].values
            n_fold, p_fold = X_train_fold_np.shape
            W = np.ones((n_fold, 1))

            if trial_params.get('reweighting', 'DWR') == "DWR":
                W = DWR(X_train_fold_np, logger=logging, device=device)
            elif trial_params.get('reweighting', 'SRDO') == "SRDO":
                p_s_fold = p_fold // 2
                # 优化：减少SRDO的迭代次数以加速
                reduced_iters = trial_params.get('iters_balance', 2500)
                # 使用优化的hidden_layer_sizes
                W = SRDO(X_train_fold_np, p_s_fold,
                        hidden_layer_sizes=trial_params['srdo_hidden_layers'],
                        decorrelation_type=trial_params.get('decorrelation_type', 'global'),
                        max_iter=reduced_iters)

            # 权重处理 - 使用优化的裁剪范围（更温和的处理）
            if trial_params.get('reweighting', 'none') != 'none':
                mean_value = max(np.mean(W), 1e-8)
                W = W * (1 / mean_value)
                W = np.clip(W, trial_params['w_clip_min'], trial_params['w_clip_max'])
            else:
                # 如果不使用reweighting，保持权重为1
                W = np.ones((n_fold, 1))

            # 训练模型并进行特征选择
            if trial_params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
                try:
                    model_func = get_algorithm_class(trial_params.get('backend', 'Weighted_cox'))
                    model = model_func(
                        X=train_data_fold[feature_cols + [duration_col, event_col]],
                        duration_col=duration_col,
                        event_col=event_col,
                        W=W,
                        pen=penalizer,
                        **trial_params
                    )
                except Exception as model_error:
                    logging.warning(f"Model training failed in fold {fold}: {str(model_error)}")
                    # 跳过这个fold，但不终止整个试验
                    continue

                # 特征选择
                summary = model.summary
                if topN > 0:
                    sorted_indices = summary['p'].sort_values().head(topN).index
                else:
                    if (summary['p'] < 0.1).any():
                        sorted_indices = summary[summary['p'] < 0.1].index
                    else:
                        sorted_indices = summary.index[:5]  # 至少选择5个特征

                selected_features_fold = list(sorted_indices)
                # 在 cross_validation_objective 函数中修改
                if trial_params.get('backend', 'Weighted_cox') in ["LogLogistic", "Weibull", "LogNormal"]:
                    # 参数化模型：过滤掉 Intercept，只保留特征相关的项
                    feature_summary = summary[~summary.index.get_level_values(1).str.contains('Intercept', na=False)]
                    if topN > 0:
                        sorted_indices = feature_summary['p'].sort_values().head(topN).index
                    else:
                        if (feature_summary['p'] < 0.1).any():
                            sorted_indices = feature_summary[feature_summary['p'] < 0.1].index
                        else:
                            sorted_indices = feature_summary.index[:5]

                    # 提取特征名（tuple 的第二个元素）
                    selected_features_fold = [col[1] for col in sorted_indices]
                    selected_features_fold = list(set(selected_features_fold))

                # 用选择的特征重新训练最终模型
                val_data_fold["Weights"] = np.ones((val_data_fold.shape[0],))
                val_data_for_score = val_data_fold[selected_features_fold + [duration_col, event_col, "Weights"]]

                # 训练最终模型并使用SurvivalEVAL进行综合评估
                try:
                    if trial_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
                        final_model = CoxPHFitter(penalizer=penalizer2)
                        final_model.fit(train_data_fold[selected_features_fold + [duration_col, event_col]],
                                      duration_col=duration_col, event_col=event_col)

                        # 获取生存曲线预测
                        survival_curves = final_model.predict_survival_function(val_data_for_score)

                    else:
                        # AFT模型 (LogLogistic, Weibull, LogNormal) - 添加更好的错误处理
                        try:
                            model_func = get_algorithm_class(trial_params.get('backend', 'LogNormal'))
                            final_model = model_func(
                                X=train_data_fold[selected_features_fold + [duration_col, event_col]],
                                duration_col=duration_col,
                                event_col=event_col,
                                W=W,
                                pen=penalizer2,
                            )

                            # AFT模型的生存曲线预测
                            survival_curves = final_model.predict_survival_function(val_data_for_score)
                        except Exception as aft_error:
                            logging.warning(f"AFT model failed in fold {fold}: {str(aft_error)}")
                            # 回退到基础评估
                            c_indices_val.append(0.5)
                            aucs_val.append(0.5)
                            ibs_val.append(1.0)
                            mae_val.append(1000.0)
                            continue

                    # 准备数据
                    train_event_times = train_data_fold[duration_col].values
                    train_event_indicators = train_data_fold[event_col].values
                    val_event_times = val_data_fold[duration_col].values
                    val_event_indicators = val_data_fold[event_col].values

                    # 创建SurvivalEVAL评估器
                    evl = LifelinesEvaluator(survival_curves, val_event_times, val_event_indicators,
                                           train_event_times, train_event_indicators)

                    # 计算C-index
                    val_c_index, _, _ = evl.concordance()
                    c_indices_val.append(val_c_index)

                    # 计算AUC (使用中位数时间点)
                    try:
                        target_time = np.median(val_event_times[val_event_indicators == 1])
                        if not np.isnan(target_time) and target_time > 0:
                            val_auc = evl.auc(target_time)
                            aucs_val.append(val_auc)
                        else:
                            aucs_val.append(0.5)  # 默认值
                    except:
                        aucs_val.append(0.5)

                    # 计算IBS
                    try:
                        max_time = int(np.max(val_event_times))
                        num_points = min(max_time + 1, 50)  # 限制计算点数
                        val_ibs = evl.integrated_brier_score(num_points=num_points, draw_figure=False)
                        ibs_val.append(val_ibs)
                    except:
                        ibs_val.append(1.0)  # 默认高值

                    # 计算MAE (使用Hinge方法)
                    try:
                        val_mae = evl.mae(method="Hinge")
                        mae_val.append(val_mae)
                    except:
                        mae_val.append(1000.0)  # 默认高值

                except Exception as eval_error:
                    logging.warning(f"SurvivalEVAL evaluation failed in fold {fold}: {str(eval_error)}")
                    # 使用基础C-index计算作为后备
                    if trial_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
                        final_model = CoxPHFitter(penalizer=penalizer2)
                        final_model.fit(train_data_fold[selected_features_fold + [duration_col, event_col]],
                                      duration_col=duration_col, event_col=event_col)
                        val_c_index = concordance_index(val_data_for_score[duration_col],
                                                       -final_model.predict_partial_hazard(val_data_for_score),
                                                       val_data_for_score[event_col])
                    else:
                        model_func = get_algorithm_class(trial_params.get('backend', 'LogNormal'))
                        final_model = model_func(
                            X=train_data_fold[selected_features_fold + [duration_col, event_col]],
                            duration_col=duration_col,
                            event_col=event_col,
                            W=W,
                            pen=penalizer2,
                        )
                        val_c_index = final_model.score(val_data_for_score, scoring_method='concordance_index')

                    c_indices_val.append(val_c_index)
                    aucs_val.append(0.5)
                    ibs_val.append(1.0)
                    mae_val.append(1000.0)

        except Exception as e:
            # 如果某个fold失败，记录但继续
            logging.warning(f"Fold {fold} failed: {str(e)}")
            continue

    # 如果没有成功的fold，返回很低的分数
    if not c_indices_val:
        return (0.5, 0.5, -1.0, -1000.0)

    # 返回多个指标的平均值供Optuna多目标优化
    # 注意：IBS和MAE取负值，因为Optuna要最大化，而我们要最小化这些指标
    mean_c_index = np.mean(c_indices_val)
    mean_auc = np.mean(aucs_val) if aucs_val else 0.5
    mean_ibs = np.mean(ibs_val) if ibs_val else 1.0
    mean_mae = np.mean(mae_val) if mae_val else 1000.0

    return (mean_c_index, mean_auc, -mean_ibs, -mean_mae)


def train_final_model_with_best_params(df_trainval, df_test, feature_cols, best_params):
    """
    Stage 2: Train the final model on the entire trainval with the best hyperparameters,
    and then evaluate on the test set using SurvivalEVAL
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 首先在整个trainval上训练标准化器（用所有特征）
    df_trainval_scaled = df_trainval.copy()

    # 计算权重 - 使用优化的参数
    X_trainval_np = df_trainval_scaled[feature_cols].values
    n_trainval, p_trainval = X_trainval_np.shape
    W = np.ones((n_trainval, 1))

    if best_params.get('reweighting', 'SRDO') == "DWR":
        W = DWR(X_trainval_np, logger=logging, device=device)
    elif best_params.get('reweighting', 'SRDO') == "SRDO":
        p_s_trainval = p_trainval // 2
        # 使用优化后的SRDO参数 - 修复bug：正确重构hidden_layer_sizes
        if 'n_layers' in best_params:
            # 从Optuna优化结果中重构hidden_layer_sizes
            n_layers = best_params['n_layers']
            srdo_hidden_layers = ()
            for i in range(n_layers):
                dim = best_params.get(f'hidden_dim_{i + 1}')
                if dim is not None:
                    srdo_hidden_layers += (dim,)

            # 如果重构失败，使用默认值
            if len(srdo_hidden_layers) == 0:
                srdo_hidden_layers = (64, 108)
        else:
            # 如果没有优化SRDO参数，使用默认值
            srdo_hidden_layers = best_params.get('srdo_hidden_layers', (64, 108))

        W = SRDO(X_trainval_np, p_s_trainval,
                hidden_layer_sizes=srdo_hidden_layers,
                decorrelation_type=best_params.get('decorrelation_type', 'global'),
                max_iter=best_params.get('iters_balance', 2500))
        logging.info(f"Using optimized SRDO parameters: hidden_layers={srdo_hidden_layers}")

    # 权重处理 - 使用优化的裁剪范围
    mean_value = max(np.mean(W), 1e-8)
    W = W * (1 / mean_value)
    w_clip_min = best_params.get('w_clip_min', 0.004482532833926257)
    w_clip_max = best_params.get('w_clip_max', 2.6)
    W = np.clip(W, w_clip_min, w_clip_max)
    logging.info(f"Using optimized weight clipping: min={w_clip_min:.6f}, max={w_clip_max:.2f}")

    # 用最佳参数训练模型并进行特征选择
    if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
        model_func = get_algorithm_class(best_params.get('backend', 'Weighted_cox'))
        model = model_func(
            X=df_trainval_scaled[feature_cols + [duration_col, event_col]],
            duration_col=duration_col,
            event_col=event_col,
            W=W,
            pen=best_params['penalizer'],
            **best_params
        )

        # 特征选择
        summary = model.summary

        if best_params['topN'] > 0:
            sorted_indices = summary['p'].sort_values().head(best_params['topN']).index
        else:
            if (summary['p'] < 0.1).any():
                sorted_indices = summary[summary['p'] < 0.1].index
            else:
                sorted_indices = summary.index[:5]

        selected_features = list(sorted_indices)
        # 在 cross_validation_objective 函数中修改
        if best_params.get('backend', 'Weighted_cox') in ["LogLogistic", "Weibull", "LogNormal"]:
            # 参数化模型：过滤掉 Intercept，只保留特征相关的项
            feature_summary = summary[~summary.index.get_level_values(1).str.contains('Intercept', na=False)]
            if best_params['topN'] > 0:
                sorted_indices = feature_summary['p'].sort_values().head(best_params['topN']).index
            else:
                if (feature_summary['p'] < 0.1).any():
                    sorted_indices = feature_summary[feature_summary['p'] < 0.1].index
                else:
                    sorted_indices = feature_summary.index[:5]

            # 提取特征名（tuple 的第二个元素）
            selected_features = [col[1] for col in sorted_indices]
            selected_features = list(set(selected_features))

        logging.info(f"Final model selected {len(selected_features)} features: {selected_features}")

        # 重新标准化训练验证集（只用选择的特征）
        df_trainval_final = df_trainval.copy()

        # 用选择的特征训练最终模型
        if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
            final_model = CoxPHFitter(penalizer=best_params['penalizer2'])
            final_model.fit(df_trainval_final[selected_features + [duration_col, event_col]],
                          duration_col=duration_col, event_col=event_col)
        else:
            model_func = get_algorithm_class(best_params.get('backend', 'LogNormal'))
            final_model = model_func(
                X=df_trainval_final[selected_features + [duration_col, event_col]],
                duration_col=duration_col,
                event_col=event_col,
                W=W,
                pen=best_params['penalizer2'],
            )

        # 准备测试集（现在特征名称匹配了）
        df_test_scaled = df_test.copy()
        df_test_scaled["Weights"] = np.ones((df_test_scaled.shape[0],))
        test_data_for_score = df_test_scaled[selected_features + [duration_col, event_col, "Weights"]]

        # 使用SurvivalEVAL进行综合评估
        try:
            # 获取生存曲线预测
            survival_curves = final_model.predict_survival_function(test_data_for_score)

            # 准备数据
            train_event_times = df_trainval_final[duration_col].values
            train_event_indicators = df_trainval_final[event_col].values
            test_event_times = df_test_scaled[duration_col].values
            test_event_indicators = df_test_scaled[event_col].values

            # 创建SurvivalEVAL评估器
            evl = LifelinesEvaluator(survival_curves, test_event_times, test_event_indicators,
                                   train_event_times, train_event_indicators)

            # 计算所有指标
            final_test_c_index, _, _ = evl.concordance()


            c_index_check = final_model.score(test_data_for_score, scoring_method='concordance_index')
            if final_test_c_index != c_index_check:
                logging.warning(f"SurvivalEVAL C-index: {final_test_c_index:.4f} != Basic C-index: {c_index_check:.4f}")
                final_test_c_index = c_index_check



            # 计算AUC (使用中位数时间点)
            try:
                target_time = np.median(test_event_times[test_event_indicators == 1])
                if not np.isnan(target_time) and target_time > 0:
                    final_test_auc = evl.auc(target_time)
                else:
                    final_test_auc = 0.5
            except:
                final_test_auc = 0.5

            # 计算IBS
            try:
                max_time = int(np.max(test_event_times))
                num_points = min(max_time + 1, 50)  # 限制计算点数
                final_test_ibs = evl.integrated_brier_score(num_points=num_points, draw_figure=False)
            except:
                final_test_ibs = 1.0

            # 计算MAE (使用Hinge方法)
            try:
                final_test_mae = evl.mae(method="Hinge")
            except:
                final_test_mae = 1000.0

            return {
                'final_test_c_index': final_test_c_index,
                'final_test_time_dependent_auc': final_test_auc,
                'final_test_integrated_brier_score': final_test_ibs,
                'final_test_duration_rmse': final_test_mae  # 使用MAE替代RMSE
            }, selected_features, final_model

        except Exception as eval_error:
            logging.warning(f"SurvivalEVAL evaluation failed: {str(eval_error)}")
            # 使用基础C-index计算作为后备
            if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
                final_test_c_index = concordance_index(test_data_for_score[duration_col],
                                                      -final_model.predict_partial_hazard(test_data_for_score),
                                                      test_data_for_score[event_col])
            else:
                final_test_c_index = final_model.score(test_data_for_score, scoring_method='concordance_index')

            return {
                'final_test_c_index': final_test_c_index,
                'final_test_time_dependent_auc': 0.5,
                'final_test_integrated_brier_score': 1.0,
                'final_test_duration_rmse': 1000.0
            }, selected_features, final_model

    return None, [], None


def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting Round {round_idx} ---")

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Multi-objective hyperparameter optimization (using Optuna)
    # ===============================
    logging.info("=== Stage 1: Starting multi-objective hyperparameter optimization ===")

    n_trials_reduced = min(200, n_trials if n_trials else 25)

    logging.info(f"Use {n_trials_reduced} Optuna trials for multi-objective optimization")

    # 创建多目标优化study
    study = optuna.create_study(
        directions=['maximize', 'maximize', 'maximize', 'maximize'],  # C-index, AUC, -IBS, -MAE
        sampler=optuna.samplers.NSGAIISampler()
    )

    # 定义优化目标函数
    def objective_wrapper(trial):
        return cross_validation_objective(trial, df_trainval, feature_cols, params, round_idx)

    # 执行超参数优化
    study.optimize(objective_wrapper, n_trials=n_trials_reduced)

    # 获取最佳参数 (选择第一个目标C-index最好的试验)
    best_trial = max(study.trials, key=lambda t: t.values[0] if t.values else -1)
    best_params = params.copy()
    best_params.update(best_trial.params)
    best_cv_scores = best_trial.values

    logging.info(f"Multi-objective optimization completed!")
    logging.info(f"Best validation scores - C-index: {best_cv_scores[0]:.4f}, AUC: {best_cv_scores[1]:.4f}, "
                f"IBS: {-best_cv_scores[2]:.4f}, MAE: {-best_cv_scores[3]:.4f}")
    logging.info(f"Best parameters: {best_trial.params}")

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final model training and evaluation ===")

    final_test_metrics, selected_features, final_model = train_final_model_with_best_params(
        df_trainval, df_test, feature_cols, best_params
    )

    if final_test_metrics is not None:
        logging.info(f"Final test C-index: {final_test_metrics['final_test_c_index']:.4f}")
        logging.info(f"Final test AUC: {final_test_metrics['final_test_time_dependent_auc']:.4f}")
        logging.info(f"Final test IBS: {final_test_metrics['final_test_integrated_brier_score']:.4f}")
        logging.info(f"Final test MAE: {final_test_metrics['final_test_duration_rmse']:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_c_index": best_cv_scores[0],
            "best_cv_auc": best_cv_scores[1],
            "best_cv_ibs": -best_cv_scores[2],
            "best_cv_mae": -best_cv_scores[3],
            "final_test_c_index": final_test_metrics['final_test_c_index'],
            "final_test_time_dependent_auc": final_test_metrics['final_test_time_dependent_auc'],
            "final_test_integrated_brier_score": final_test_metrics['final_test_integrated_brier_score'],
            "final_test_duration_rmse": final_test_metrics['final_test_duration_rmse'],
            "best_params": best_params,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final model training failed")
        return {}

def main(params):
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)
    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"StableCoxCox_Optuna_{timestamp}.log")

        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 获取根logger并添加处理器
        logger = logging.getLogger()
        logger.handlers.clear()  # 清除现有处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info(f"StableCox Optuna Optimization Started")
        logging.info(f"Log file: {log_filename}")
        logging.info(f"MPI Configuration: {size} processes")
        logging.info(f"Parameters: {params}")
        logging.info(f'Model using in this file is: {params["backend"]}')

    PATH = params['PATH']
    begin_time = time.time()
    setup_seed(params['seed'])

    df_trainval, df_test, feature_cols = data_processing(PATH)

    all_indices = list(range(params['times']))
    my_indices = [i for i in all_indices if i % size == rank]

    my_results_list = dd(list)

    for i in my_indices:
        if rank == 0:
            logging.info(f"Starting Round {i + 1}/{params['times']}")
        # df_trainval, df_test, feature_cols = data_processing(PATH, i)

        results_per_run = objective(params['n_trials'], params, i, logging, PATH, df_trainval, df_test, feature_cols)
        if results_per_run:
            for k, v in results_per_run.items():
                my_results_list[k].append(v)

    all_results_list = comm.gather(my_results_list, root=0)

    if rank == 0:
        final_results_list = dd(list)
        for res in all_results_list:
            for k, v in res.items():
                final_results_list[k].extend(v)

        # 保存最终结果
        if params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
            if final_results_list:
                logging.info("=" * 70)
                logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
                logging.info("=" * 70)

                for metric_name, value_list in final_results_list.items():
                    if metric_name == 'best_params':
                        continue

                    numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                    if numeric_values:
                        final_mean = np.mean(numeric_values)
                        final_std = np.std(numeric_values)
                        cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                        logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

                if 'final_test_c_index' in final_results_list:
                    final_test_scores = final_results_list['final_test_c_index']
                    final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                    if final_test_scores:
                        logging.info("-" * 50)
                        logging.info("TEST SET PERFORMANCE ANALYSIS")
                        logging.info("-" * 50)
                        logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                        mean_score = np.mean(final_test_scores)
                        std_score = np.std(final_test_scores)
                        cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                        logging.info("Final Test Set Performance Summary:")
                        logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                        logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                        logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                        logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                        logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                        # 判断方差是否过大
                        if cv_score > 10:
                            logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                            logging.warning("1. Increasing test set size")
                            logging.warning("2. Using more Optuna trials")
                            logging.warning("3. Fixing data splits across runs")
                            logging.warning("4. Ensemble methods")
                        logging.info("-" * 50)

                        final_test_auc_scores = final_results_list['final_test_time_dependent_auc']
                        final_test_auc_scores = [s for s in final_test_auc_scores if isinstance(s, (int, float, np.number))]
                        final_test_ibs = final_results_list['final_test_integrated_brier_score']
                        final_test_ibs = [s for s in final_test_ibs if isinstance(s, (int, float, np.number))]
                        final_test_rmse = final_results_list['final_test_duration_rmse']
                        final_test_rmse = [s for s in final_test_rmse if isinstance(s, (int, float, np.number))]
                        logging.info(
                            f"Final Test AUC: Mean={np.mean(final_test_auc_scores):.4f}, Std={np.std(final_test_auc_scores):.4f}")
                        logging.info(
                            f"Final Test IBS: Mean={np.mean(final_test_ibs):.4f}, Std={np.std(final_test_ibs):.4f}")
                        logging.info(
                            f"Final Test RMSE: Mean={np.mean(final_test_rmse):.4f}, Std={np.std(final_test_rmse):.4f}")

                        logging.info("=" * 50)

                    else:
                        logging.info("No valid test scores to analyze")
            else:
                logging.info("No results were generated from the runs.")
        logging.info(f"Total time: {time.time() - begin_time:.2f} seconds")

if __name__ == "__main__":
    import time
    from datetime import datetime

    params = {
        'PATH': r'processed_equipment_data_all.csv',
        'seed': 9, # 10 最好
        'n_splits': 10,
        'reweighting': 'none',
        'decorrelation_type': 'global', #global, none
        'iters_balance': 2500,
        'backend': 'LogNormal', #["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]
        'paradigm': 'regr',
        'penalizer': 0.03,
        'penalizer2': 0.03,
        'topN': 5,
        'times': 5,
        'result_dir': 'results',
        'n_trials': 10,
        'test_size': 0.1,
    }

    main(params)
#基本运行： mpiexec -n 10 python .\ZZ_StableCoxCox_v2_Optuna.py
#后台运行 nohup mpiexec -n 4 python ZZ_StableCoxCox_v2_Optuna.py &
#实时查看日志 tail -f logs/StableCox_Optuna_*.log
