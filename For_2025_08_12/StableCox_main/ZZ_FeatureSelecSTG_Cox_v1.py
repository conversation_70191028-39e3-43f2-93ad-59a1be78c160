# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import <PERSON>P<PERSON>itter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings

warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import StratifiedKFold
import optuna
from optuna.samplers import TPESampler
import io
import contextlib
import re
from SurvivalEVAL.Evaluator import LifelinesEvaluator

# STG相关导入
try:
    from stg import STG
    import stg.utils as stg_utils

    STG_AVAILABLE = True
except ImportError:
    logging.warning("STG package not found. Please install stg package to use this functionality.")
    STG_AVAILABLE = False

# 兼容性修复
import collections
import collections.abc

collections.Sequence = collections.abc.Sequence
collections.Mapping = collections.abc.Mapping
collections.Set = collections.abc.Set

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

# device = 'cuda' if torch.cuda.is_available() else 'cpu'
device = 'cpu'

class STGEarlyStopping:
    """
    Early stopping for STG model training - 简化版本，不依赖state_dict
    """

    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0

    def __call__(self, score):
        if self.best_score is None:
            self.best_score = score
            return False
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = score
            self.counter = 0
        return False


class STGNativeEarlyStopping:
    """
    Early stopping hook for STG's fit method.
    Performs validation internally to decide when to stop.
    """

    def __init__(self, stg_instance, val_data, patience=20, min_delta=0.001):
        self.stg_instance = stg_instance
        self.val_data = val_data
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None

    def __call__(self, model):  # model is the internal torch model, not the STG wrapper
        # Use the stg_instance to call public methods
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            # Use the validation data provided during initialization
            self.stg_instance.evaluate(self.val_data['X'], {'E': self.val_data['E'], 'T': self.val_data['T']})
        output = f.getvalue()

        match = re.search(r"test_CI=([0-9.]+)", output)
        if not match:
            return False  # Continue if score not found

        score = float(match.group(1))

        if self.best_score is None:
            self.best_score = score
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                # logging.info(f"Early stopping triggered. Best score: {self.best_score:.4f}")
                return True  # Stop
        else:
            self.best_score = score
            self.counter = 0

        return False  # Continue


def stg_survival_function_prediction(estimator, X, time_points=None):
    """
    Convert STG predictions to survival function format for SurvivalEVAL
    由于STG模型主要设计用于Cox回归的C-index优化，没有直接的风险预测方法，
    我们需要通过其内部结构来构造合理的生存函数
    """
    try:
        # 确保输入数据格式正确
        if isinstance(X, pd.DataFrame):
            X_np = X.values.astype(np.float32)
        else:
            X_np = X.astype(np.float32)

        # 如果没有指定时间点，创建合理的时间范围
        if time_points is None:
            time_points = np.linspace(0.1, 100, 50)

        # STG模型的风险预测策略
        risk_scores = None

        try:
            # 方法1: 尝试使用STG内部模型直接预测
            if hasattr(estimator, 'model') and estimator.model is not None:
                X_tensor = torch.FloatTensor(X_np).to(estimator.device)
                estimator.model.eval()
                with torch.no_grad():
                    # STG的前向传播通常返回logits
                    outputs = estimator.model(X_tensor)

                    # 处理不同的输出格式
                    if isinstance(outputs, tuple):
                        # 如果是tuple，通常第一个是主要输出
                        risk_logits = outputs[0]
                    else:
                        risk_logits = outputs

                    # 转换为numpy并确保形状正确
                    if hasattr(risk_logits, 'detach'):
                        risk_scores = risk_logits.detach().cpu().numpy()
                    else:
                        risk_scores = risk_logits

                    risk_scores = np.array(risk_scores).flatten()

                    # 确保长度匹配
                    if len(risk_scores) != X_np.shape[0]:
                        logging.warning(f"Risk scores length mismatch, reshaping from {len(risk_scores)} to {X_np.shape[0]}")
                        if len(risk_scores) > X_np.shape[0]:
                            risk_scores = risk_scores[:X_np.shape[0]]
                        else:
                            # 如果长度不够，重复或插值
                            risk_scores = np.resize(risk_scores, X_np.shape[0])

                    logging.info(f"Successfully extracted risk scores from STG model: shape={risk_scores.shape}, range=[{risk_scores.min():.4f}, {risk_scores.max():.4f}]")

        except Exception as e:
            logging.warning(f"Failed to extract risk scores from STG model: {str(e)}")
            risk_scores = None

        # 方法2: 如果直接预测失败，使用基于特征的简单风险模型
        if risk_scores is None:
            try:
                # 获取特征重要性
                feature_gates = None
                try:
                    feature_gates = estimator.get_gates(mode='prob')
                    if hasattr(feature_gates, 'detach'):
                        feature_gates = feature_gates.detach().cpu().numpy()
                    feature_gates = np.array(feature_gates).flatten()
                except:
                    try:
                        feature_gates = estimator.get_gates()
                        if hasattr(feature_gates, 'detach'):
                            feature_gates = feature_gates.detach().cpu().numpy()
                        feature_gates = np.array(feature_gates).flatten()
                    except:
                        feature_gates = None

                if feature_gates is not None and len(feature_gates) == X_np.shape[1]:
                    # 使用特征重要性加权计算风险分数
                    # 归一化特征重要性
                    feature_weights = feature_gates / (np.sum(feature_gates) + 1e-8)

                    # 计算加权风险分数
                    risk_scores = np.sum(X_np * feature_weights, axis=1)

                    # 添加一些随机性以避免所有样本风险相同
                    risk_scores += np.random.normal(0, 0.1, size=risk_scores.shape)

                    logging.info(f"Used feature gates to compute risk scores: shape={risk_scores.shape}")
                else:
                    # 最后的fallback：基于特征均值的简单风险分数
                    risk_scores = np.mean(X_np, axis=1) + np.random.normal(0, 0.1, size=X_np.shape[0])
                    logging.warning("Used feature mean as fallback risk scores")

            except Exception as e:
                logging.warning(f"Feature-based risk calculation failed: {str(e)}")
                # 最终fallback
                risk_scores = np.random.normal(0, 1, size=X_np.shape[0])
                logging.warning("Used random risk scores as final fallback")

        # 确保risk_scores有效
        if risk_scores is None:
            risk_scores = np.random.normal(0, 1, size=X_np.shape[0])
            logging.warning("Risk scores is None, using random values")

        risk_scores = np.array(risk_scores).flatten()

        # 将风险分数转换为生存函数
        survival_probs = []

        # 标准化风险分数为正的hazard rates
        # 确保hazard rates为正值
        risk_scores_centered = risk_scores - np.min(risk_scores) + 0.01

        # 使用相对风险的概念：高风险分数 -> 高hazard rate -> 低生存概率
        # 使用中位生存时间来校准hazard rates
        median_time = np.median(time_points)

        # 将风险分数映射到合理的hazard rate范围
        # 使用sigmoid变换确保hazard rates在合理范围内
        normalized_risks = (risk_scores_centered - np.mean(risk_scores_centered)) / (np.std(risk_scores_centered) + 1e-8)
        hazard_rates = 0.01 + 0.1 * (1 / (1 + np.exp(-normalized_risks)))  # hazard rates在[0.01, 0.11]范围内

        for hazard_rate in hazard_rates:
            # 生成生存曲线: S(t) = exp(-hazard_rate * t)
            survival_curve = np.exp(-hazard_rate * time_points)
            # 确保生存概率在有效范围内
            survival_curve = np.clip(survival_curve, 1e-10, 1.0)
            survival_probs.append(survival_curve)

        # 创建类似lifelines格式的DataFrame
        survival_df = pd.DataFrame(
            np.array(survival_probs).T,
            index=time_points,
            columns=range(len(risk_scores))
        )

        logging.info(f"Generated survival functions: {survival_df.shape}, risk_scores range=[{risk_scores.min():.4f}, {risk_scores.max():.4f}]")
        return survival_df

    except Exception as e:
        logging.error(f"Error in STG survival function prediction: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        # 返回默认生存曲线
        if time_points is None:
            time_points = np.linspace(0.1, 100, 50)

        # 确定样本数量
        try:
            n_samples = X.shape[0] if hasattr(X, 'shape') else len(X)
        except:
            n_samples = 1

        # 创建默认的指数衰减生存曲线，每个样本略有不同
        default_survival_probs = []
        for i in range(n_samples):
            # 为每个样本创建略有不同的衰减率
            decay_rate = 0.01 + 0.005 * (i % 10) / 10  # 在0.01到0.015之间变化
            survival_curve = np.exp(-decay_rate * time_points)
            default_survival_probs.append(survival_curve)

        survival_df = pd.DataFrame(
            np.array(default_survival_probs).T,
            index=time_points,
            columns=range(n_samples)
        )

        logging.warning(f"Returned default survival curves: {survival_df.shape}")
        return survival_df
def evaluate_stg_with_survivaleval(estimator, X_train, y_train, X_val, y_val):
    """
    Evaluate STG model using SurvivalEVAL metrics
    Returns: (c_index, auc, ibs, mae)
    """
    try:
        # 转换数据格式
        train_times = y_train['t'] if isinstance(y_train, dict) else y_train['duration']
        train_events = y_train['e'] if isinstance(y_train, dict) else y_train['event']
        val_times = y_val['t'] if isinstance(y_val, dict) else y_val['duration']
        val_events = y_val['e'] if isinstance(y_val, dict) else y_val['event']

        # 获取生存函数预测
        survival_curves = stg_survival_function_prediction(estimator, X_val)

        # 创建SurvivalEVAL评估器
        evl = LifelinesEvaluator(survival_curves, val_times, val_events,
                               train_times, train_events)

        # 计算指标
        c_index, _, _ = evl.concordance()

        # 计算IBS
        try:
            max_time = int(np.max(val_times))
            num_points = min(max_time + 1, 50)
            ibs = evl.integrated_brier_score(num_points=num_points, draw_figure=False)
        except:
            ibs = 1.0

        # 计算MAE
        try:
            mae = evl.mae(method="Hinge")
            if np.isnan(mae) or np.isinf(mae):
                logging.warning(f"MAE is NaN or Inf: {mae}")
                mae = 1000.0
        except:
            mae = 1000.0

        logging.info(f"SurvivalEVAL metrics - C-index: {c_index:.4f}, IBS: {ibs:.4f}, MAE: {mae:.4f}")
        return c_index, ibs, mae

    except Exception as e:
        logging.warning(f"SurvivalEVAL evaluation failed: {str(e)}")
        return 0.5, 1.0, 1000.0


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols


def cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx):
    """
    Stage 1: STG hyperparameter optimization using Optuna with cross-validation
    严格按照v5_STG和Cox-example的数据处理方式
    """
    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return 0.5

    # Optuna超参数建议
    n_layers = trial.suggest_int('n_layers', 1, 4)
    hidden_dims = []
    for i in range(n_layers):
        dim = trial.suggest_int(f'hidden_dim_{i + 1}', 1, 64)
        hidden_dims.append(dim)

    activation = trial.suggest_categorical('activation', ['relu', 'selu', 'tanh', 'sigmoid'])
    optimizer = trial.suggest_categorical('optimizer', ['Adam', 'SGD', 'Adagrad'])
    learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-1, log=True)
    sigma = trial.suggest_float('sigma', 0.1, 2.0, log=True)
    lam = trial.suggest_float('lam', 1e-3, 10, log=True)
    if params.get('backend') in ["LogLogistic", "Weibull", "LogNormal"]:
        # AFT模型需要更保守的penalizer范围
        penalizer = trial.suggest_float('penalizer', 0.01, 0.5, log=True)
    else:
        # Cox模型可以使用更宽的范围
        penalizer = trial.suggest_float('penalizer', 0.0001, 2, log=True)
    # 早停参数
    patience = trial.suggest_int('patience', 20, 50)

    logging.info(f"=== Starting STG cross-validation optimization ===")
    logging.info(f"Hidden dims: {hidden_dims}, activation: {activation}, optimizer: {optimizer}")

    # 交叉验证设置 - 确保足够的样本
    cv_splits = params.get('n_splits', 5)

    # 检查数据集大小，如果太小则减少fold数
    min_samples_per_fold = 20  # 每个fold最少需要20个样本
    if len(df_trainval) < cv_splits * min_samples_per_fold:
        cv_splits = max(2, len(df_trainval) // min_samples_per_fold)
        logging.warning(f"Reducing CV folds to {cv_splits} due to small dataset size")

    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
        # random_state=params['seed'] + round_idx
    )

    cv_scores = []
    selected_features_list = []

    for fold_idx, (train_idx, val_idx) in enumerate(cv.split(df_trainval, df_trainval[event_col])):
        try:
            # 分割数据
            train_fold = df_trainval.iloc[train_idx].copy()
            val_fold = df_trainval.iloc[val_idx].copy()

            # 检查数据质量
            train_events = train_fold[event_col].sum()
            val_events = val_fold[event_col].sum()

            if train_events == 0 or train_events == len(train_fold):
                logging.warning(f"Fold {fold_idx}: Training set has only one type of outcome, skipping")
                continue

            if val_events == 0 or val_events == len(val_fold):
                logging.warning(f"Fold {fold_idx}: Validation set has only one type of outcome, skipping")
                continue

            if len(val_fold) < 5:  # 验证集太小
                logging.warning(f"Fold {fold_idx}: Validation set too small ({len(val_fold)} samples), skipping")
                continue
            train_fold_scaled = train_fold.copy()

            val_fold_scaled = val_fold.copy()

            # 准备STG格式的数据
            train_X = train_fold_scaled[feature_cols].to_numpy()
            train_y = {'e': train_fold_scaled[event_col].to_numpy(), 't': train_fold_scaled[duration_col].to_numpy()}

            val_X = val_fold_scaled[feature_cols].to_numpy()
            val_y = {'e': val_fold_scaled[event_col].to_numpy(), 't': val_fold_scaled[duration_col].to_numpy()}

            # prepare_data for STG
            train_data = {}
            train_data['X'], train_data['E'], train_data['T'] = stg_utils.prepare_data(train_X, train_y)
            train_data['ties'] = 'noties'

            val_data = {}
            val_data['X'], val_data['E'], val_data['T'] = stg_utils.prepare_data(val_X, val_y)
            val_data['ties'] = 'noties'

            # 再次检查prepared数据
            if len(val_data['E']) < 2 or len(np.unique(val_data['E'])) < 2:
                logging.warning(f"Fold {fold_idx}: Insufficient validation data after preparation, skipping")
                continue

            # 构建STG模型 - 按照Cox-example的方式
            model = STG(
                task_type='cox',
                input_dim=train_data['X'].shape[1],
                output_dim=1,
                hidden_dims=hidden_dims,
                activation=activation,
                optimizer=optimizer,
                learning_rate=learning_rate,
                batch_size=train_data['X'].shape[0],  # 使用全batch大小，与v5_STG一致
                feature_selection=True,
                sigma=sigma,
                lam=lam,
                # random_state=params['seed'] + round_idx ,
                device = device  # 可以根据需要改为'cuda'
            )

            # 早停机制 - 修复：使用STG的early_stop hook
            early_stop_hook = STGNativeEarlyStopping(model, val_data, patience=patience)

            # 训练模型
            max_epochs = min(params.get('max_epochs', 300), 500)  # CV时减少epochs

            model.fit(
                train_data['X'],
                {'E': train_data['E'], 'T': train_data['T']},
                nr_epochs=max_epochs,
                valid_X=val_data['X'],
                valid_y={'E': val_data['E'], 'T': val_data['T']},
                print_interval=max_epochs,  # 禁用fit内部的打印
                verbose=False,
                early_stop=early_stop_hook
            )

            # 从hook中获取最佳分数
            best_val_score = early_stop_hook.best_score if early_stop_hook.best_score is not None else 0.5

            # 获取选择的特征
            try:
                feature_importances = None
                # 优先使用mode='raw'获取原始的mu值
                try:
                    feature_importances = model.get_gates(mode='raw')
                    # logging.info("Successfully used get_gates(mode='raw') - got raw mu values")
                except (TypeError, AttributeError) as e:
                    # logging.info(f"get_gates(mode='raw') failed: {e}, trying alternative methods")

                    # 尝试直接访问mu属性
                    try:
                        if hasattr(model, 'mu'):
                            feature_importances = model.mu
                            # logging.info("Successfully extracted raw mu from model.mu attribute")
                        elif hasattr(model, 'gates'):
                            feature_importances = model.gates
                            # logging.info("Successfully extracted from model.gates attribute")
                    except Exception as e2:
                        logging.warning(f"Direct mu access failed: {e2}")

                        # 最后尝试mode='prob'
                        try:
                            feature_importances = model.get_gates(mode='prob')
                            # logging.info("Fallback to get_gates(mode='prob')")
                        except Exception as e3:
                            logging.warning(f"All get_gates methods failed: {e3}")

                if feature_importances is not None:
                    # 转换为numpy数组
                    if hasattr(feature_importances, 'detach'):
                        feature_importances = feature_importances.detach().cpu().numpy()
                    elif hasattr(feature_importances, 'numpy'):
                        feature_importances = feature_importances.numpy()
                    feature_importances = np.array(feature_importances).flatten()

                    logging.info(f"Feature importances range: [{feature_importances.min():.4f}, {feature_importances.max():.4f}]")
                    logging.info(f"Feature importances mean: {feature_importances.mean():.4f}, median: {np.median(feature_importances):.4f}")

                if feature_importances is not None and len(feature_importances) > 0:
                    # 使用阈值选择特征 - 直接基于原始mu值
                    threshold = params.get('threshold_gates', 0.5)

                    # 对于原始mu值，通常在[-inf, +inf]范围内，我们需要更合理的阈值策略
                    if threshold > 0:
                        # 如果设置了正阈值，直接使用
                        selected_indices = np.where(feature_importances > threshold)[0]
                    else:
                        # 如果阈值为负或零，使用相对阈值策略
                        # 选择高于均值的特征
                        relative_threshold = max(feature_importances.mean(), np.median(feature_importances))
                        selected_indices = np.where(feature_importances >= relative_threshold)[0]

                    selected_features = [feature_cols[i] for i in selected_indices]

                    # 如果没有选择任何特征，选择top-k个
                    if len(selected_features) == 0:
                        top_k = min(10, len(feature_cols))
                        top_indices = np.argsort(feature_importances)[-top_k:]
                        selected_features = [feature_cols[i] for i in top_indices]
                        # logging.info(f"No features above threshold, selected top {top_k} features")

                    # logging.info(f"Fold {fold_idx}: Selected {len(selected_features)} features with threshold {threshold}")
                else:
                    # 如果无法获取特征重要性，使用默认选择
                    selected_features = feature_cols[:10]
                    logging.info(f"Fold {fold_idx}: Using default selection - first 10 features")

                W = np.ones((train_fold_scaled.shape[0],))
                if params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
                    model_func = get_algorithm_class(params.get('backend', 'Weighted_cox'))
                    model = model_func(
                        X=train_fold_scaled[selected_features + [duration_col, event_col]],
                        duration_col=duration_col,
                        event_col=event_col,
                        W=W,
                        pen=penalizer,
                    )
                    val_fold_scaled["Weights"] = np.ones((val_fold_scaled.shape[0],))
                    val_data_for_score = val_fold_scaled[selected_features + [duration_col, event_col, "Weights"]]
                    best_val_score = model.score(val_data_for_score, scoring_method='concordance_index')
                    # 只有成功训练的fold才添加到结果中
                    if best_val_score > 0:
                        cv_scores.append(best_val_score)
                        logging.info(f"Fold {fold_idx}: Best validation C-index = {best_val_score:.4f}")
                        # 获取选择的特征

            except Exception as e:
                logging.warning(f"Could not extract feature selection results: {str(e)}")
                selected_features = feature_cols[:10]
            selected_features_list.append(selected_features)

        except Exception as e:
            logging.warning(f"Error in fold {fold_idx}: {str(e)}")
            continue

    # 寻找最共同的特征 - 实现交集逻辑
    if len(selected_features_list) > 0:
        # 统计每个特征在多少个fold中被选择
        feature_counts = {}
        for features in selected_features_list:
            for feature in features:
                feature_counts[feature] = feature_counts.get(feature, 0) + 1

        # 找到在至少一半fold中被选择的特征
        min_folds = max(1, len(selected_features_list) // 2)
        common_features = [feature for feature, count in feature_counts.items() if count >= min_folds]

        # 如果共同特征太少，降低要求
        if len(common_features) < 5:
            min_folds = max(1, len(selected_features_list) // 3)
            common_features = [feature for feature, count in feature_counts.items() if count >= min_folds]

        # 如果还是太少，选择出现次数最多的前10个
        if len(common_features) < 5:
            sorted_features = sorted(feature_counts.items(), key=lambda x: x[1], reverse=True)
            common_features = [feature for feature, count in sorted_features[:10]]

        logging.info(f"Found {len(common_features)} common features across {len(selected_features_list)} folds")
        logging.info(f"Common features: {common_features[:10]}...")  # 只显示前10个避免日志过长

        # 将common_features添加到CV评估结果中供后续使用
        # 这里我们暂时存储在某个地方，或者可以作为函数返回值的一部分

    else:
        logging.warning("No successful feature selection from any fold")
        common_features = feature_cols[:10]

    # 返回平均交叉验证C-index
    if not cv_scores:
        logging.warning("No successful CV folds, returning random level")
        # 即使没有成功的CV fold，也返回默认的共同特征
        default_common_features = feature_cols[:10] if len(feature_cols) >= 10 else feature_cols
        # 将共同特征存储在trial的用户属性中，供后续使用
        trial.set_user_attr('common_features', default_common_features)
        return 0.5

    mean_cv_score = np.mean(cv_scores)
    logging.info(f"Trial completed: CV C-index = {mean_cv_score:.4f} (from {len(cv_scores)} successful folds)")

    # 将共同特征存储在trial的用户属性中，供后续使用
    final_common_features = common_features if 'common_features' in locals() else feature_cols[:10]
    trial.set_user_attr('common_features', final_common_features)
    trial.set_user_attr('feature_selection_details', {
        'selected_features_per_fold': selected_features_list,
        'feature_counts': feature_counts if 'feature_counts' in locals() else {},
        'n_successful_folds': len(cv_scores)
    })

    return mean_cv_score


def train_final_model_with_best_params(df_trainval, df_test, feature_cols, best_params):
    """
    Stage 2: Train the final STG model on the entire trainval with the best hyperparameters,
    use STG for feature selection, then train Cox regression on selected features and evaluate on test set
    保持与cross_validation_optuna_stg中相同的STG+Cox流程
    """
    logging.info(f"=== Training final STG model with best parameters ===")
    logging.info(f"Available features: {len(feature_cols)}")
    logging.info(f"Best parameters: {best_params}")

    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return None, None, None

    try:
        # ===================================
        # Step 1: 使用最佳参数训练STG模型进行特征选择
        # ===================================
        logging.info("Step 1: Training STG model for feature selection...")

        # 准备STG格式的数据
        train_X = df_trainval[feature_cols].to_numpy()
        train_y = {'e': df_trainval[event_col].to_numpy(), 't': df_trainval[duration_col].to_numpy()}

        test_X = df_test[feature_cols].to_numpy()
        test_y = {'e': df_test[event_col].to_numpy(), 't': df_test[duration_col].to_numpy()}

        # prepare_data for STG
        train_data = {}
        train_data['X'], train_data['E'], train_data['T'] = stg_utils.prepare_data(train_X, train_y)
        train_data['ties'] = 'noties'

        test_data = {}
        test_data['X'], test_data['E'], test_data['T'] = stg_utils.prepare_data(test_X, test_y)
        test_data['ties'] = 'noties'

        # 从最佳参数中提取STG参数
        stg_params = {}

        # 构建hidden_dims
        n_layers = best_params.get('n_layers', 2)
        hidden_dims = []
        for i in range(n_layers):
            dim = best_params.get(f'hidden_dim_{i + 1}', 32)
            hidden_dims.append(dim)

        # 构建STG模型
        stg_model = STG(
            task_type='cox',
            input_dim=train_data['X'].shape[1],
            output_dim=1,
            hidden_dims=hidden_dims,
            activation=best_params.get('activation', 'relu'),
            optimizer=best_params.get('optimizer', 'Adam'),
            learning_rate=best_params.get('learning_rate', 0.01),
            batch_size=train_data['X'].shape[0],  # 使用全batch
            feature_selection=True,
            sigma=best_params.get('sigma', 1.0),
            lam=best_params.get('lam', 1.0),
            device=device
        )

        # 训练STG模型
        max_epochs = best_params.get('max_epochs', 500)
        patience = best_params.get('patience', 30)

        # 早停机制
        early_stop_hook = STGNativeEarlyStopping(stg_model, test_data, patience=patience)

        logging.info(f"Training STG model for {max_epochs} epochs...")
        stg_model.fit(
            train_data['X'],
            {'E': train_data['E'], 'T': train_data['T']},
            nr_epochs=max_epochs,
            valid_X=test_data['X'],
            valid_y={'E': test_data['E'], 'T': test_data['T']},
            print_interval=max_epochs,  # 减少输出
            verbose=False,
            early_stop=early_stop_hook
        )

        # ===================================
        # Step 2: 从STG模型中提取特征选择结果
        # ===================================
        logging.info("Step 2: Extracting feature selection results from STG...")

        selected_features = []
        feature_importances = None

        # 使用与cross_validation中相同的特征提取逻辑
        try:
            # 优先使用mode='raw'获取原始的mu值
            try:
                feature_importances = stg_model.get_gates(mode='raw')
                logging.info("Successfully used get_gates(mode='raw') - got raw mu values")
            except (TypeError, AttributeError) as e:
                logging.info(f"get_gates(mode='raw') failed: {e}, trying alternative methods")

                # 尝试直接访问mu属性
                try:
                    if hasattr(stg_model, 'mu'):
                        feature_importances = stg_model.mu
                        logging.info("Successfully extracted raw mu from model.mu attribute")
                    elif hasattr(stg_model, 'gates'):
                        feature_importances = stg_model.gates
                        logging.info("Successfully extracted from model.gates attribute")
                except Exception as e2:
                    logging.warning(f"Direct mu access failed: {e2}")

                    # 最后尝试mode='prob'
                    try:
                        feature_importances = stg_model.get_gates(mode='prob')
                        logging.info("Fallback to get_gates(mode='prob')")
                    except Exception as e3:
                        logging.warning(f"All get_gates methods failed: {e3}")

            if feature_importances is not None:
                # 转换为numpy数组
                if hasattr(feature_importances, 'detach'):
                    feature_importances = feature_importances.detach().cpu().numpy()
                elif hasattr(feature_importances, 'numpy'):
                    feature_importances = feature_importances.numpy()
                feature_importances = np.array(feature_importances).flatten()

                logging.info(f"Feature importances range: [{feature_importances.min():.4f}, {feature_importances.max():.4f}]")
                logging.info(f"Feature importances mean: {feature_importances.mean():.4f}, median: {np.median(feature_importances):.4f}")

            if feature_importances is not None and len(feature_importances) > 0:
                # 使用与CV中相同的阈值选择策略
                threshold = best_params.get('threshold_gates', 0.0)

                # 对于原始mu值的阈值策略
                if threshold > 0:
                    # 如果设置了正阈值，直接使用
                    selected_indices = np.where(feature_importances > threshold)[0]
                else:
                    # 如果阈值为负或零，使用相对阈值策略
                    relative_threshold = max(feature_importances.mean(), np.median(feature_importances))
                    selected_indices = np.where(feature_importances >= relative_threshold)[0]

                selected_features = [feature_cols[i] for i in selected_indices]

                # 如果没有选择任何特征，选择top-k个
                if len(selected_features) == 0:
                    top_k = min(10, len(feature_cols))
                    top_indices = np.argsort(feature_importances)[-top_k:]
                    selected_features = [feature_cols[i] for i in top_indices]
                    logging.info(f"No features above threshold, selected top {top_k} features")

                logging.info(f"Selected {len(selected_features)} features with threshold {threshold}")
                logging.info(f"Selected features: {selected_features[:10]}...")
            else:
                # 如果无法获取特征重要性，使用默认选择
                selected_features = feature_cols[:10]
                logging.warning("Could not extract feature importances, using first 10 features")

        except Exception as e:
            logging.warning(f"Feature extraction failed: {str(e)}")
            selected_features = feature_cols[:10]
            logging.warning("Using first 10 features as fallback")

        # ===================================
        # Step 3: 使用选择的特征训练Cox回归模型
        # ===================================
        logging.info("Step 3: Training Cox regression with selected features...")

        if len(selected_features) == 0:
            logging.error("No features selected, cannot train Cox model")
            return None, None, None

        # 准备Cox模型数据 - 与CV中的逻辑保持一致
        W_train = np.ones((df_trainval.shape[0],))
        W_test = np.ones((df_test.shape[0],))

        # 使用与CV中相同的Cox模型训练方式
        penalizer = best_params.get('penalizer', 0.03)

        if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
            # 使用自定义的Weighted_cox算法
            model_func = get_algorithm_class(best_params.get('backend', 'Weighted_cox'))
            final_model = model_func(
                X=df_trainval[selected_features + [duration_col, event_col]],
                duration_col=duration_col,
                event_col=event_col,
                W=W_train,
                pen=penalizer,
            )

            # 评估测试集
            df_test_with_weights = df_test.copy()
            df_test_with_weights["Weights"] = W_test
            test_data_for_score = df_test_with_weights[selected_features + [duration_col, event_col, "Weights"]]
            final_test_c_index_real = final_model.score(test_data_for_score, scoring_method='concordance_index')

        logging.info(f"Final test C-index: {final_test_c_index_real:.4f}")

        # ===================================
        # Step 4: 使用SurvivalEVAL进行综合评估
        # ===================================
        logging.info("Step 4: Comprehensive evaluation using SurvivalEVAL...")

        final_test_ibs = 1.0
        final_test_mae = 1000.0

        try:
            if hasattr(final_model, 'predict_survival_function'):
                # 使用lifelines模型
                df_cox_test = df_test[selected_features + [duration_col, event_col]].copy()
                df_cox_test = df_cox_test.replace([np.inf, -np.inf], np.nan).dropna()

                if len(df_cox_test) >= 5:  # 确保有足够的测试数据
                    survival_curves = final_model.predict_survival_function(df_cox_test)

                    # 准备SurvivalEVAL数据
                    train_event_times = df_trainval[duration_col].values
                    train_event_indicators = df_trainval[event_col].values
                    test_event_times = df_cox_test[duration_col].values
                    test_event_indicators = df_cox_test[event_col].values

                    # 创建评估器
                    evl = LifelinesEvaluator(survival_curves, test_event_times, test_event_indicators,
                                             train_event_times, train_event_indicators)
                    final_test_c_index, _, _ = evl.concordance()
                    if final_test_c_index != final_test_c_index_real:
                        logging.warning(
                            f"SurvivalEVAL C-index: {final_test_c_index:.4f} != Basic C-index: {final_test_c_index_real:.4f}")
                        final_test_c_index = final_test_c_index_real

                    # 计算IBS
                    try:
                        max_time = int(np.max(test_event_times))
                        num_points = min(max_time + 1, 50)
                        final_test_ibs = evl.integrated_brier_score(num_points=num_points, draw_figure=False)
                    except Exception as e:
                        logging.warning(f"IBS calculation failed: {e}")
                        final_test_ibs = 1.0

                    # 计算MAE
                    try:
                        final_test_mae = evl.mae(method="Hinge")
                    except Exception as e:
                        logging.warning(f"MAE calculation failed: {e}")
                        final_test_mae = 1000.0

        except Exception as eval_error:
            logging.warning(f"SurvivalEVAL evaluation failed: {str(eval_error)}")

        # 准备返回结果
        return {
            'final_test_c_index': final_test_c_index,
            'final_test_integrated_brier_score': final_test_ibs,
            'final_test_duration_rmse': final_test_mae  # 使用MAE替代RMSE
        }, feature_cols, final_model

    except Exception as e:
        logging.error(f"Error in final STG+Cox model training: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return None, None, None

def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting STG Round {round_idx}---")

    if not STG_AVAILABLE:
        logging.error("STG package not available. Cannot proceed.")
        return {}

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Hyperparameter optimization (using Optuna)
    # ===============================
    logging.info("=== Stage 1: Starting STG hyperparameter optimization ===")

    # Adaptive adjustment of Optuna trials
    n_trials_reduced = n_trials


    logging.info(f"Use {n_trials_reduced} Optuna trials")

    # Create Optuna study
    study = optuna.create_study(
        direction='maximize',
        sampler=TPESampler(seed=params['seed'] + round_idx)
    )

    # Define objective wrapper
    def objective_wrapper(trial):
        return cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx)

    # Execute hyperparameter optimization
    study.optimize(objective_wrapper, n_trials=n_trials_reduced)

    # Get best parameters
    best_params = params.copy()
    best_params.update(study.best_params)
    best_cv_score = study.best_value

    logging.info(f"STG hyperparameter optimization completed! Best validation C-index: {best_cv_score:.4f}")
    logging.info(f"Best parameters: {study.best_params}")

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final STG model training and evaluation ===")

    final_test_metrics, selected_features, final_model = train_final_model_with_best_params(
        df_trainval, df_test, feature_cols, best_params
    )


    if final_test_metrics is not None:
        logging.info(f"Final test C-index: {final_test_metrics['final_test_c_index']:.4f}")
        logging.info(f"Final test IBS: {final_test_metrics['final_test_integrated_brier_score']:.4f}")
        logging.info(f"Final test MAE: {final_test_metrics['final_test_duration_rmse']:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_c_index": best_cv_score,
            "final_test_c_index": final_test_metrics['final_test_c_index'],
            "final_test_integrated_brier_score": final_test_metrics['final_test_integrated_brier_score'],
            "final_test_duration_rmse": final_test_metrics['final_test_duration_rmse'],
            "best_params": best_params,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final model training failed")
        return {}



def main(params):
    import time
    from datetime import datetime

    # Create logs folder
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)
    if not STG_AVAILABLE:
        print("ERROR: STG package not available. Please install the stg package to use this script.")
        exit(1)

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # Simplified logging setup: only main process saves logs
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"FeatureSelecSTG_Cox_{timestamp}.log")

        # Configure dual output to file and console
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # Set format
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Get root logger and add handlers
        logger = logging.getLogger()
        logger.handlers.clear()  # Clear existing handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info("=== STG Survival Analysis with Optuna Optimization ===")
        logging.info(f"Logging to: {log_filename}")
        logging.info(f"MPI processes: {size}")
        logging.info(f"Parameters: {params}")

    else:
        # Non-main processes: only console output, no file logging
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        console_handler.setFormatter(formatter)

        logger = logging.getLogger()
        logger.handlers.clear()
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

    # Create results directory
    os.makedirs(params['result_dir'], exist_ok=True)

    # Record start time
    start_time = time.time()

    PATH = params['PATH']
    df_trainval, df_test, feature_cols = data_processing(PATH)

    # Run multiple rounds across MPI processes
    local_results = dd(list)
    rounds_per_process = params['times'] // size
    remainder = params['times'] % size

    # Distribute rounds among processes
    if rank < remainder:
        local_rounds = rounds_per_process + 1
        start_round = rank * (rounds_per_process + 1)
    else:
        local_rounds = rounds_per_process
        start_round = rank * rounds_per_process + remainder

    logging.info(f"Process {rank}: Running {local_rounds} rounds starting from round {start_round}")

    for i in range(local_rounds):
        round_idx = start_round + i
        logging.info(f"Process {rank}: Starting round {round_idx + 1}/{params['times']}")

        try:
            result = objective(
                n_trials=params.get('n_trials', 30),
                params=params,
                round_idx=round_idx,
                logger=logging,
                PATH=params['PATH'],
                df_trainval=df_trainval,
                df_test=df_test,
                feature_cols=feature_cols
            )

            if result:
                # 将结果按指标分类存储到字典中
                for k, v in result.items():
                    local_results[k].append(v)
                logging.info(f"Round {round_idx + 1} completed successfully")
            else:
                logging.warning(f"Round {round_idx + 1} failed")

        except Exception as e:
            logging.error(f"Error in round {round_idx + 1}: {str(e)}")

    # Gather results from all processes
    all_results = comm.gather(local_results, root=0)

    # Process and save results (only main process)
    if rank == 0:
        # Flatten results
        final_results_list = dd(list)
        for process_results in all_results:
            for k, v in process_results.items():
                final_results_list[k].extend(v)

        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                if metric_name == 'best_params':
                    continue

                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                        logging.warning("1. Increasing test set size")
                        logging.warning("2. Using more Optuna trials")
                        logging.warning("3. Fixing data splits across runs")
                        logging.warning("4. Ensemble methods")
                    logging.info("-" * 50)

                    final_test_ibs = final_results_list['final_test_integrated_brier_score']
                    final_test_ibs = [s for s in final_test_ibs if isinstance(s, (int, float, np.number))]
                    final_test_rmse = final_results_list['final_test_duration_rmse']
                    final_test_rmse = [s for s in final_test_rmse if isinstance(s, (int, float, np.number))]
                    logging.info(
                        f"Final Test IBS: Mean={np.mean(final_test_ibs):.4f}, Std={np.std(final_test_ibs):.4f}")
                    logging.info(
                        f"Final Test RMSE: Mean={np.mean(final_test_rmse):.4f}, Std={np.std(final_test_rmse):.4f}")

                    logging.info("=" * 50)

                else:
                    logging.info("No valid test scores to analyze")
        else:
            logging.error("No successful results obtained!")

        total_time = time.time() - start_time
        logging.info(f"Total execution time: {total_time:.2f} seconds")
        logging.info("=== Execution completed ===")

if __name__ == "__main__":


    params = {
        'PATH': r'processed_equipment_data_all.csv',
        'seed': 9,  # Fixed base seed
        'n_splits': 10,  # Cross-validation folds
        'times': 1,
        'result_dir': 'results',
        'test_size': 0.1,
        # STG specific parameters
        'max_epochs': 1000,  # Maximum training epochs
        'n_trials': 1,  # Number of Optuna trials
        'threshold_gates': 0.01,
        # For regression
        'backend': 'Weighted_cox', #["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]
        'penalizer': 0.03,
    }

    main(params)


# 运行命令:
# mpiexec -n 4 python ZZ_STG_v1.py
