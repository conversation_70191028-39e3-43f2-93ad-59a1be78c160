#!/usr/bin/env python3
"""
Simple test script to verify basic integration functionality
"""

import pandas as pd
import numpy as np
from lifelines.datasets import load_rossi
from sksurv.util import Surv
import logging

logging.basicConfig(level=logging.INFO)

def test_basic_integration():
    """Test basic integration with simple data"""
    
    print("Testing basic SurvivalEVAL integration...")
    
    # Load simple data
    rossi = load_rossi()
    rossi = rossi.sample(frac=1.0, random_state=42)
    
    # Split train/test
    train = rossi.iloc[:300, :]
    test = rossi.iloc[300:, :]
    
    # Convert to sksurv format
    y_train = Surv.from_dataframe('arrest', 'week', train)
    y_test = Surv.from_dataframe('arrest', 'week', test)
    
    # Select numeric features only
    feature_cols = ['fin', 'age', 'race', 'wexp', 'mar', 'paro', 'prio']
    X_train = train[feature_cols].values
    X_test = test[feature_cols].values
    
    print(f"Train shape: {X_train.shape}, Test shape: {X_test.shape}")
    
    # Test GBM integration
    try:
        from sksurv.ensemble import GradientBoostingSurvivalAnalysis
        from ZZ_GBM_v4 import evaluate_gbm_with_survivaleval
        
        print("\n--- Testing GBM ---")
        gbm = GradientBoostingSurvivalAnalysis(n_estimators=20, learning_rate=0.1, max_depth=3)
        gbm.fit(X_train, y_train)
        
        c_index, auc, ibs, mae = evaluate_gbm_with_survivaleval(
            gbm, X_train, y_train, X_test, y_test
        )
        
        print(f"GBM metrics: C-index={c_index:.4f}, AUC={auc:.4f}, IBS={ibs:.4f}, MAE={mae:.4f}")
        gbm_success = True
        
    except Exception as e:
        print(f"GBM test failed: {e}")
        gbm_success = False
    
    # Test RSF integration
    try:
        from sksurv.ensemble import RandomSurvivalForest
        from ZZ_RSF_v4 import evaluate_rsf_with_survivaleval
        
        print("\n--- Testing RSF ---")
        rsf = RandomSurvivalForest(n_estimators=20, min_samples_split=5, min_samples_leaf=2)
        rsf.fit(X_train, y_train)
        
        c_index, auc, ibs, mae = evaluate_rsf_with_survivaleval(
            rsf, X_train, y_train, X_test, y_test
        )
        
        print(f"RSF metrics: C-index={c_index:.4f}, AUC={auc:.4f}, IBS={ibs:.4f}, MAE={mae:.4f}")
        rsf_success = True
        
    except Exception as e:
        print(f"RSF test failed: {e}")
        rsf_success = False
    
    # Test multi-objective search (simplified)
    try:
        print("\n--- Testing Multi-objective Search ---")
        
        # Create test DataFrame
        df_test = train[feature_cols + ['week', 'arrest']].copy()
        df_test.rename(columns={'week': 'duration', 'arrest': 'event'}, inplace=True)
        
        params = {'seed': 42, 'n_splits': 3, 'n_iter': 2}
        
        # Test GBM search
        from ZZ_GBM_v4 import cross_validation_multi_objective_search as gbm_search
        
        print("Testing GBM multi-objective search...")
        best_model, best_params, best_metrics = gbm_search(
            df_test.iloc[:100], feature_cols, params, round_idx=0
        )
        print(f"GBM search completed: {best_metrics}")
        
        # Test RSF search
        from ZZ_RSF_v4 import cross_validation_multi_objective_search as rsf_search
        
        print("Testing RSF multi-objective search...")
        best_model, best_params, best_metrics = rsf_search(
            df_test.iloc[:100], feature_cols, params, round_idx=0
        )
        print(f"RSF search completed: {best_metrics}")
        
        search_success = True
        
    except Exception as e:
        print(f"Multi-objective search test failed: {e}")
        search_success = False
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    print(f"GBM SurvivalEVAL Integration: {'✓ PASSED' if gbm_success else '✗ FAILED'}")
    print(f"RSF SurvivalEVAL Integration: {'✓ PASSED' if rsf_success else '✗ FAILED'}")
    print(f"Multi-objective Search: {'✓ PASSED' if search_success else '✗ FAILED'}")
    
    if gbm_success and rsf_success and search_success:
        print("\n🎉 All integrations successful! Ready for production use.")
    else:
        print("\n⚠️  Some integrations failed. Please check the error messages above.")


if __name__ == "__main__":
    test_basic_integration()
