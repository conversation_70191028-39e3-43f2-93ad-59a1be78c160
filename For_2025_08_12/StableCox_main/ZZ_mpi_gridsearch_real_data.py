import numpy as np
from mpi4py import MPI
import itertools
import subprocess
import sys
import os

"""
使用方式（Windows）：
mpiexec -n 4 python ZZ_mpi_gridsearch_real_data.py
"""

# 参数空间
TOPN_LIST = [5, 10, 15, 20] #list(range(1, 11))
BACKEND_LIST = ["LogNormal"] #"Weighted_cox", "LogLogistic", "Weibull", "LogNormal"
LAM_BACKEND_LIST = [0.03]
LAM_BACKEND_LIST2 = [0.03]

# 生成参数组合
grid = list(itertools.product(TOPN_LIST, BACKEND_LIST, LAM_BACKEND_LIST, LAM_BACKEND_LIST2))

# MPI初始化
comm = MPI.COMM_WORLD
rank = comm.Get_rank()
size = comm.Get_size()

# 分配任务
tasks_per_rank = len(grid) // size
remainder = len(grid) % size
start = rank * tasks_per_rank + min(rank, remainder)
end = start + tasks_per_rank + (1 if rank < remainder else 0)
my_tasks = grid[start:end]

# 脚本路径与日志路径
script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'ZZ_StableCoxCox_v1_Optuna.py'))
python_exe = os.path.abspath(sys.executable)

# 创建 logs 文件夹
log_dir = os.path.join(os.path.dirname(__file__), "logs")
os.makedirs(log_dir, exist_ok=True)

for i, (topN, backend, lam_backend, lam_backend2) in enumerate(my_tasks):
    # 日志文件路径
    log_filename = f'log_rank{rank}_task{i}_topN{topN}_lam{lam_backend}.txt'
    log_path = os.path.abspath(os.path.join(log_dir, log_filename))

    # 构建要传入 cmd 的命令（注意整体引号包装）
    cmd_inside = f'"{python_exe}" "{script_path}" --topN={topN} --backend={backend} --lam_backend={lam_backend} --lam_backend2={lam_backend2} > "{log_path}" 2>&1'

    # start 的正确格式："窗口名" "cmd.exe" /k "命令"
    # cmd /k 必须使用双引号包住整个命令
    full_cmd = f'start "" cmd /k "{cmd_inside}"'

    try:
        subprocess.Popen(full_cmd, shell=True)
        print(f"[Rank {rank}] Launched: topN={topN}, backend={backend}, lam_backend={lam_backend}")
    except Exception as e:
        print(f"[Rank {rank}] Failed to launch task: {e}", file=sys.stderr)
