# 建议统一使用 lifelines 的 concordance_index
from lifelines.utils import concordance_index
from sksurv.util import Surv
import numpy as np
import logging

duration_col = 'duration'
event_col = 'event'

def compute_c_index(model, df_test, feature_cols, backend):
    """统一的C-index计算函数"""
    if backend == 'Cox':
        # Cox模型
        risk_scores = model.predict_partial_hazard(df_test)
        c_index = concordance_index(df_test[duration_col], -risk_scores, df_test[event_col])
    elif backend == 'GBM':
        # GBM模型
        c_index = model.score(df_test[feature_cols],
                              Surv.from_dataframe(event_col, duration_col, df_test))
    else:
        # 其他模型
        risk_scores = model.predict_partial_hazard(df_test)
        c_index = concordance_index(df_test[duration_col], -risk_scores, df_test[event_col])

    return c_index


def compute_safe_auc(y_train, y_test, risk_scores, df_train, df_test):
    """改进的AUC计算函数"""
    try:
        # 简化时间点选择策略
        train_times = df_train[df_train[event_col] == 1][duration_col]
        test_times = df_test[df_test[event_col] == 1][duration_col]

        if len(train_times) == 0 or len(test_times) == 0:
            return np.nan

        # 使用更保守的时间范围
        max_safe_time = min(
            train_times.quantile(0.75),  # 训练集75%分位数
            test_times.quantile(0.75),  # 测试集75%分位数
            df_train[duration_col].quantile(0.8)  # 训练集观察时间80%分位数
        )

        # 选择3个时间点
        time_points = np.linspace(
            train_times.quantile(0.25),
            max_safe_time,
            3
        )

        # 确保时间点有效
        time_points = time_points[time_points > 0]
        time_points = np.unique(time_points)

        if len(time_points) == 0:
            return np.nan

        auc_scores, mean_auc = cumulative_dynamic_auc(
            y_train, y_test, risk_scores, time_points
        )
        return mean_auc

    except Exception as e:
        logging.warning(f"AUC calculation failed: {e}")
        return np.nan


def compute_brier_scores(model, df_train, df_test, feature_cols, backend):
    """改进的Brier Score计算"""
    try:
        y_train = Surv.from_dataframe(event_col, duration_col, df_train)
        y_test = Surv.from_dataframe(event_col, duration_col, df_test)

        # 选择评估时间点
        eval_times = select_evaluation_times(df_train, df_test)
        if len(eval_times) == 0:
            return np.nan, np.nan

        # 根据模型类型获取生存概率
        if backend == 'GBM':
            survival_probs = get_gbm_survival_probs(model, df_test[feature_cols], eval_times)
        elif backend == 'Cox':
            survival_probs = get_cox_survival_probs(model, df_test, eval_times)
        else:
            # 其他模型使用指数近似
            risk_scores = model.predict_partial_hazard(df_test)
            survival_probs = get_exponential_survival_probs(risk_scores, eval_times)

        # 计算Brier Score
        brier_scores = []
        for i, t in enumerate(eval_times):
            bs_result = brier_score(y_train, y_test, survival_probs[:, i], t)
            if len(bs_result) > 1 and not np.isnan(bs_result[1]):
                brier_scores.append(bs_result[1])

        if len(brier_scores) == 0:
            return np.nan, np.nan

        mean_brier = np.mean(brier_scores)

        # 计算IBS
        try:
            ibs = integrated_brier_score(y_train, y_test, survival_probs, eval_times)
        except:
            # 手动计算IBS
            ibs = np.trapz(brier_scores, eval_times) / (eval_times[-1] - eval_times[0])

        return mean_brier, ibs

    except Exception as e:
        logging.warning(f"Brier score calculation failed: {e}")
        return np.nan, np.nan


def select_evaluation_times(df_train, df_test):
    """选择合适的评估时间点"""
    train_events = df_train[df_train[event_col] == 1][duration_col]
    test_events = df_test[df_test[event_col] == 1][duration_col]

    if len(train_events) == 0:
        return np.array([])

    # 保守的时间范围选择
    max_time = min(
        train_events.quantile(0.75),
        df_train[duration_col].quantile(0.8)
    )

    if len(test_events) > 0:
        max_time = min(max_time, test_events.quantile(0.75))

    min_time = train_events.quantile(0.25)

    if max_time <= min_time:
        return np.array([])

    # 选择2-3个时间点
    eval_times = np.linspace(min_time, max_time, 3)
    return eval_times[eval_times > 0]


def compute_brier_scores(model, df_train, df_test, feature_cols, backend):
    """改进的Brier Score计算"""
    try:
        y_train = Surv.from_dataframe(event_col, duration_col, df_train)
        y_test = Surv.from_dataframe(event_col, duration_col, df_test)

        # 选择评估时间点
        eval_times = select_evaluation_times(df_train, df_test)
        if len(eval_times) == 0:
            return np.nan, np.nan

        # 根据模型类型获取生存概率
        if backend == 'GBM':
            survival_probs = get_gbm_survival_probs(model, df_test[feature_cols], eval_times)
        elif backend == 'Cox':
            survival_probs = get_cox_survival_probs(model, df_test, eval_times)
        else:
            # 其他模型使用指数近似
            risk_scores = model.predict_partial_hazard(df_test)
            survival_probs = get_exponential_survival_probs(risk_scores, eval_times)

        # 计算Brier Score
        brier_scores = []
        for i, t in enumerate(eval_times):
            bs_result = brier_score(y_train, y_test, survival_probs[:, i], t)
            if len(bs_result) > 1 and not np.isnan(bs_result[1]):
                brier_scores.append(bs_result[1])

        if len(brier_scores) == 0:
            return np.nan, np.nan

        mean_brier = np.mean(brier_scores)

        # 计算IBS
        try:
            ibs = integrated_brier_score(y_train, y_test, survival_probs, eval_times)
        except:
            # 手动计算IBS
            ibs = np.trapz(brier_scores, eval_times) / (eval_times[-1] - eval_times[0])

        return mean_brier, ibs

    except Exception as e:
        logging.warning(f"Brier score calculation failed: {e}")
        return np.nan, np.nan


def select_evaluation_times(df_train, df_test):
    """选择合适的评估时间点"""
    train_events = df_train[df_train[event_col] == 1][duration_col]
    test_events = df_test[df_test[event_col] == 1][duration_col]

    if len(train_events) == 0:
        return np.array([])

    # 保守的时间范围选择
    max_time = min(
        train_events.quantile(0.75),
        df_train[duration_col].quantile(0.8)
    )

    if len(test_events) > 0:
        max_time = min(max_time, test_events.quantile(0.75))

    min_time = train_events.quantile(0.25)

    if max_time <= min_time:
        return np.array([])

    # 选择2-3个时间点
    eval_times = np.linspace(min_time, max_time, 3)
    return eval_times[eval_times > 0]


def compute_duration_rmse(model, df_train, df_test, feature_cols, backend):
    """改进的duration RMSE计算"""
    try:
        # 只对未删失样本计算
        uncensored_mask = df_test[event_col] == 1
        if uncensored_mask.sum() == 0:
            return np.nan

        true_durations = df_test.loc[uncensored_mask, duration_col]

        if backend == 'GBM':
            # GBM: 使用期望生存时间
            predicted_durations = predict_gbm_durations(model, df_test[feature_cols])
        elif backend == 'Cox':
            # Cox: 使用中位生存时间或期望生存时间
            predicted_durations = predict_cox_durations(model, df_test, df_train)
        else:
            # 其他模型: 基于风险评分的简单转换
            risk_scores = model.predict_partial_hazard(df_test)
            baseline_median = df_train[duration_col].median()
            predicted_durations = baseline_median * np.exp(-risk_scores * 0.5)

        pred_durations_uncensored = predicted_durations[uncensored_mask]

        # 处理异常值
        max_reasonable = df_train[duration_col].quantile(0.95) * 2
        min_reasonable = df_train[duration_col].quantile(0.05) * 0.5
        pred_durations_uncensored = np.clip(
            pred_durations_uncensored,
            min_reasonable,
            max_reasonable
        )

        rmse = np.sqrt(mean_squared_error(true_durations, pred_durations_uncensored))
        return rmse

    except Exception as e:
        logging.warning(f"Duration RMSE calculation failed: {e}")
        return np.nan


def compute_comprehensive_metrics_unified(model, df_train, df_test, feature_cols, backend):
    """统一的综合评估函数"""
    try:
        # 1. C-index
        c_index = compute_c_index(model, df_test, feature_cols, backend)

        # 2. Time-dependent AUC
        y_train = Surv.from_dataframe(event_col, duration_col, df_train)
        y_test = Surv.from_dataframe(event_col, duration_col, df_test)

        if backend == 'GBM':
            risk_scores = model.predict(df_test[feature_cols])
        else:
            risk_scores = model.predict_partial_hazard(df_test)

        td_auc = compute_safe_auc(y_train, y_test, risk_scores, df_train, df_test)

        # 3. Brier Score 和 IBS
        mean_brier, ibs = compute_brier_scores(model, df_train, df_test, feature_cols, backend)

        # 4. Duration RMSE
        duration_rmse = compute_duration_rmse(model, df_train, df_test, feature_cols, backend)

        return {
            'c_index': c_index,
            'time_dependent_auc': td_auc,
            'brier_score': mean_brier,
            'integrated_brier_score': ibs,
            'duration_rmse': duration_rmse
        }

    except Exception as e:
        logging.error(f"Comprehensive evaluation failed: {e}")
        return {
            'c_index': 0.5,
            'time_dependent_auc': np.nan,
            'brier_score': np.nan,
            'integrated_brier_score': np.nan,
            'duration_rmse': np.nan
        }