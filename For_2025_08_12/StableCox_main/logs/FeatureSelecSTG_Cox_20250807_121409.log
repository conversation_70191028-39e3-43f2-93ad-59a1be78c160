2025-08-07 12:14:09 [INFO] === STG Survival Analysis with Optuna Optimization ===
2025-08-07 12:14:09 [INFO] Logging to: logs\FeatureSelecSTG_Cox_20250807_121409.log
2025-08-07 12:14:09 [INFO] MPI processes: 5
2025-08-07 12:14:09 [INFO] Parameters: {'PATH': 'processed_equipment_data_all.csv', 'seed': 9, 'n_splits': 10, 'times': 5, 'result_dir': 'results', 'test_size': 0.1, 'max_epochs': 1000, 'n_trials': 300, 'threshold_gates': 0.001, 'backend': 'LogLogistic', 'penalizer': 0.03}
2025-08-07 12:14:09 [INFO] Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-08-07 12:14:09 [INFO] Data scaler splite with random state 9
2025-08-07 12:14:09 [INFO] Process 0: Running 1 rounds starting from round 0
2025-08-07 12:14:09 [INFO] Process 0: Starting round 1/5
2025-08-07 12:14:09 [INFO] --- Starting STG Round 0---
2025-08-07 12:14:09 [INFO] Data split - Train/Val: (1777, 182), Test: (198, 182)
2025-08-07 12:14:09 [INFO] === Stage 1: Starting STG hyperparameter optimization ===
2025-08-07 12:14:09 [INFO] Use 300 Optuna trials
2025-08-07 12:14:09 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:14:09 [INFO] Hidden dims: [33], activation: relu, optimizer: Adam
2025-08-07 12:14:10 [INFO] Early stopping triggered. Best score: 0.5329
2025-08-07 12:14:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:10 [INFO] Feature importances range: [-0.0379, 0.0259]
2025-08-07 12:14:10 [INFO] Feature importances mean: -0.0071, median: -0.0066
2025-08-07 12:14:10 [INFO] Fold 0: Selected 42 features with threshold 0.001
2025-08-07 12:14:11 [INFO] Fold 0: Best validation C-index = 0.5114
2025-08-07 12:14:11 [INFO] Early stopping triggered. Best score: 0.4627
2025-08-07 12:14:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:11 [INFO] Feature importances range: [-0.0345, 0.0298]
2025-08-07 12:14:11 [INFO] Feature importances mean: -0.0062, median: -0.0057
2025-08-07 12:14:11 [INFO] Fold 1: Selected 44 features with threshold 0.001
2025-08-07 12:14:12 [INFO] Fold 1: Best validation C-index = 0.4884
2025-08-07 12:14:21 [INFO] Early stopping triggered. Best score: 0.5463
2025-08-07 12:14:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:21 [INFO] Feature importances range: [-0.0486, -0.0023]
2025-08-07 12:14:21 [INFO] Feature importances mean: -0.0267, median: -0.0263
2025-08-07 12:14:21 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:14:21 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:14:21 [INFO] Fold 2: Best validation C-index = 0.4902
2025-08-07 12:14:25 [INFO] Early stopping triggered. Best score: 0.5252
2025-08-07 12:14:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:25 [INFO] Feature importances range: [-0.0666, -0.0083]
2025-08-07 12:14:25 [INFO] Feature importances mean: -0.0390, median: -0.0394
2025-08-07 12:14:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:14:25 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:14:25 [INFO] Fold 3: Best validation C-index = 0.5362
2025-08-07 12:14:26 [INFO] Early stopping triggered. Best score: 0.5440
2025-08-07 12:14:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:26 [INFO] Feature importances range: [-0.0276, 0.0217]
2025-08-07 12:14:26 [INFO] Feature importances mean: -0.0044, median: -0.0046
2025-08-07 12:14:26 [INFO] Fold 4: Selected 54 features with threshold 0.001
2025-08-07 12:14:26 [INFO] Fold 4: Best validation C-index = 0.5436
2025-08-07 12:14:27 [INFO] Early stopping triggered. Best score: 0.5271
2025-08-07 12:14:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:27 [INFO] Feature importances range: [-0.0336, 0.0191]
2025-08-07 12:14:27 [INFO] Feature importances mean: -0.0098, median: -0.0100
2025-08-07 12:14:27 [INFO] Fold 5: Selected 22 features with threshold 0.001
2025-08-07 12:14:28 [INFO] Fold 5: Best validation C-index = 0.5169
2025-08-07 12:14:39 [INFO] Early stopping triggered. Best score: 0.5077
2025-08-07 12:14:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:39 [INFO] Feature importances range: [-0.0499, 0.0076]
2025-08-07 12:14:39 [INFO] Feature importances mean: -0.0173, median: -0.0173
2025-08-07 12:14:39 [INFO] Fold 6: Selected 8 features with threshold 0.001
2025-08-07 12:14:39 [INFO] Fold 6: Best validation C-index = 0.4886
2025-08-07 12:14:40 [INFO] Early stopping triggered. Best score: 0.5448
2025-08-07 12:14:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:40 [INFO] Feature importances range: [-0.0338, 0.0164]
2025-08-07 12:14:40 [INFO] Feature importances mean: -0.0056, median: -0.0065
2025-08-07 12:14:40 [INFO] Fold 7: Selected 46 features with threshold 0.001
2025-08-07 12:14:41 [INFO] Fold 7: Best validation C-index = 0.5307
2025-08-07 12:14:43 [INFO] Early stopping triggered. Best score: 0.5315
2025-08-07 12:14:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:43 [INFO] Feature importances range: [-0.0435, 0.0088]
2025-08-07 12:14:43 [INFO] Feature importances mean: -0.0131, median: -0.0133
2025-08-07 12:14:43 [INFO] Fold 8: Selected 17 features with threshold 0.001
2025-08-07 12:14:44 [INFO] Fold 8: Best validation C-index = 0.5403
2025-08-07 12:14:53 [INFO] Early stopping triggered. Best score: 0.5208
2025-08-07 12:14:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:53 [INFO] Feature importances range: [-0.0419, 0.0140]
2025-08-07 12:14:53 [INFO] Feature importances mean: -0.0125, median: -0.0130
2025-08-07 12:14:53 [INFO] Fold 9: Selected 19 features with threshold 0.001
2025-08-07 12:14:53 [INFO] Fold 9: Best validation C-index = 0.4548
2025-08-07 12:14:53 [INFO] Found 32 common features across 10 folds
2025-08-07 12:14:53 [INFO] Common features: ['100V_V280_L_Y', '100V_V700_L_X', '100V_V700_L_Y', '200V_V700_L_Z', '420V_V700_L_X', 'EPI_700V_X1_LOW', 'EPI_Asymmetry', 'HEATSINK_TEMP_VALUE', 'Heat_Sink_Temp_Amplifier_Initial', 'OUTPUT_VOLTAGE_DISCH_V700_L_Z']...
2025-08-07 12:14:53 [INFO] Trial completed: CV C-index = 0.5101 (from 10 successful folds)
2025-08-07 12:14:54 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:14:54 [INFO] Hidden dims: [37, 58, 43], activation: selu, optimizer: Adagrad
2025-08-07 12:14:56 [INFO] Early stopping triggered. Best score: 0.5559
2025-08-07 12:14:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:14:56 [INFO] Feature importances range: [-0.2846, -0.2262]
2025-08-07 12:14:56 [INFO] Feature importances mean: -0.2529, median: -0.2528
2025-08-07 12:14:56 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:14:56 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:14:57 [INFO] Fold 0: Best validation C-index = 0.5296
2025-08-07 12:15:08 [INFO] Early stopping triggered. Best score: 0.5811
2025-08-07 12:15:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:08 [INFO] Feature importances range: [-0.2375, -0.1866]
2025-08-07 12:15:08 [INFO] Feature importances mean: -0.2115, median: -0.2109
2025-08-07 12:15:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:08 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:15:08 [INFO] Fold 1: Best validation C-index = 0.5911
2025-08-07 12:15:11 [INFO] Early stopping triggered. Best score: 0.5369
2025-08-07 12:15:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:11 [INFO] Feature importances range: [-0.2404, -0.1878]
2025-08-07 12:15:11 [INFO] Feature importances mean: -0.2144, median: -0.2146
2025-08-07 12:15:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:11 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:15:12 [INFO] Fold 2: Best validation C-index = 0.5320
2025-08-07 12:15:23 [INFO] Early stopping triggered. Best score: 0.5597
2025-08-07 12:15:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:23 [INFO] Feature importances range: [-0.2813, -0.2241]
2025-08-07 12:15:23 [INFO] Feature importances mean: -0.2543, median: -0.2540
2025-08-07 12:15:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:23 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:15:24 [INFO] Fold 3: Best validation C-index = 0.5154
2025-08-07 12:15:26 [INFO] Early stopping triggered. Best score: 0.5054
2025-08-07 12:15:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:26 [INFO] Feature importances range: [-0.2464, -0.1978]
2025-08-07 12:15:26 [INFO] Feature importances mean: -0.2243, median: -0.2247
2025-08-07 12:15:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:26 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:15:26 [INFO] Fold 4: Best validation C-index = 0.4714
2025-08-07 12:15:33 [INFO] Early stopping triggered. Best score: 0.5378
2025-08-07 12:15:33 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:33 [INFO] Feature importances range: [-0.2541, -0.1944]
2025-08-07 12:15:33 [INFO] Feature importances mean: -0.2214, median: -0.2223
2025-08-07 12:15:33 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:33 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:15:35 [INFO] Fold 5: Best validation C-index = 0.5164
2025-08-07 12:15:39 [INFO] Early stopping triggered. Best score: 0.5467
2025-08-07 12:15:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:39 [INFO] Feature importances range: [-0.2477, -0.1949]
2025-08-07 12:15:39 [INFO] Feature importances mean: -0.2176, median: -0.2174
2025-08-07 12:15:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:39 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:15:39 [INFO] Fold 6: Best validation C-index = 0.5502
2025-08-07 12:15:42 [INFO] Early stopping triggered. Best score: 0.5316
2025-08-07 12:15:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:42 [INFO] Feature importances range: [-0.2441, -0.1969]
2025-08-07 12:15:42 [INFO] Feature importances mean: -0.2222, median: -0.2218
2025-08-07 12:15:42 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:42 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:15:42 [INFO] Fold 7: Best validation C-index = 0.5128
2025-08-07 12:15:53 [INFO] Early stopping triggered. Best score: 0.5309
2025-08-07 12:15:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:53 [INFO] Feature importances range: [-0.2688, -0.2156]
2025-08-07 12:15:53 [INFO] Feature importances mean: -0.2423, median: -0.2425
2025-08-07 12:15:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:53 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:15:53 [INFO] Fold 8: Best validation C-index = 0.5206
2025-08-07 12:15:55 [INFO] Early stopping triggered. Best score: 0.5253
2025-08-07 12:15:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:55 [INFO] Feature importances range: [-0.2394, -0.1885]
2025-08-07 12:15:55 [INFO] Feature importances mean: -0.2104, median: -0.2101
2025-08-07 12:15:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:15:55 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:15:55 [INFO] Fold 9: Best validation C-index = 0.5049
2025-08-07 12:15:55 [INFO] Found 10 common features across 10 folds
2025-08-07 12:15:55 [INFO] Common features: ['PULSE_280V_Y1_LOW', 'BUS_VOLTAGE_VX_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_H_X', 'EPI_IERROR_Peak', 'OUTPUT_VOLTAGE_DISCH_V700_H_X', 'Y_CLOSED_LOOP_V700_L_Z', 'PULSE_280V_Z1_LOW', 'Z_CLOSED_LOOP_V700_H_Z', 'Absolute_Error', '420V_V700_H_X']...
2025-08-07 12:15:55 [INFO] Trial completed: CV C-index = 0.5245 (from 10 successful folds)
2025-08-07 12:15:55 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:15:55 [INFO] Hidden dims: [56, 63, 12], activation: tanh, optimizer: Adagrad
2025-08-07 12:15:57 [INFO] Early stopping triggered. Best score: 0.5395
2025-08-07 12:15:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:15:57 [INFO] Feature importances range: [-0.0339, 0.0119]
2025-08-07 12:15:57 [INFO] Feature importances mean: -0.0121, median: -0.0129
2025-08-07 12:15:57 [INFO] Fold 0: Selected 22 features with threshold 0.001
2025-08-07 12:15:57 [INFO] Fold 0: Best validation C-index = 0.5479
2025-08-07 12:16:08 [INFO] Early stopping triggered. Best score: 0.5214
2025-08-07 12:16:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:08 [INFO] Feature importances range: [-0.0389, 0.0092]
2025-08-07 12:16:08 [INFO] Feature importances mean: -0.0141, median: -0.0137
2025-08-07 12:16:08 [INFO] Fold 1: Selected 8 features with threshold 0.001
2025-08-07 12:16:09 [INFO] Fold 1: Best validation C-index = 0.5127
2025-08-07 12:16:10 [INFO] Early stopping triggered. Best score: 0.5107
2025-08-07 12:16:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:10 [INFO] Feature importances range: [-0.0325, 0.0179]
2025-08-07 12:16:10 [INFO] Feature importances mean: -0.0080, median: -0.0076
2025-08-07 12:16:10 [INFO] Fold 2: Selected 32 features with threshold 0.001
2025-08-07 12:16:10 [INFO] Fold 2: Best validation C-index = 0.4969
2025-08-07 12:16:12 [INFO] Early stopping triggered. Best score: 0.5274
2025-08-07 12:16:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:12 [INFO] Feature importances range: [-0.0338, 0.0060]
2025-08-07 12:16:12 [INFO] Feature importances mean: -0.0121, median: -0.0115
2025-08-07 12:16:12 [INFO] Fold 3: Selected 14 features with threshold 0.001
2025-08-07 12:16:12 [INFO] Fold 3: Best validation C-index = 0.4913
2025-08-07 12:16:13 [INFO] Early stopping triggered. Best score: 0.5209
2025-08-07 12:16:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:13 [INFO] Feature importances range: [-0.0331, 0.0226]
2025-08-07 12:16:13 [INFO] Feature importances mean: -0.0094, median: -0.0098
2025-08-07 12:16:13 [INFO] Fold 4: Selected 25 features with threshold 0.001
2025-08-07 12:16:13 [INFO] Fold 4: Best validation C-index = 0.5094
2025-08-07 12:16:22 [INFO] Early stopping triggered. Best score: 0.5156
2025-08-07 12:16:22 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:22 [INFO] Feature importances range: [-0.0404, 0.0176]
2025-08-07 12:16:22 [INFO] Feature importances mean: -0.0082, median: -0.0076
2025-08-07 12:16:22 [INFO] Fold 5: Selected 35 features with threshold 0.001
2025-08-07 12:16:23 [INFO] Fold 5: Best validation C-index = 0.5038
2025-08-07 12:16:24 [INFO] Early stopping triggered. Best score: 0.5139
2025-08-07 12:16:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:24 [INFO] Feature importances range: [-0.0331, 0.0247]
2025-08-07 12:16:24 [INFO] Feature importances mean: -0.0073, median: -0.0076
2025-08-07 12:16:24 [INFO] Fold 6: Selected 30 features with threshold 0.001
2025-08-07 12:16:24 [INFO] Fold 6: Best validation C-index = 0.4918
2025-08-07 12:16:26 [INFO] Early stopping triggered. Best score: 0.5074
2025-08-07 12:16:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:26 [INFO] Feature importances range: [-0.0416, 0.0145]
2025-08-07 12:16:26 [INFO] Feature importances mean: -0.0134, median: -0.0140
2025-08-07 12:16:26 [INFO] Fold 7: Selected 13 features with threshold 0.001
2025-08-07 12:16:26 [INFO] Fold 7: Best validation C-index = 0.4933
2025-08-07 12:16:27 [INFO] Early stopping triggered. Best score: 0.5472
2025-08-07 12:16:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:27 [INFO] Feature importances range: [-0.0371, 0.0137]
2025-08-07 12:16:27 [INFO] Feature importances mean: -0.0087, median: -0.0088
2025-08-07 12:16:27 [INFO] Fold 8: Selected 26 features with threshold 0.001
2025-08-07 12:16:28 [INFO] Fold 8: Best validation C-index = 0.5213
2025-08-07 12:16:37 [INFO] Early stopping triggered. Best score: 0.5536
2025-08-07 12:16:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:37 [INFO] Feature importances range: [-0.0370, 0.0171]
2025-08-07 12:16:37 [INFO] Feature importances mean: -0.0087, median: -0.0082
2025-08-07 12:16:37 [INFO] Fold 9: Selected 32 features with threshold 0.001
2025-08-07 12:16:37 [INFO] Fold 9: Best validation C-index = 0.4911
2025-08-07 12:16:37 [INFO] Found 27 common features across 10 folds
2025-08-07 12:16:37 [INFO] Common features: ['420V_V700_L_Z', 'PULSE_700V_Y1_LOW', 'X_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V700_H_Y', '100A_Flattop_Positive', '100A_IERROR_Peak', 'EPI_IERROR_RMS', 'PULSE_700V_Z2_LOW', '300V_V280_H_Z', '420V_V700_H_X']...
2025-08-07 12:16:37 [INFO] Trial completed: CV C-index = 0.5060 (from 10 successful folds)
2025-08-07 12:16:37 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:16:37 [INFO] Hidden dims: [46, 60, 3], activation: relu, optimizer: Adagrad
2025-08-07 12:16:39 [INFO] Early stopping triggered. Best score: 0.5127
2025-08-07 12:16:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:39 [INFO] Feature importances range: [-0.0235, 0.0273]
2025-08-07 12:16:39 [INFO] Feature importances mean: -0.0008, median: -0.0022
2025-08-07 12:16:39 [INFO] Fold 0: Selected 73 features with threshold 0.001
2025-08-07 12:16:40 [INFO] Fold 0: Best validation C-index = 0.4704
2025-08-07 12:16:42 [INFO] Early stopping triggered. Best score: 0.5039
2025-08-07 12:16:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:42 [INFO] Feature importances range: [-0.0263, 0.0257]
2025-08-07 12:16:42 [INFO] Feature importances mean: -0.0006, median: -0.0002
2025-08-07 12:16:42 [INFO] Fold 1: Selected 78 features with threshold 0.001
2025-08-07 12:16:42 [INFO] Fold 1: Best validation C-index = 0.5290
2025-08-07 12:16:52 [INFO] Early stopping triggered. Best score: 0.4887
2025-08-07 12:16:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:52 [INFO] Feature importances range: [-0.0250, 0.0299]
2025-08-07 12:16:52 [INFO] Feature importances mean: -0.0005, median: -0.0008
2025-08-07 12:16:52 [INFO] Fold 2: Selected 71 features with threshold 0.001
2025-08-07 12:16:53 [INFO] Fold 2: Best validation C-index = 0.5514
2025-08-07 12:16:55 [INFO] Early stopping triggered. Best score: 0.5182
2025-08-07 12:16:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:16:55 [INFO] Feature importances range: [-0.0307, 0.0342]
2025-08-07 12:16:55 [INFO] Feature importances mean: 0.0004, median: 0.0001
2025-08-07 12:16:55 [INFO] Fold 3: Selected 82 features with threshold 0.001
2025-08-07 12:16:55 [INFO] Fold 3: Best validation C-index = 0.4746
2025-08-07 12:17:05 [INFO] Early stopping triggered. Best score: 0.5141
2025-08-07 12:17:05 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:05 [INFO] Feature importances range: [-0.0334, 0.0330]
2025-08-07 12:17:05 [INFO] Feature importances mean: -0.0006, median: -0.0006
2025-08-07 12:17:05 [INFO] Fold 4: Selected 79 features with threshold 0.001
2025-08-07 12:17:06 [INFO] Fold 4: Best validation C-index = 0.5236
2025-08-07 12:17:09 [INFO] Early stopping triggered. Best score: 0.5006
2025-08-07 12:17:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:09 [INFO] Feature importances range: [-0.0254, 0.0254]
2025-08-07 12:17:09 [INFO] Feature importances mean: -0.0005, median: -0.0014
2025-08-07 12:17:09 [INFO] Fold 5: Selected 72 features with threshold 0.001
2025-08-07 12:17:09 [INFO] Fold 5: Best validation C-index = 0.5074
2025-08-07 12:17:11 [INFO] Early stopping triggered. Best score: 0.4473
2025-08-07 12:17:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:11 [INFO] Feature importances range: [-0.0267, 0.0279]
2025-08-07 12:17:11 [INFO] Feature importances mean: -0.0001, median: 0.0004
2025-08-07 12:17:11 [INFO] Fold 6: Selected 85 features with threshold 0.001
2025-08-07 12:17:12 [INFO] Fold 6: Best validation C-index = 0.5283
2025-08-07 12:17:13 [INFO] Early stopping triggered. Best score: 0.4920
2025-08-07 12:17:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:13 [INFO] Feature importances range: [-0.0359, 0.0213]
2025-08-07 12:17:13 [INFO] Feature importances mean: -0.0023, median: -0.0015
2025-08-07 12:17:13 [INFO] Fold 7: Selected 70 features with threshold 0.001
2025-08-07 12:17:16 [INFO] Fold 7: Best validation C-index = 0.5136
2025-08-07 12:17:23 [INFO] Early stopping triggered. Best score: 0.4919
2025-08-07 12:17:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:23 [INFO] Feature importances range: [-0.0270, 0.0257]
2025-08-07 12:17:23 [INFO] Feature importances mean: -0.0009, median: -0.0016
2025-08-07 12:17:23 [INFO] Fold 8: Selected 75 features with threshold 0.001
2025-08-07 12:17:24 [INFO] Fold 8: Best validation C-index = 0.4750
2025-08-07 12:17:26 [INFO] Early stopping triggered. Best score: 0.4684
2025-08-07 12:17:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:26 [INFO] Feature importances range: [-0.0249, 0.0246]
2025-08-07 12:17:26 [INFO] Feature importances mean: 0.0000, median: 0.0007
2025-08-07 12:17:26 [INFO] Fold 9: Selected 87 features with threshold 0.001
2025-08-07 12:17:27 [INFO] Fold 9: Best validation C-index = 0.5447
2025-08-07 12:17:27 [INFO] Found 80 common features across 10 folds
2025-08-07 12:17:27 [INFO] Common features: ['100A_Flattop_Positive', '100A_IERROR_RMS', '100V_V280_H_X', '100V_V280_L_Y', '100V_V700_H_Z', '200V_V280_H_Z', '200V_V700_H_Z', '200V_V700_L_Z', '300V_V280_H_Z', '300V_V700_L_Y']...
2025-08-07 12:17:27 [INFO] Trial completed: CV C-index = 0.5118 (from 10 successful folds)
2025-08-07 12:17:27 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:17:27 [INFO] Hidden dims: [18, 59, 2, 25], activation: sigmoid, optimizer: Adam
2025-08-07 12:17:28 [INFO] Early stopping triggered. Best score: 0.4635
2025-08-07 12:17:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:28 [INFO] Feature importances range: [-0.0223, 0.0155]
2025-08-07 12:17:28 [INFO] Feature importances mean: -0.0028, median: -0.0030
2025-08-07 12:17:28 [INFO] Fold 0: Selected 63 features with threshold 0.001
2025-08-07 12:17:28 [INFO] Fold 0: Best validation C-index = 0.5124
2025-08-07 12:17:37 [INFO] Early stopping triggered. Best score: 0.4927
2025-08-07 12:17:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:37 [INFO] Feature importances range: [-0.0251, 0.0264]
2025-08-07 12:17:37 [INFO] Feature importances mean: -0.0031, median: -0.0038
2025-08-07 12:17:37 [INFO] Fold 1: Selected 61 features with threshold 0.001
2025-08-07 12:17:38 [INFO] Fold 1: Best validation C-index = 0.5272
2025-08-07 12:17:41 [INFO] Early stopping triggered. Best score: 0.4638
2025-08-07 12:17:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:41 [INFO] Feature importances range: [-0.0338, 0.0166]
2025-08-07 12:17:41 [INFO] Feature importances mean: -0.0069, median: -0.0065
2025-08-07 12:17:41 [INFO] Fold 2: Selected 35 features with threshold 0.001
2025-08-07 12:17:41 [INFO] Fold 2: Best validation C-index = 0.4983
2025-08-07 12:17:42 [INFO] Early stopping triggered. Best score: 0.5216
2025-08-07 12:17:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:42 [INFO] Feature importances range: [-0.0292, 0.0245]
2025-08-07 12:17:42 [INFO] Feature importances mean: -0.0029, median: -0.0033
2025-08-07 12:17:42 [INFO] Fold 3: Selected 60 features with threshold 0.001
2025-08-07 12:17:43 [INFO] Fold 3: Best validation C-index = 0.5068
2025-08-07 12:17:52 [INFO] Early stopping triggered. Best score: 0.4499
2025-08-07 12:17:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:52 [INFO] Feature importances range: [-0.0312, 0.0234]
2025-08-07 12:17:52 [INFO] Feature importances mean: -0.0031, median: -0.0034
2025-08-07 12:17:52 [INFO] Fold 4: Selected 60 features with threshold 0.001
2025-08-07 12:17:53 [INFO] Fold 4: Best validation C-index = 0.5633
2025-08-07 12:17:54 [INFO] Early stopping triggered. Best score: 0.4959
2025-08-07 12:17:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:54 [INFO] Feature importances range: [-0.0354, 0.0258]
2025-08-07 12:17:54 [INFO] Feature importances mean: -0.0037, median: -0.0047
2025-08-07 12:17:54 [INFO] Fold 5: Selected 49 features with threshold 0.001
2025-08-07 12:17:55 [INFO] Fold 5: Best validation C-index = 0.4593
2025-08-07 12:17:56 [INFO] Early stopping triggered. Best score: 0.4764
2025-08-07 12:17:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:56 [INFO] Feature importances range: [-0.0362, 0.0263]
2025-08-07 12:17:56 [INFO] Feature importances mean: -0.0034, median: -0.0056
2025-08-07 12:17:56 [INFO] Fold 6: Selected 56 features with threshold 0.001
2025-08-07 12:17:57 [INFO] Fold 6: Best validation C-index = 0.4719
2025-08-07 12:17:58 [INFO] Early stopping triggered. Best score: 0.4700
2025-08-07 12:17:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:17:58 [INFO] Feature importances range: [-0.0343, 0.0280]
2025-08-07 12:17:58 [INFO] Feature importances mean: -0.0020, median: -0.0017
2025-08-07 12:17:58 [INFO] Fold 7: Selected 74 features with threshold 0.001
2025-08-07 12:17:59 [INFO] Fold 7: Best validation C-index = 0.4802
2025-08-07 12:18:07 [INFO] Early stopping triggered. Best score: 0.4869
2025-08-07 12:18:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:07 [INFO] Feature importances range: [-0.0293, 0.0295]
2025-08-07 12:18:07 [INFO] Feature importances mean: -0.0031, median: -0.0020
2025-08-07 12:18:07 [INFO] Fold 8: Selected 66 features with threshold 0.001
2025-08-07 12:18:08 [INFO] Fold 8: Best validation C-index = 0.4899
2025-08-07 12:18:10 [INFO] Early stopping triggered. Best score: 0.5382
2025-08-07 12:18:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:10 [INFO] Feature importances range: [-0.0403, 0.0202]
2025-08-07 12:18:10 [INFO] Feature importances mean: -0.0073, median: -0.0079
2025-08-07 12:18:10 [INFO] Fold 9: Selected 44 features with threshold 0.001
2025-08-07 12:18:11 [INFO] Fold 9: Best validation C-index = 0.5136
2025-08-07 12:18:11 [INFO] Found 33 common features across 10 folds
2025-08-07 12:18:11 [INFO] Common features: ['100V_V280_L_Y', '200V_V280_L_Y', '420V_V280_L_Z', '420V_V700_H_Y', 'Absolute_Error', 'EPI_700V_X2_LOW', 'EPI_700V_Y1_HIGH', 'EPI_700V_Z2_HIGH', 'EPI_Asymmetry', 'HST_POST_TEMP']...
2025-08-07 12:18:11 [INFO] Trial completed: CV C-index = 0.5023 (from 10 successful folds)
2025-08-07 12:18:11 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:18:11 [INFO] Hidden dims: [48, 36], activation: selu, optimizer: Adam
2025-08-07 12:18:26 [INFO] Early stopping triggered. Best score: 0.5129
2025-08-07 12:18:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:26 [INFO] Feature importances range: [-0.0372, 0.0245]
2025-08-07 12:18:26 [INFO] Feature importances mean: -0.0050, median: -0.0048
2025-08-07 12:18:26 [INFO] Fold 0: Selected 49 features with threshold 0.001
2025-08-07 12:18:26 [INFO] Fold 0: Best validation C-index = 0.5330
2025-08-07 12:18:37 [INFO] Early stopping triggered. Best score: 0.5230
2025-08-07 12:18:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:37 [INFO] Feature importances range: [-0.0300, 0.0198]
2025-08-07 12:18:37 [INFO] Feature importances mean: -0.0034, median: -0.0037
2025-08-07 12:18:37 [INFO] Fold 1: Selected 56 features with threshold 0.001
2025-08-07 12:18:38 [INFO] Fold 1: Best validation C-index = 0.5170
2025-08-07 12:18:39 [INFO] Early stopping triggered. Best score: 0.5127
2025-08-07 12:18:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:39 [INFO] Feature importances range: [-0.0273, 0.0242]
2025-08-07 12:18:39 [INFO] Feature importances mean: -0.0009, median: 0.0003
2025-08-07 12:18:39 [INFO] Fold 2: Selected 86 features with threshold 0.001
2025-08-07 12:18:40 [INFO] Fold 2: Best validation C-index = 0.4967
2025-08-07 12:18:42 [INFO] Early stopping triggered. Best score: 0.5270
2025-08-07 12:18:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:42 [INFO] Feature importances range: [-0.0281, 0.0253]
2025-08-07 12:18:42 [INFO] Feature importances mean: -0.0026, median: -0.0029
2025-08-07 12:18:42 [INFO] Fold 3: Selected 65 features with threshold 0.001
2025-08-07 12:18:43 [INFO] Fold 3: Best validation C-index = 0.4921
2025-08-07 12:18:53 [INFO] Early stopping triggered. Best score: 0.4903
2025-08-07 12:18:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:53 [INFO] Feature importances range: [-0.0330, 0.0215]
2025-08-07 12:18:53 [INFO] Feature importances mean: -0.0013, median: -0.0010
2025-08-07 12:18:53 [INFO] Fold 4: Selected 81 features with threshold 0.001
2025-08-07 12:18:54 [INFO] Fold 4: Best validation C-index = 0.5442
2025-08-07 12:18:56 [INFO] Early stopping triggered. Best score: 0.4909
2025-08-07 12:18:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:56 [INFO] Feature importances range: [-0.0272, 0.0276]
2025-08-07 12:18:56 [INFO] Feature importances mean: -0.0005, median: -0.0008
2025-08-07 12:18:56 [INFO] Fold 5: Selected 74 features with threshold 0.001
2025-08-07 12:18:57 [INFO] Fold 5: Best validation C-index = 0.4764
2025-08-07 12:18:58 [INFO] Early stopping triggered. Best score: 0.4558
2025-08-07 12:18:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:18:58 [INFO] Feature importances range: [-0.0273, 0.0226]
2025-08-07 12:18:58 [INFO] Feature importances mean: -0.0010, median: -0.0004
2025-08-07 12:18:58 [INFO] Fold 6: Selected 82 features with threshold 0.001
2025-08-07 12:18:59 [INFO] Fold 6: Best validation C-index = 0.4686
2025-08-07 12:19:08 [INFO] Early stopping triggered. Best score: 0.4911
2025-08-07 12:19:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:08 [INFO] Feature importances range: [-0.0256, 0.0258]
2025-08-07 12:19:08 [INFO] Feature importances mean: -0.0018, median: -0.0017
2025-08-07 12:19:08 [INFO] Fold 7: Selected 69 features with threshold 0.001
2025-08-07 12:19:09 [INFO] Fold 7: Best validation C-index = 0.4669
2025-08-07 12:19:11 [INFO] Early stopping triggered. Best score: 0.4695
2025-08-07 12:19:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:11 [INFO] Feature importances range: [-0.0280, 0.0318]
2025-08-07 12:19:11 [INFO] Feature importances mean: -0.0005, median: 0.0002
2025-08-07 12:19:11 [INFO] Fold 8: Selected 83 features with threshold 0.001
2025-08-07 12:19:12 [INFO] Fold 8: Best validation C-index = 0.5195
2025-08-07 12:19:24 [INFO] Early stopping triggered. Best score: 0.5579
2025-08-07 12:19:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:24 [INFO] Feature importances range: [-0.0284, 0.0293]
2025-08-07 12:19:24 [INFO] Feature importances mean: -0.0036, median: -0.0038
2025-08-07 12:19:24 [INFO] Fold 9: Selected 51 features with threshold 0.001
2025-08-07 12:19:25 [INFO] Fold 9: Best validation C-index = 0.4940
2025-08-07 12:19:25 [INFO] Found 59 common features across 10 folds
2025-08-07 12:19:25 [INFO] Common features: ['100A_Flattop_Positive', '100V_V280_L_Z', '200V_V700_H_Y', '300V_V280_H_X', '300V_V280_H_Y', '300V_V700_H_Y', '328A_Flattop_Positive', '420V_V700_L_Y', 'EPI_280V_X1_LOW', 'EPI_700V_Z2_HIGH']...
2025-08-07 12:19:25 [INFO] Trial completed: CV C-index = 0.5008 (from 10 successful folds)
2025-08-07 12:19:25 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:19:25 [INFO] Hidden dims: [3, 33, 6], activation: relu, optimizer: Adam
2025-08-07 12:19:34 [INFO] Early stopping triggered. Best score: 0.5426
2025-08-07 12:19:34 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:34 [INFO] Feature importances range: [-0.0423, 0.0232]
2025-08-07 12:19:34 [INFO] Feature importances mean: -0.0024, median: -0.0022
2025-08-07 12:19:34 [INFO] Fold 0: Selected 65 features with threshold 0.001
2025-08-07 12:19:36 [INFO] Fold 0: Best validation C-index = 0.4906
2025-08-07 12:19:38 [INFO] Early stopping triggered. Best score: 0.4665
2025-08-07 12:19:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:38 [INFO] Feature importances range: [-0.0243, 0.0225]
2025-08-07 12:19:38 [INFO] Feature importances mean: 0.0001, median: 0.0004
2025-08-07 12:19:38 [INFO] Fold 1: Selected 86 features with threshold 0.001
2025-08-07 12:19:39 [INFO] Fold 1: Best validation C-index = 0.5077
2025-08-07 12:19:40 [INFO] Early stopping triggered. Best score: 0.5061
2025-08-07 12:19:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:40 [INFO] Feature importances range: [-0.0287, 0.0300]
2025-08-07 12:19:40 [INFO] Feature importances mean: 0.0004, median: 0.0012
2025-08-07 12:19:40 [INFO] Fold 2: Selected 91 features with threshold 0.001
2025-08-07 12:19:41 [INFO] Fold 2: Best validation C-index = 0.4938
2025-08-07 12:19:43 [INFO] Early stopping triggered. Best score: 0.5221
2025-08-07 12:19:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:43 [INFO] Feature importances range: [-0.0325, 0.0263]
2025-08-07 12:19:43 [INFO] Feature importances mean: -0.0014, median: -0.0017
2025-08-07 12:19:43 [INFO] Fold 3: Selected 69 features with threshold 0.001
2025-08-07 12:19:43 [INFO] Fold 3: Best validation C-index = 0.5141
2025-08-07 12:19:52 [INFO] Early stopping triggered. Best score: 0.5181
2025-08-07 12:19:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:52 [INFO] Feature importances range: [-0.0236, 0.0263]
2025-08-07 12:19:52 [INFO] Feature importances mean: -0.0001, median: 0.0002
2025-08-07 12:19:52 [INFO] Fold 4: Selected 83 features with threshold 0.001
2025-08-07 12:19:53 [INFO] Fold 4: Best validation C-index = 0.5001
2025-08-07 12:19:57 [INFO] Early stopping triggered. Best score: 0.5119
2025-08-07 12:19:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:19:57 [INFO] Feature importances range: [-0.0308, 0.0216]
2025-08-07 12:19:57 [INFO] Feature importances mean: -0.0018, median: -0.0018
2025-08-07 12:19:57 [INFO] Fold 5: Selected 69 features with threshold 0.001
2025-08-07 12:19:58 [INFO] Fold 5: Best validation C-index = 0.5626
2025-08-07 12:20:09 [INFO] Early stopping triggered. Best score: 0.5338
2025-08-07 12:20:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:09 [INFO] Feature importances range: [-0.0299, 0.0340]
2025-08-07 12:20:09 [INFO] Feature importances mean: -0.0025, median: -0.0025
2025-08-07 12:20:09 [INFO] Fold 6: Selected 64 features with threshold 0.001
2025-08-07 12:20:10 [INFO] Fold 6: Best validation C-index = 0.5386
2025-08-07 12:20:11 [INFO] Early stopping triggered. Best score: 0.5227
2025-08-07 12:20:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:11 [INFO] Feature importances range: [-0.0318, 0.0211]
2025-08-07 12:20:11 [INFO] Feature importances mean: -0.0008, median: 0.0006
2025-08-07 12:20:11 [INFO] Fold 7: Selected 87 features with threshold 0.001
2025-08-07 12:20:12 [INFO] Fold 7: Best validation C-index = 0.4684
2025-08-07 12:20:26 [INFO] Early stopping triggered. Best score: 0.4832
2025-08-07 12:20:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:26 [INFO] Feature importances range: [-0.0360, 0.0179]
2025-08-07 12:20:26 [INFO] Feature importances mean: -0.0041, median: -0.0032
2025-08-07 12:20:26 [INFO] Fold 8: Selected 53 features with threshold 0.001
2025-08-07 12:20:26 [INFO] Fold 8: Best validation C-index = 0.5190
2025-08-07 12:20:40 [INFO] Early stopping triggered. Best score: 0.5103
2025-08-07 12:20:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:40 [INFO] Feature importances range: [-0.0336, 0.0223]
2025-08-07 12:20:40 [INFO] Feature importances mean: -0.0054, median: -0.0053
2025-08-07 12:20:40 [INFO] Fold 9: Selected 39 features with threshold 0.001
2025-08-07 12:20:40 [INFO] Fold 9: Best validation C-index = 0.4495
2025-08-07 12:20:40 [INFO] Found 66 common features across 10 folds
2025-08-07 12:20:40 [INFO] Common features: ['100A_IERROR_Peak', '100V_V280_H_Z', '100V_V700_H_Y', '200V_V700_H_X', '300V_V280_H_Y', '300V_V700_L_Y', '300V_V700_L_Z', '328A_IERROR_Peak', '420V_V280_L_Y', '420V_V280_L_Z']...
2025-08-07 12:20:40 [INFO] Trial completed: CV C-index = 0.5044 (from 10 successful folds)
2025-08-07 12:20:40 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:20:40 [INFO] Hidden dims: [51, 32], activation: relu, optimizer: Adagrad
2025-08-07 12:20:41 [INFO] Early stopping triggered. Best score: 0.5074
2025-08-07 12:20:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:41 [INFO] Feature importances range: [-0.0289, 0.0254]
2025-08-07 12:20:41 [INFO] Feature importances mean: -0.0011, median: -0.0010
2025-08-07 12:20:41 [INFO] Fold 0: Selected 67 features with threshold 0.001
2025-08-07 12:20:42 [INFO] Fold 0: Best validation C-index = 0.5116
2025-08-07 12:20:43 [INFO] Early stopping triggered. Best score: 0.4825
2025-08-07 12:20:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:43 [INFO] Feature importances range: [-0.0252, 0.0219]
2025-08-07 12:20:43 [INFO] Feature importances mean: -0.0012, median: -0.0011
2025-08-07 12:20:43 [INFO] Fold 1: Selected 68 features with threshold 0.001
2025-08-07 12:20:45 [INFO] Fold 1: Best validation C-index = 0.4853
2025-08-07 12:20:53 [INFO] Early stopping triggered. Best score: 0.4678
2025-08-07 12:20:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:53 [INFO] Feature importances range: [-0.0248, 0.0252]
2025-08-07 12:20:53 [INFO] Feature importances mean: 0.0009, median: 0.0011
2025-08-07 12:20:53 [INFO] Fold 2: Selected 91 features with threshold 0.001
2025-08-07 12:20:54 [INFO] Fold 2: Best validation C-index = 0.5154
2025-08-07 12:20:54 [INFO] Early stopping triggered. Best score: 0.4833
2025-08-07 12:20:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:54 [INFO] Feature importances range: [-0.0234, 0.0246]
2025-08-07 12:20:54 [INFO] Feature importances mean: 0.0008, median: 0.0013
2025-08-07 12:20:54 [INFO] Fold 3: Selected 91 features with threshold 0.001
2025-08-07 12:20:55 [INFO] Fold 3: Best validation C-index = 0.4733
2025-08-07 12:20:56 [INFO] Early stopping triggered. Best score: 0.5160
2025-08-07 12:20:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:56 [INFO] Feature importances range: [-0.0273, 0.0243]
2025-08-07 12:20:56 [INFO] Feature importances mean: 0.0001, median: -0.0005
2025-08-07 12:20:56 [INFO] Fold 4: Selected 79 features with threshold 0.001
2025-08-07 12:20:57 [INFO] Fold 4: Best validation C-index = 0.5134
2025-08-07 12:20:58 [INFO] Early stopping triggered. Best score: 0.4563
2025-08-07 12:20:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:20:58 [INFO] Feature importances range: [-0.0269, 0.0288]
2025-08-07 12:20:58 [INFO] Feature importances mean: -0.0001, median: -0.0001
2025-08-07 12:20:58 [INFO] Fold 5: Selected 79 features with threshold 0.001
2025-08-07 12:20:59 [INFO] Fold 5: Best validation C-index = 0.4714
2025-08-07 12:21:07 [INFO] Early stopping triggered. Best score: 0.5140
2025-08-07 12:21:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:07 [INFO] Feature importances range: [-0.0296, 0.0295]
2025-08-07 12:21:07 [INFO] Feature importances mean: 0.0003, median: 0.0003
2025-08-07 12:21:07 [INFO] Fold 6: Selected 85 features with threshold 0.001
2025-08-07 12:21:08 [INFO] Fold 6: Best validation C-index = 0.5110
2025-08-07 12:21:09 [INFO] Early stopping triggered. Best score: 0.5348
2025-08-07 12:21:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:09 [INFO] Feature importances range: [-0.0275, 0.0273]
2025-08-07 12:21:09 [INFO] Feature importances mean: -0.0008, median: -0.0015
2025-08-07 12:21:09 [INFO] Fold 7: Selected 72 features with threshold 0.001
2025-08-07 12:21:09 [INFO] Fold 7: Best validation C-index = 0.5328
2025-08-07 12:21:10 [INFO] Early stopping triggered. Best score: 0.4830
2025-08-07 12:21:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:10 [INFO] Feature importances range: [-0.0301, 0.0287]
2025-08-07 12:21:10 [INFO] Feature importances mean: -0.0011, median: -0.0010
2025-08-07 12:21:10 [INFO] Fold 8: Selected 73 features with threshold 0.001
2025-08-07 12:21:11 [INFO] Fold 8: Best validation C-index = 0.5012
2025-08-07 12:21:12 [INFO] Early stopping triggered. Best score: 0.5040
2025-08-07 12:21:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:12 [INFO] Feature importances range: [-0.0263, 0.0281]
2025-08-07 12:21:12 [INFO] Feature importances mean: 0.0001, median: -0.0000
2025-08-07 12:21:12 [INFO] Fold 9: Selected 82 features with threshold 0.001
2025-08-07 12:21:13 [INFO] Fold 9: Best validation C-index = 0.5502
2025-08-07 12:21:13 [INFO] Found 88 common features across 10 folds
2025-08-07 12:21:13 [INFO] Common features: ['100A_IERROR_RMS', '100V_V280_H_X', '100V_V700_H_Z', '100V_V700_L_X', '100V_V700_L_Z', '200V_V280_H_Z', '200V_V280_L_X', '200V_V280_L_Y', '200V_V700_H_X', '200V_V700_H_Y']...
2025-08-07 12:21:13 [INFO] Trial completed: CV C-index = 0.5066 (from 10 successful folds)
2025-08-07 12:21:13 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:21:13 [INFO] Hidden dims: [44, 59, 36, 62], activation: tanh, optimizer: SGD
2025-08-07 12:21:40 [INFO] Early stopping triggered. Best score: 0.5208
2025-08-07 12:21:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:40 [INFO] Feature importances range: [-0.4795, -0.4292]
2025-08-07 12:21:40 [INFO] Feature importances mean: -0.4544, median: -0.4545
2025-08-07 12:21:40 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:21:40 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:21:40 [INFO] Fold 0: Best validation C-index = 0.4904
2025-08-07 12:21:54 [INFO] Early stopping triggered. Best score: 0.5197
2025-08-07 12:21:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:21:54 [INFO] Feature importances range: [-0.2143, -0.1560]
2025-08-07 12:21:54 [INFO] Feature importances mean: -0.1867, median: -0.1872
2025-08-07 12:21:54 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:21:54 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:21:54 [INFO] Fold 1: Best validation C-index = 0.5411
2025-08-07 12:22:07 [INFO] Early stopping triggered. Best score: 0.4791
2025-08-07 12:22:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:07 [INFO] Feature importances range: [-0.2407, -0.1775]
2025-08-07 12:22:07 [INFO] Feature importances mean: -0.2081, median: -0.2092
2025-08-07 12:22:07 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:07 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:22:08 [INFO] Fold 2: Best validation C-index = 0.5153
2025-08-07 12:22:09 [INFO] Early stopping triggered. Best score: 0.4793
2025-08-07 12:22:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:09 [INFO] Feature importances range: [-0.0949, -0.0455]
2025-08-07 12:22:09 [INFO] Feature importances mean: -0.0729, median: -0.0730
2025-08-07 12:22:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:09 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:22:10 [INFO] Fold 3: Best validation C-index = 0.4879
2025-08-07 12:22:11 [INFO] Early stopping triggered. Best score: 0.5248
2025-08-07 12:22:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:11 [INFO] Feature importances range: [-0.1001, -0.0462]
2025-08-07 12:22:11 [INFO] Feature importances mean: -0.0745, median: -0.0749
2025-08-07 12:22:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:11 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:22:12 [INFO] Fold 4: Best validation C-index = 0.4319
2025-08-07 12:22:38 [INFO] Early stopping triggered. Best score: 0.5250
2025-08-07 12:22:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:38 [INFO] Feature importances range: [-0.5100, -0.4417]
2025-08-07 12:22:38 [INFO] Feature importances mean: -0.4712, median: -0.4712
2025-08-07 12:22:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:38 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:22:39 [INFO] Fold 5: Best validation C-index = 0.4735
2025-08-07 12:22:41 [INFO] Early stopping triggered. Best score: 0.4943
2025-08-07 12:22:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:41 [INFO] Feature importances range: [-0.1219, -0.0596]
2025-08-07 12:22:41 [INFO] Feature importances mean: -0.0933, median: -0.0937
2025-08-07 12:22:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:41 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:22:41 [INFO] Fold 6: Best validation C-index = 0.4876
2025-08-07 12:22:43 [INFO] Early stopping triggered. Best score: 0.4699
2025-08-07 12:22:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:43 [INFO] Feature importances range: [-0.1071, -0.0525]
2025-08-07 12:22:43 [INFO] Feature importances mean: -0.0796, median: -0.0800
2025-08-07 12:22:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:43 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:22:43 [INFO] Fold 7: Best validation C-index = 0.5269
2025-08-07 12:22:54 [INFO] Early stopping triggered. Best score: 0.5221
2025-08-07 12:22:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:54 [INFO] Feature importances range: [-0.1102, -0.0586]
2025-08-07 12:22:54 [INFO] Feature importances mean: -0.0846, median: -0.0840
2025-08-07 12:22:54 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:54 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:22:54 [INFO] Fold 8: Best validation C-index = 0.5553
2025-08-07 12:22:56 [INFO] Early stopping triggered. Best score: 0.5354
2025-08-07 12:22:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:56 [INFO] Feature importances range: [-0.1042, -0.0557]
2025-08-07 12:22:56 [INFO] Feature importances mean: -0.0828, median: -0.0823
2025-08-07 12:22:56 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:22:56 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:22:56 [INFO] Fold 9: Best validation C-index = 0.4961
2025-08-07 12:22:56 [INFO] Found 10 common features across 10 folds
2025-08-07 12:22:56 [INFO] Common features: ['BUS_VOLTAGE_VZ_LOW', 'Shot_Shot_Stab', '100V_V700_H_X', 'Z_CLOSED_LOOP_V700_L_Y', 'PULSE_280V_Z1_LOW', 'Z_CLOSED_LOOP_V280_L_X', 'EPI_700V_Z1_HIGH', 'Heat_Sink_Temp_Amplifier_Initial', 'PULSE_700V_Y2_HIGH', 'EPI_700V_Z2_HIGH']...
2025-08-07 12:22:56 [INFO] Trial completed: CV C-index = 0.5006 (from 10 successful folds)
2025-08-07 12:22:56 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:22:56 [INFO] Hidden dims: [46], activation: sigmoid, optimizer: SGD
2025-08-07 12:22:57 [INFO] Early stopping triggered. Best score: 0.5242
2025-08-07 12:22:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:57 [INFO] Feature importances range: [-0.0327, 0.0202]
2025-08-07 12:22:57 [INFO] Feature importances mean: -0.0086, median: -0.0085
2025-08-07 12:22:57 [INFO] Fold 0: Selected 34 features with threshold 0.001
2025-08-07 12:22:57 [INFO] Fold 0: Best validation C-index = 0.5184
2025-08-07 12:22:58 [INFO] Early stopping triggered. Best score: 0.5212
2025-08-07 12:22:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:22:58 [INFO] Feature importances range: [-0.0271, 0.0229]
2025-08-07 12:22:58 [INFO] Feature importances mean: -0.0071, median: -0.0076
2025-08-07 12:22:58 [INFO] Fold 1: Selected 34 features with threshold 0.001
2025-08-07 12:22:59 [INFO] Fold 1: Best validation C-index = 0.5307
2025-08-07 12:23:08 [INFO] Early stopping triggered. Best score: 0.4681
2025-08-07 12:23:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:08 [INFO] Feature importances range: [-0.0353, 0.0178]
2025-08-07 12:23:08 [INFO] Feature importances mean: -0.0069, median: -0.0057
2025-08-07 12:23:08 [INFO] Fold 2: Selected 41 features with threshold 0.001
2025-08-07 12:23:08 [INFO] Fold 2: Best validation C-index = 0.5140
2025-08-07 12:23:09 [INFO] Early stopping triggered. Best score: 0.5014
2025-08-07 12:23:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:09 [INFO] Feature importances range: [-0.0415, 0.0264]
2025-08-07 12:23:09 [INFO] Feature importances mean: -0.0080, median: -0.0069
2025-08-07 12:23:09 [INFO] Fold 3: Selected 37 features with threshold 0.001
2025-08-07 12:23:09 [INFO] Fold 3: Best validation C-index = 0.4945
2025-08-07 12:23:10 [INFO] Early stopping triggered. Best score: 0.5331
2025-08-07 12:23:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:10 [INFO] Feature importances range: [-0.0334, 0.0225]
2025-08-07 12:23:10 [INFO] Feature importances mean: -0.0076, median: -0.0077
2025-08-07 12:23:10 [INFO] Fold 4: Selected 36 features with threshold 0.001
2025-08-07 12:23:11 [INFO] Fold 4: Best validation C-index = 0.4889
2025-08-07 12:23:11 [INFO] Early stopping triggered. Best score: 0.5143
2025-08-07 12:23:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:11 [INFO] Feature importances range: [-0.0307, 0.0136]
2025-08-07 12:23:11 [INFO] Feature importances mean: -0.0084, median: -0.0086
2025-08-07 12:23:11 [INFO] Fold 5: Selected 34 features with threshold 0.001
2025-08-07 12:23:12 [INFO] Fold 5: Best validation C-index = 0.4650
2025-08-07 12:23:13 [INFO] Early stopping triggered. Best score: 0.4516
2025-08-07 12:23:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:13 [INFO] Feature importances range: [-0.0331, 0.0205]
2025-08-07 12:23:13 [INFO] Feature importances mean: -0.0084, median: -0.0078
2025-08-07 12:23:13 [INFO] Fold 6: Selected 33 features with threshold 0.001
2025-08-07 12:23:13 [INFO] Fold 6: Best validation C-index = 0.4571
2025-08-07 12:23:23 [INFO] Early stopping triggered. Best score: 0.5188
2025-08-07 12:23:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:23 [INFO] Feature importances range: [-0.0467, 0.0046]
2025-08-07 12:23:23 [INFO] Feature importances mean: -0.0168, median: -0.0156
2025-08-07 12:23:23 [INFO] Fold 7: Selected 5 features with threshold 0.001
2025-08-07 12:23:23 [INFO] Fold 7: Best validation C-index = 0.4631
2025-08-07 12:23:24 [INFO] Early stopping triggered. Best score: 0.5406
2025-08-07 12:23:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:24 [INFO] Feature importances range: [-0.0393, 0.0226]
2025-08-07 12:23:24 [INFO] Feature importances mean: -0.0073, median: -0.0067
2025-08-07 12:23:24 [INFO] Fold 8: Selected 32 features with threshold 0.001
2025-08-07 12:23:25 [INFO] Fold 8: Best validation C-index = 0.5135
2025-08-07 12:23:25 [INFO] Early stopping triggered. Best score: 0.5393
2025-08-07 12:23:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:25 [INFO] Feature importances range: [-0.0339, 0.0218]
2025-08-07 12:23:25 [INFO] Feature importances mean: -0.0085, median: -0.0078
2025-08-07 12:23:25 [INFO] Fold 9: Selected 32 features with threshold 0.001
2025-08-07 12:23:26 [INFO] Fold 9: Best validation C-index = 0.5607
2025-08-07 12:23:26 [INFO] Found 43 common features across 10 folds
2025-08-07 12:23:26 [INFO] Common features: ['100V_V280_L_X', '200V_V700_L_Y', '300V_V700_L_Y', 'BUS_VOLTAGE_VY_LOW', 'EPI_280V_X2_LOW', 'EPI_280V_Z1_LOW', 'EPI_700V_Y1_LOW', 'EPI_IERROR_Peak', 'PULSE_280V_Y2_HIGH', 'PULSE_280V_Y2_LOW']...
2025-08-07 12:23:26 [INFO] Trial completed: CV C-index = 0.5006 (from 10 successful folds)
2025-08-07 12:23:26 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:23:26 [INFO] Hidden dims: [26, 6, 59, 1], activation: selu, optimizer: Adagrad
2025-08-07 12:23:28 [INFO] Early stopping triggered. Best score: 0.5199
2025-08-07 12:23:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:28 [INFO] Feature importances range: [-1.1079, -1.0988]
2025-08-07 12:23:28 [INFO] Feature importances mean: -1.1027, median: -1.1026
2025-08-07 12:23:28 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:28 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:23:28 [INFO] Fold 0: Best validation C-index = 0.5456
2025-08-07 12:23:39 [INFO] Early stopping triggered. Best score: 0.5278
2025-08-07 12:23:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:39 [INFO] Feature importances range: [-1.0918, -1.0858]
2025-08-07 12:23:39 [INFO] Feature importances mean: -1.0890, median: -1.0891
2025-08-07 12:23:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:39 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:23:39 [INFO] Fold 1: Best validation C-index = 0.4797
2025-08-07 12:23:41 [INFO] Early stopping triggered. Best score: 0.5596
2025-08-07 12:23:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:41 [INFO] Feature importances range: [-1.1024, -1.0939]
2025-08-07 12:23:41 [INFO] Feature importances mean: -1.0983, median: -1.0984
2025-08-07 12:23:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:41 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:23:42 [INFO] Fold 2: Best validation C-index = 0.5786
2025-08-07 12:23:53 [INFO] Early stopping triggered. Best score: 0.5092
2025-08-07 12:23:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:53 [INFO] Feature importances range: [-1.1843, -1.1791]
2025-08-07 12:23:53 [INFO] Feature importances mean: -1.1820, median: -1.1821
2025-08-07 12:23:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:53 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:23:53 [INFO] Fold 3: Best validation C-index = 0.4855
2025-08-07 12:23:55 [INFO] Early stopping triggered. Best score: 0.5136
2025-08-07 12:23:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:55 [INFO] Feature importances range: [-1.0930, -1.0852]
2025-08-07 12:23:55 [INFO] Feature importances mean: -1.0889, median: -1.0889
2025-08-07 12:23:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:55 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:23:56 [INFO] Fold 4: Best validation C-index = 0.5142
2025-08-07 12:23:58 [INFO] Early stopping triggered. Best score: 0.5384
2025-08-07 12:23:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:23:58 [INFO] Feature importances range: [-1.0925, -1.0856]
2025-08-07 12:23:58 [INFO] Feature importances mean: -1.0889, median: -1.0887
2025-08-07 12:23:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:23:58 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:23:58 [INFO] Fold 5: Best validation C-index = 0.5205
2025-08-07 12:24:08 [INFO] Early stopping triggered. Best score: 0.5320
2025-08-07 12:24:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:08 [INFO] Feature importances range: [-1.1098, -1.1036]
2025-08-07 12:24:08 [INFO] Feature importances mean: -1.1071, median: -1.1073
2025-08-07 12:24:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:08 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:24:09 [INFO] Fold 6: Best validation C-index = 0.5602
2025-08-07 12:24:11 [INFO] Early stopping triggered. Best score: 0.5609
2025-08-07 12:24:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:11 [INFO] Feature importances range: [-1.1160, -1.1086]
2025-08-07 12:24:11 [INFO] Feature importances mean: -1.1112, median: -1.1111
2025-08-07 12:24:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:11 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:24:11 [INFO] Fold 7: Best validation C-index = 0.4979
2025-08-07 12:24:13 [INFO] Early stopping triggered. Best score: 0.5489
2025-08-07 12:24:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:13 [INFO] Feature importances range: [-1.0938, -1.0852]
2025-08-07 12:24:13 [INFO] Feature importances mean: -1.0892, median: -1.0892
2025-08-07 12:24:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:13 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:24:14 [INFO] Fold 8: Best validation C-index = 0.5392
2025-08-07 12:24:23 [INFO] Early stopping triggered. Best score: 0.5171
2025-08-07 12:24:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:23 [INFO] Feature importances range: [-1.0883, -1.0803]
2025-08-07 12:24:23 [INFO] Feature importances mean: -1.0843, median: -1.0843
2025-08-07 12:24:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:23 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:24:24 [INFO] Fold 9: Best validation C-index = 0.5027
2025-08-07 12:24:24 [INFO] Found 5 common features across 10 folds
2025-08-07 12:24:24 [INFO] Common features: ['PULSE_280V_Y2_LOW', 'PULSE_280V_Z2_HIGH', '200V_V700_L_Y', '328A_IERROR_Peak', 'PULSE_280V_Z1_HIGH']...
2025-08-07 12:24:24 [INFO] Trial completed: CV C-index = 0.5224 (from 10 successful folds)
2025-08-07 12:24:24 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:24:24 [INFO] Hidden dims: [27, 3, 64, 1], activation: selu, optimizer: Adagrad
2025-08-07 12:24:26 [INFO] Early stopping triggered. Best score: 0.5156
2025-08-07 12:24:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:26 [INFO] Feature importances range: [-1.0418, -1.0377]
2025-08-07 12:24:26 [INFO] Feature importances mean: -1.0398, median: -1.0398
2025-08-07 12:24:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:26 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:24:26 [INFO] Fold 0: Best validation C-index = 0.4995
2025-08-07 12:24:28 [INFO] Early stopping triggered. Best score: 0.5420
2025-08-07 12:24:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:28 [INFO] Feature importances range: [-1.0493, -1.0445]
2025-08-07 12:24:28 [INFO] Feature importances mean: -1.0469, median: -1.0469
2025-08-07 12:24:28 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:28 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:24:28 [INFO] Fold 1: Best validation C-index = 0.4805
2025-08-07 12:24:38 [INFO] Early stopping triggered. Best score: 0.5495
2025-08-07 12:24:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:38 [INFO] Feature importances range: [-1.0387, -1.0341]
2025-08-07 12:24:38 [INFO] Feature importances mean: -1.0361, median: -1.0360
2025-08-07 12:24:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:38 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:24:39 [INFO] Fold 2: Best validation C-index = 0.5268
2025-08-07 12:24:41 [INFO] Early stopping triggered. Best score: 0.5551
2025-08-07 12:24:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:41 [INFO] Feature importances range: [-1.0348, -1.0307]
2025-08-07 12:24:41 [INFO] Feature importances mean: -1.0323, median: -1.0323
2025-08-07 12:24:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:41 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:24:41 [INFO] Fold 3: Best validation C-index = 0.5412
2025-08-07 12:24:44 [INFO] Early stopping triggered. Best score: 0.5221
2025-08-07 12:24:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:44 [INFO] Feature importances range: [-1.0592, -1.0550]
2025-08-07 12:24:44 [INFO] Feature importances mean: -1.0569, median: -1.0570
2025-08-07 12:24:44 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:44 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:24:44 [INFO] Fold 4: Best validation C-index = 0.4973
2025-08-07 12:24:55 [INFO] Early stopping triggered. Best score: 0.5198
2025-08-07 12:24:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:55 [INFO] Feature importances range: [-1.0554, -1.0515]
2025-08-07 12:24:55 [INFO] Feature importances mean: -1.0537, median: -1.0536
2025-08-07 12:24:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:55 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:24:55 [INFO] Fold 5: Best validation C-index = 0.5051
2025-08-07 12:24:57 [INFO] Early stopping triggered. Best score: 0.5237
2025-08-07 12:24:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:24:57 [INFO] Feature importances range: [-1.0390, -1.0339]
2025-08-07 12:24:57 [INFO] Feature importances mean: -1.0362, median: -1.0361
2025-08-07 12:24:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:24:57 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:24:58 [INFO] Fold 6: Best validation C-index = 0.4785
2025-08-07 12:25:08 [INFO] Early stopping triggered. Best score: 0.5196
2025-08-07 12:25:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:08 [INFO] Feature importances range: [-1.0560, -1.0520]
2025-08-07 12:25:08 [INFO] Feature importances mean: -1.0537, median: -1.0537
2025-08-07 12:25:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:08 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:25:09 [INFO] Fold 7: Best validation C-index = 0.4961
2025-08-07 12:25:11 [INFO] Early stopping triggered. Best score: 0.5079
2025-08-07 12:25:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:11 [INFO] Feature importances range: [-1.0381, -1.0344]
2025-08-07 12:25:11 [INFO] Feature importances mean: -1.0362, median: -1.0362
2025-08-07 12:25:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:11 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:25:11 [INFO] Fold 8: Best validation C-index = 0.5233
2025-08-07 12:25:13 [INFO] Early stopping triggered. Best score: 0.5113
2025-08-07 12:25:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:13 [INFO] Feature importances range: [-1.0349, -1.0303]
2025-08-07 12:25:13 [INFO] Feature importances mean: -1.0324, median: -1.0324
2025-08-07 12:25:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:13 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:25:15 [INFO] Fold 9: Best validation C-index = 0.4931
2025-08-07 12:25:15 [INFO] Found 10 common features across 10 folds
2025-08-07 12:25:15 [INFO] Common features: ['300V_V700_L_Y', 'X_CLOSED_LOOP_V280_H_Y', 'X_CLOSED_LOOP_V700_L_Z', '420V_V700_L_Z', '200V_V280_H_X', 'Z_CLOSED_LOOP_V700_H_Y', 'Hysteresis', 'EPI_700V_Z2_HIGH', 'Z_STATUS_280V_Y1_High', 'PULSE_280V_Y2_HIGH']...
2025-08-07 12:25:15 [INFO] Trial completed: CV C-index = 0.5041 (from 10 successful folds)
2025-08-07 12:25:15 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:25:15 [INFO] Hidden dims: [32, 1, 59, 1], activation: selu, optimizer: Adagrad
2025-08-07 12:25:24 [INFO] Early stopping triggered. Best score: 0.5568
2025-08-07 12:25:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:24 [INFO] Feature importances range: [-1.2293, -1.1735]
2025-08-07 12:25:24 [INFO] Feature importances mean: -1.2027, median: -1.2034
2025-08-07 12:25:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:24 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:25:25 [INFO] Fold 0: Best validation C-index = 0.5159
2025-08-07 12:25:27 [INFO] Early stopping triggered. Best score: 0.5254
2025-08-07 12:25:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:27 [INFO] Feature importances range: [-1.1147, -1.0681]
2025-08-07 12:25:27 [INFO] Feature importances mean: -1.0934, median: -1.0932
2025-08-07 12:25:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:27 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:25:27 [INFO] Fold 1: Best validation C-index = 0.4882
2025-08-07 12:25:37 [INFO] Early stopping triggered. Best score: 0.5455
2025-08-07 12:25:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:37 [INFO] Feature importances range: [-1.2509, -1.2057]
2025-08-07 12:25:37 [INFO] Feature importances mean: -1.2263, median: -1.2256
2025-08-07 12:25:37 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:37 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:25:37 [INFO] Fold 2: Best validation C-index = 0.5252
2025-08-07 12:25:40 [INFO] Early stopping triggered. Best score: 0.5097
2025-08-07 12:25:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:40 [INFO] Feature importances range: [-1.2183, -1.1751]
2025-08-07 12:25:40 [INFO] Feature importances mean: -1.1928, median: -1.1922
2025-08-07 12:25:40 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:40 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:25:40 [INFO] Fold 3: Best validation C-index = 0.4949
2025-08-07 12:25:42 [INFO] Early stopping triggered. Best score: 0.5269
2025-08-07 12:25:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:42 [INFO] Feature importances range: [-1.1147, -1.0656]
2025-08-07 12:25:42 [INFO] Feature importances mean: -1.0925, median: -1.0922
2025-08-07 12:25:42 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:42 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:25:43 [INFO] Fold 4: Best validation C-index = 0.4637
2025-08-07 12:25:52 [INFO] Early stopping triggered. Best score: 0.5123
2025-08-07 12:25:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:52 [INFO] Feature importances range: [-1.1570, -1.1052]
2025-08-07 12:25:52 [INFO] Feature importances mean: -1.1270, median: -1.1267
2025-08-07 12:25:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:52 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:25:53 [INFO] Fold 5: Best validation C-index = 0.5057
2025-08-07 12:25:56 [INFO] Early stopping triggered. Best score: 0.5337
2025-08-07 12:25:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:56 [INFO] Feature importances range: [-1.2290, -1.1800]
2025-08-07 12:25:56 [INFO] Feature importances mean: -1.2035, median: -1.2029
2025-08-07 12:25:56 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:56 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:25:56 [INFO] Fold 6: Best validation C-index = 0.4911
2025-08-07 12:25:58 [INFO] Early stopping triggered. Best score: 0.5641
2025-08-07 12:25:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:25:58 [INFO] Feature importances range: [-1.2149, -1.1715]
2025-08-07 12:25:58 [INFO] Feature importances mean: -1.1933, median: -1.1940
2025-08-07 12:25:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:25:58 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:25:59 [INFO] Fold 7: Best validation C-index = 0.5075
2025-08-07 12:26:09 [INFO] Early stopping triggered. Best score: 0.5346
2025-08-07 12:26:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:09 [INFO] Feature importances range: [-1.1239, -1.0814]
2025-08-07 12:26:09 [INFO] Feature importances mean: -1.1047, median: -1.1049
2025-08-07 12:26:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:09 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:26:09 [INFO] Fold 8: Best validation C-index = 0.5214
2025-08-07 12:26:12 [INFO] Early stopping triggered. Best score: 0.5402
2025-08-07 12:26:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:12 [INFO] Feature importances range: [-1.1958, -1.1433]
2025-08-07 12:26:12 [INFO] Feature importances mean: -1.1713, median: -1.1715
2025-08-07 12:26:12 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:12 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:26:12 [INFO] Fold 9: Best validation C-index = 0.5400
2025-08-07 12:26:12 [INFO] Found 10 common features across 10 folds
2025-08-07 12:26:12 [INFO] Common features: ['300V_V700_L_Z', 'Y_CLOSED_LOOP_V700_L_Z', '300V_V280_H_Z', '300V_V280_L_Y', 'X_CLOSED_LOOP_V280_L_Z', 'IERROR_Peak_Single_Axis', 'BUS_VOLTAGE_VY_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_Y', '420V_V700_L_Z', 'X_CLOSED_LOOP_V700_H_Z']...
2025-08-07 12:26:12 [INFO] Trial completed: CV C-index = 0.5053 (from 10 successful folds)
2025-08-07 12:26:12 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:26:12 [INFO] Hidden dims: [18, 16, 46], activation: selu, optimizer: Adagrad
2025-08-07 12:26:14 [INFO] Early stopping triggered. Best score: 0.5778
2025-08-07 12:26:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:14 [INFO] Feature importances range: [-0.2303, -0.1840]
2025-08-07 12:26:14 [INFO] Feature importances mean: -0.2109, median: -0.2116
2025-08-07 12:26:14 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:14 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:26:14 [INFO] Fold 0: Best validation C-index = 0.5292
2025-08-07 12:26:24 [INFO] Early stopping triggered. Best score: 0.5279
2025-08-07 12:26:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:24 [INFO] Feature importances range: [-0.2915, -0.2509]
2025-08-07 12:26:24 [INFO] Feature importances mean: -0.2732, median: -0.2735
2025-08-07 12:26:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:24 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:26:24 [INFO] Fold 1: Best validation C-index = 0.5316
2025-08-07 12:26:26 [INFO] Early stopping triggered. Best score: 0.5367
2025-08-07 12:26:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:26 [INFO] Feature importances range: [-0.2371, -0.1807]
2025-08-07 12:26:26 [INFO] Feature importances mean: -0.2140, median: -0.2139
2025-08-07 12:26:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:26 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:26:26 [INFO] Fold 2: Best validation C-index = 0.5102
2025-08-07 12:26:28 [INFO] Early stopping triggered. Best score: 0.5549
2025-08-07 12:26:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:28 [INFO] Feature importances range: [-0.2342, -0.1887]
2025-08-07 12:26:28 [INFO] Feature importances mean: -0.2121, median: -0.2117
2025-08-07 12:26:28 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:28 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:26:28 [INFO] Fold 3: Best validation C-index = 0.5358
2025-08-07 12:26:38 [INFO] Early stopping triggered. Best score: 0.5347
2025-08-07 12:26:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:38 [INFO] Feature importances range: [-0.2611, -0.2151]
2025-08-07 12:26:38 [INFO] Feature importances mean: -0.2385, median: -0.2374
2025-08-07 12:26:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:38 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:26:39 [INFO] Fold 4: Best validation C-index = 0.4847
2025-08-07 12:26:40 [INFO] Early stopping triggered. Best score: 0.5452
2025-08-07 12:26:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:40 [INFO] Feature importances range: [-0.2341, -0.1795]
2025-08-07 12:26:40 [INFO] Feature importances mean: -0.2105, median: -0.2117
2025-08-07 12:26:40 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:40 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:26:41 [INFO] Fold 5: Best validation C-index = 0.5067
2025-08-07 12:26:53 [INFO] Early stopping triggered. Best score: 0.5289
2025-08-07 12:26:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:53 [INFO] Feature importances range: [-0.3920, -0.3543]
2025-08-07 12:26:53 [INFO] Feature importances mean: -0.3725, median: -0.3728
2025-08-07 12:26:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:53 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:26:53 [INFO] Fold 6: Best validation C-index = 0.5082
2025-08-07 12:26:55 [INFO] Early stopping triggered. Best score: 0.5270
2025-08-07 12:26:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:55 [INFO] Feature importances range: [-0.2316, -0.1785]
2025-08-07 12:26:55 [INFO] Feature importances mean: -0.2087, median: -0.2085
2025-08-07 12:26:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:55 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:26:55 [INFO] Fold 7: Best validation C-index = 0.4746
2025-08-07 12:26:57 [INFO] Early stopping triggered. Best score: 0.5207
2025-08-07 12:26:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:57 [INFO] Feature importances range: [-0.2357, -0.1885]
2025-08-07 12:26:57 [INFO] Feature importances mean: -0.2095, median: -0.2091
2025-08-07 12:26:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:57 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:26:57 [INFO] Fold 8: Best validation C-index = 0.4897
2025-08-07 12:26:59 [INFO] Early stopping triggered. Best score: 0.5460
2025-08-07 12:26:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:26:59 [INFO] Feature importances range: [-0.2525, -0.2038]
2025-08-07 12:26:59 [INFO] Feature importances mean: -0.2279, median: -0.2274
2025-08-07 12:26:59 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:26:59 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:26:59 [INFO] Fold 9: Best validation C-index = 0.4833
2025-08-07 12:26:59 [INFO] Found 10 common features across 10 folds
2025-08-07 12:26:59 [INFO] Common features: ['EPI_700V_X1_HIGH', 'EPI_280V_X1_LOW', 'EPI_700V_Z2_HIGH', '100A_Flattop_Positive', '300V_V700_L_Z', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'BUS_VOLTAGE_VZ_LOW', '100V_V700_H_X', '200V_V700_L_Z', 'Y_CLOSED_LOOP_V700_H_Z']...
2025-08-07 12:26:59 [INFO] Trial completed: CV C-index = 0.5054 (from 10 successful folds)
2025-08-07 12:26:59 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:26:59 [INFO] Hidden dims: [62, 46], activation: selu, optimizer: Adagrad
2025-08-07 12:27:09 [INFO] Early stopping triggered. Best score: 0.5246
2025-08-07 12:27:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:09 [INFO] Feature importances range: [-0.0583, 0.1535]
2025-08-07 12:27:09 [INFO] Feature importances mean: 0.0100, median: 0.0030
2025-08-07 12:27:09 [INFO] Fold 0: Selected 99 features with threshold 0.001
2025-08-07 12:27:09 [INFO] Fold 0: Best validation C-index = 0.5128
2025-08-07 12:27:11 [INFO] Early stopping triggered. Best score: 0.5110
2025-08-07 12:27:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:11 [INFO] Feature importances range: [-0.0560, 0.1389]
2025-08-07 12:27:11 [INFO] Feature importances mean: 0.0120, median: 0.0043
2025-08-07 12:27:11 [INFO] Fold 1: Selected 105 features with threshold 0.001
2025-08-07 12:27:12 [INFO] Fold 1: Best validation C-index = 0.4980
2025-08-07 12:27:25 [INFO] Early stopping triggered. Best score: 0.5735
2025-08-07 12:27:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:25 [INFO] Feature importances range: [-0.0765, 0.3656]
2025-08-07 12:27:25 [INFO] Feature importances mean: 0.0541, median: 0.0215
2025-08-07 12:27:25 [INFO] Fold 2: Selected 122 features with threshold 0.001
2025-08-07 12:27:26 [INFO] Fold 2: Best validation C-index = 0.5122
2025-08-07 12:27:29 [INFO] Early stopping triggered. Best score: 0.5245
2025-08-07 12:27:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:29 [INFO] Feature importances range: [-0.0491, 0.1600]
2025-08-07 12:27:29 [INFO] Feature importances mean: 0.0060, median: -0.0002
2025-08-07 12:27:29 [INFO] Fold 3: Selected 86 features with threshold 0.001
2025-08-07 12:27:29 [INFO] Fold 3: Best validation C-index = 0.5003
2025-08-07 12:27:38 [INFO] Early stopping triggered. Best score: 0.5737
2025-08-07 12:27:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:38 [INFO] Feature importances range: [-0.0698, 0.1584]
2025-08-07 12:27:38 [INFO] Feature importances mean: 0.0027, median: -0.0035
2025-08-07 12:27:38 [INFO] Fold 4: Selected 78 features with threshold 0.001
2025-08-07 12:27:39 [INFO] Fold 4: Best validation C-index = 0.5564
2025-08-07 12:27:41 [INFO] Early stopping triggered. Best score: 0.5121
2025-08-07 12:27:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:41 [INFO] Feature importances range: [-0.0676, 0.1465]
2025-08-07 12:27:41 [INFO] Feature importances mean: 0.0029, median: 0.0004
2025-08-07 12:27:41 [INFO] Fold 5: Selected 88 features with threshold 0.001
2025-08-07 12:27:42 [INFO] Fold 5: Best validation C-index = 0.5037
2025-08-07 12:27:52 [INFO] Early stopping triggered. Best score: 0.5581
2025-08-07 12:27:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:52 [INFO] Feature importances range: [-0.3286, 0.2071]
2025-08-07 12:27:52 [INFO] Feature importances mean: 0.0162, median: 0.0063
2025-08-07 12:27:52 [INFO] Fold 6: Selected 107 features with threshold 0.001
2025-08-07 12:27:53 [INFO] Fold 6: Best validation C-index = 0.4961
2025-08-07 12:27:55 [INFO] Early stopping triggered. Best score: 0.5178
2025-08-07 12:27:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:55 [INFO] Feature importances range: [-0.0914, 0.1216]
2025-08-07 12:27:55 [INFO] Feature importances mean: 0.0029, median: -0.0002
2025-08-07 12:27:55 [INFO] Fold 7: Selected 87 features with threshold 0.001
2025-08-07 12:27:55 [INFO] Fold 7: Best validation C-index = 0.4494
2025-08-07 12:27:57 [INFO] Early stopping triggered. Best score: 0.5412
2025-08-07 12:27:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:27:57 [INFO] Feature importances range: [-0.0554, 0.1449]
2025-08-07 12:27:57 [INFO] Feature importances mean: 0.0144, median: 0.0056
2025-08-07 12:27:57 [INFO] Fold 8: Selected 104 features with threshold 0.001
2025-08-07 12:27:58 [INFO] Fold 8: Best validation C-index = 0.5198
2025-08-07 12:28:08 [INFO] Early stopping triggered. Best score: 0.5561
2025-08-07 12:28:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:08 [INFO] Feature importances range: [-0.0508, 0.1506]
2025-08-07 12:28:08 [INFO] Feature importances mean: 0.0070, median: 0.0018
2025-08-07 12:28:08 [INFO] Fold 9: Selected 92 features with threshold 0.001
2025-08-07 12:28:09 [INFO] Fold 9: Best validation C-index = 0.4928
2025-08-07 12:28:09 [INFO] Found 108 common features across 10 folds
2025-08-07 12:28:09 [INFO] Common features: ['100A_Flattop_Negative', '100A_Flattop_Positive', '100A_IERROR_RMS', '100V_V280_L_X', '100V_V280_L_Z', '100V_V700_H_Z', '200V_V280_H_X', '200V_V280_L_Y', '200V_V700_L_X', '328A_Flattop_Negative']...
2025-08-07 12:28:09 [INFO] Trial completed: CV C-index = 0.5041 (from 10 successful folds)
2025-08-07 12:28:10 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:28:10 [INFO] Hidden dims: [20, 18, 49, 33], activation: selu, optimizer: Adagrad
2025-08-07 12:28:11 [INFO] Early stopping triggered. Best score: 0.5204
2025-08-07 12:28:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:11 [INFO] Feature importances range: [-0.3884, -0.3352]
2025-08-07 12:28:11 [INFO] Feature importances mean: -0.3665, median: -0.3662
2025-08-07 12:28:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:11 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:28:12 [INFO] Fold 0: Best validation C-index = 0.4924
2025-08-07 12:28:13 [INFO] Early stopping triggered. Best score: 0.5339
2025-08-07 12:28:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:13 [INFO] Feature importances range: [-0.3951, -0.3444]
2025-08-07 12:28:13 [INFO] Feature importances mean: -0.3717, median: -0.3716
2025-08-07 12:28:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:13 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:28:13 [INFO] Fold 1: Best validation C-index = 0.5117
2025-08-07 12:28:23 [INFO] Early stopping triggered. Best score: 0.5226
2025-08-07 12:28:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:23 [INFO] Feature importances range: [-0.3998, -0.3575]
2025-08-07 12:28:23 [INFO] Feature importances mean: -0.3770, median: -0.3769
2025-08-07 12:28:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:23 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:28:24 [INFO] Fold 2: Best validation C-index = 0.5082
2025-08-07 12:28:26 [INFO] Early stopping triggered. Best score: 0.5345
2025-08-07 12:28:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:26 [INFO] Feature importances range: [-0.3958, -0.3496]
2025-08-07 12:28:26 [INFO] Feature importances mean: -0.3728, median: -0.3732
2025-08-07 12:28:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:26 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:28:26 [INFO] Fold 3: Best validation C-index = 0.5254
2025-08-07 12:28:37 [INFO] Early stopping triggered. Best score: 0.5282
2025-08-07 12:28:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:37 [INFO] Feature importances range: [-0.5860, -0.5481]
2025-08-07 12:28:37 [INFO] Feature importances mean: -0.5666, median: -0.5663
2025-08-07 12:28:37 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:37 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:28:37 [INFO] Fold 4: Best validation C-index = 0.5361
2025-08-07 12:28:39 [INFO] Early stopping triggered. Best score: 0.5417
2025-08-07 12:28:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:39 [INFO] Feature importances range: [-0.3848, -0.3372]
2025-08-07 12:28:39 [INFO] Feature importances mean: -0.3619, median: -0.3617
2025-08-07 12:28:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:39 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:28:39 [INFO] Fold 5: Best validation C-index = 0.4996
2025-08-07 12:28:41 [INFO] Early stopping triggered. Best score: 0.5118
2025-08-07 12:28:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:41 [INFO] Feature importances range: [-0.3998, -0.3496]
2025-08-07 12:28:41 [INFO] Feature importances mean: -0.3760, median: -0.3754
2025-08-07 12:28:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:41 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:28:42 [INFO] Fold 6: Best validation C-index = 0.4737
2025-08-07 12:28:53 [INFO] Early stopping triggered. Best score: 0.5885
2025-08-07 12:28:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:53 [INFO] Feature importances range: [-0.5778, -0.5365]
2025-08-07 12:28:53 [INFO] Feature importances mean: -0.5583, median: -0.5585
2025-08-07 12:28:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:53 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:28:54 [INFO] Fold 7: Best validation C-index = 0.5417
2025-08-07 12:28:55 [INFO] Early stopping triggered. Best score: 0.5380
2025-08-07 12:28:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:55 [INFO] Feature importances range: [-0.3960, -0.3518]
2025-08-07 12:28:55 [INFO] Feature importances mean: -0.3715, median: -0.3707
2025-08-07 12:28:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:55 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:28:56 [INFO] Fold 8: Best validation C-index = 0.4628
2025-08-07 12:28:58 [INFO] Early stopping triggered. Best score: 0.5110
2025-08-07 12:28:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:28:58 [INFO] Feature importances range: [-0.3877, -0.3443]
2025-08-07 12:28:58 [INFO] Feature importances mean: -0.3674, median: -0.3681
2025-08-07 12:28:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:28:58 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:28:58 [INFO] Fold 9: Best validation C-index = 0.4907
2025-08-07 12:28:58 [INFO] Found 10 common features across 10 folds
2025-08-07 12:28:58 [INFO] Common features: ['Y_STATUS_280V_Y1_High', 'ICC_TEMP(T0)', 'Y_CLOSED_LOOP_V280_H_Y', 'PULSE_700V_X2_HIGH', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_H_X', 'PULSE_280V_Z2_HIGH', 'EPI_700V_Z2_HIGH', 'Y_CLOSED_LOOP_V280_H_Z', '200V_V700_L_X']...
2025-08-07 12:28:58 [INFO] Trial completed: CV C-index = 0.5042 (from 10 successful folds)
2025-08-07 12:28:58 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:28:58 [INFO] Hidden dims: [37, 46, 24], activation: selu, optimizer: SGD
2025-08-07 12:29:08 [INFO] Early stopping triggered. Best score: 0.5301
2025-08-07 12:29:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:08 [INFO] Feature importances range: [-0.1150, -0.0373]
2025-08-07 12:29:08 [INFO] Feature importances mean: -0.0750, median: -0.0756
2025-08-07 12:29:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:08 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:29:08 [INFO] Fold 0: Best validation C-index = 0.4502
2025-08-07 12:29:10 [INFO] Early stopping triggered. Best score: 0.5114
2025-08-07 12:29:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:10 [INFO] Feature importances range: [-0.1162, -0.0444]
2025-08-07 12:29:10 [INFO] Feature importances mean: -0.0755, median: -0.0750
2025-08-07 12:29:10 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:10 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:29:11 [INFO] Fold 1: Best validation C-index = 0.5035
2025-08-07 12:29:13 [INFO] Early stopping triggered. Best score: 0.5548
2025-08-07 12:29:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:13 [INFO] Feature importances range: [-0.1167, -0.0443]
2025-08-07 12:29:13 [INFO] Feature importances mean: -0.0752, median: -0.0755
2025-08-07 12:29:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:13 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:29:13 [INFO] Fold 2: Best validation C-index = 0.5209
2025-08-07 12:29:25 [INFO] Early stopping triggered. Best score: 0.5302
2025-08-07 12:29:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:25 [INFO] Feature importances range: [-0.2339, -0.1037]
2025-08-07 12:29:25 [INFO] Feature importances mean: -0.1638, median: -0.1626
2025-08-07 12:29:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:25 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:29:25 [INFO] Fold 3: Best validation C-index = 0.5141
2025-08-07 12:29:41 [INFO] Early stopping triggered. Best score: 0.5745
2025-08-07 12:29:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:41 [INFO] Feature importances range: [-0.7586, -0.5503]
2025-08-07 12:29:41 [INFO] Feature importances mean: -0.6770, median: -0.6778
2025-08-07 12:29:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:41 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:29:42 [INFO] Fold 4: Best validation C-index = 0.5412
2025-08-07 12:29:44 [INFO] Early stopping triggered. Best score: 0.5181
2025-08-07 12:29:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:29:44 [INFO] Feature importances range: [-0.1188, -0.0345]
2025-08-07 12:29:44 [INFO] Feature importances mean: -0.0824, median: -0.0821
2025-08-07 12:29:44 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:29:44 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:29:44 [INFO] Fold 5: Best validation C-index = 0.5223
2025-08-07 12:30:09 [INFO] Early stopping triggered. Best score: 0.5742
2025-08-07 12:30:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:09 [INFO] Feature importances range: [-0.8324, -0.6927]
2025-08-07 12:30:09 [INFO] Feature importances mean: -0.7630, median: -0.7647
2025-08-07 12:30:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:09 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:30:09 [INFO] Fold 6: Best validation C-index = 0.4969
2025-08-07 12:30:12 [INFO] Early stopping triggered. Best score: 0.4688
2025-08-07 12:30:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:12 [INFO] Feature importances range: [-0.1134, -0.0405]
2025-08-07 12:30:12 [INFO] Feature importances mean: -0.0763, median: -0.0769
2025-08-07 12:30:12 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:12 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:30:12 [INFO] Fold 7: Best validation C-index = 0.5388
2025-08-07 12:30:14 [INFO] Early stopping triggered. Best score: 0.5046
2025-08-07 12:30:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:14 [INFO] Feature importances range: [-0.1077, -0.0359]
2025-08-07 12:30:14 [INFO] Feature importances mean: -0.0755, median: -0.0754
2025-08-07 12:30:14 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:14 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:30:16 [INFO] Fold 8: Best validation C-index = 0.4951
2025-08-07 12:30:24 [INFO] Early stopping triggered. Best score: 0.5116
2025-08-07 12:30:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:24 [INFO] Feature importances range: [-0.1110, -0.0416]
2025-08-07 12:30:24 [INFO] Feature importances mean: -0.0746, median: -0.0734
2025-08-07 12:30:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:24 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:30:25 [INFO] Fold 9: Best validation C-index = 0.4493
2025-08-07 12:30:25 [INFO] Found 10 common features across 10 folds
2025-08-07 12:30:25 [INFO] Common features: ['100V_V700_L_X', 'Y_CLOSED_LOOP_V700_H_X', 'PULSE_700V_X2_LOW', '300V_V280_L_Z', '200V_V280_L_Z', 'PULSE_280V_X1_LOW', '200V_V280_H_X', 'X_CLOSED_LOOP_V700_L_Y', 'EPI_IERROR_RMS', 'PULSE_700V_Z1_LOW']...
2025-08-07 12:30:25 [INFO] Trial completed: CV C-index = 0.5032 (from 10 successful folds)
2025-08-07 12:30:25 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:30:25 [INFO] Hidden dims: [5, 15, 50, 23], activation: selu, optimizer: Adagrad
2025-08-07 12:30:27 [INFO] Early stopping triggered. Best score: 0.5430
2025-08-07 12:30:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:27 [INFO] Feature importances range: [-0.4624, -0.4126]
2025-08-07 12:30:27 [INFO] Feature importances mean: -0.4377, median: -0.4382
2025-08-07 12:30:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:27 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:30:27 [INFO] Fold 0: Best validation C-index = 0.4704
2025-08-07 12:30:37 [INFO] Early stopping triggered. Best score: 0.5831
2025-08-07 12:30:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:37 [INFO] Feature importances range: [-0.6525, -0.6155]
2025-08-07 12:30:37 [INFO] Feature importances mean: -0.6339, median: -0.6344
2025-08-07 12:30:37 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:37 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:30:38 [INFO] Fold 1: Best validation C-index = 0.4628
2025-08-07 12:30:39 [INFO] Early stopping triggered. Best score: 0.5551
2025-08-07 12:30:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:39 [INFO] Feature importances range: [-0.3920, -0.3486]
2025-08-07 12:30:39 [INFO] Feature importances mean: -0.3713, median: -0.3718
2025-08-07 12:30:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:39 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:30:40 [INFO] Fold 2: Best validation C-index = 0.5779
2025-08-07 12:30:41 [INFO] Early stopping triggered. Best score: 0.5394
2025-08-07 12:30:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:41 [INFO] Feature importances range: [-0.4531, -0.4072]
2025-08-07 12:30:41 [INFO] Feature importances mean: -0.4317, median: -0.4319
2025-08-07 12:30:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:41 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:30:42 [INFO] Fold 3: Best validation C-index = 0.4937
2025-08-07 12:30:43 [INFO] Early stopping triggered. Best score: 0.5856
2025-08-07 12:30:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:43 [INFO] Feature importances range: [-0.4029, -0.3498]
2025-08-07 12:30:43 [INFO] Feature importances mean: -0.3714, median: -0.3710
2025-08-07 12:30:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:43 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:30:43 [INFO] Fold 4: Best validation C-index = 0.5055
2025-08-07 12:30:52 [INFO] Early stopping triggered. Best score: 0.5160
2025-08-07 12:30:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:52 [INFO] Feature importances range: [-0.3956, -0.3492]
2025-08-07 12:30:52 [INFO] Feature importances mean: -0.3722, median: -0.3722
2025-08-07 12:30:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:52 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:30:53 [INFO] Fold 5: Best validation C-index = 0.4837
2025-08-07 12:30:54 [INFO] Early stopping triggered. Best score: 0.5222
2025-08-07 12:30:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:54 [INFO] Feature importances range: [-0.4058, -0.3522]
2025-08-07 12:30:54 [INFO] Feature importances mean: -0.3778, median: -0.3778
2025-08-07 12:30:54 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:54 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:30:54 [INFO] Fold 6: Best validation C-index = 0.5254
2025-08-07 12:30:55 [INFO] Early stopping triggered. Best score: 0.4994
2025-08-07 12:30:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:55 [INFO] Feature importances range: [-0.3952, -0.3411]
2025-08-07 12:30:55 [INFO] Feature importances mean: -0.3663, median: -0.3656
2025-08-07 12:30:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:55 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:30:56 [INFO] Fold 7: Best validation C-index = 0.5081
2025-08-07 12:30:58 [INFO] Early stopping triggered. Best score: 0.5340
2025-08-07 12:30:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:30:58 [INFO] Feature importances range: [-0.3990, -0.3504]
2025-08-07 12:30:58 [INFO] Feature importances mean: -0.3726, median: -0.3727
2025-08-07 12:30:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:30:58 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:30:58 [INFO] Fold 8: Best validation C-index = 0.5001
2025-08-07 12:31:07 [INFO] Early stopping triggered. Best score: 0.5227
2025-08-07 12:31:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:07 [INFO] Feature importances range: [-0.4085, -0.3371]
2025-08-07 12:31:07 [INFO] Feature importances mean: -0.3711, median: -0.3706
2025-08-07 12:31:07 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:07 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:31:08 [INFO] Fold 9: Best validation C-index = 0.5172
2025-08-07 12:31:08 [INFO] Found 5 common features across 10 folds
2025-08-07 12:31:08 [INFO] Common features: ['EPI_IERROR_RMS', '100A_Flattop_Negative', '300V_V280_L_Z', 'Z_CLOSED_LOOP_V280_H_X', '420V_V700_H_Z']...
2025-08-07 12:31:08 [INFO] Trial completed: CV C-index = 0.5045 (from 10 successful folds)
2025-08-07 12:31:08 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:31:08 [INFO] Hidden dims: [24, 24, 35], activation: sigmoid, optimizer: Adagrad
2025-08-07 12:31:23 [INFO] Early stopping triggered. Best score: 0.5224
2025-08-07 12:31:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:23 [INFO] Feature importances range: [-0.1075, -0.0496]
2025-08-07 12:31:23 [INFO] Feature importances mean: -0.0768, median: -0.0771
2025-08-07 12:31:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:23 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:31:24 [INFO] Fold 0: Best validation C-index = 0.5052
2025-08-07 12:31:25 [INFO] Early stopping triggered. Best score: 0.5447
2025-08-07 12:31:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:25 [INFO] Feature importances range: [-0.0601, -0.0054]
2025-08-07 12:31:25 [INFO] Feature importances mean: -0.0328, median: -0.0329
2025-08-07 12:31:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:25 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:31:26 [INFO] Fold 1: Best validation C-index = 0.5163
2025-08-07 12:31:39 [INFO] Early stopping triggered. Best score: 0.5134
2025-08-07 12:31:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:39 [INFO] Feature importances range: [-0.0866, -0.0383]
2025-08-07 12:31:39 [INFO] Feature importances mean: -0.0641, median: -0.0637
2025-08-07 12:31:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:39 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:31:39 [INFO] Fold 2: Best validation C-index = 0.5097
2025-08-07 12:31:41 [INFO] Early stopping triggered. Best score: 0.4763
2025-08-07 12:31:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:41 [INFO] Feature importances range: [-0.0573, -0.0101]
2025-08-07 12:31:41 [INFO] Feature importances mean: -0.0334, median: -0.0334
2025-08-07 12:31:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:41 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:31:41 [INFO] Fold 3: Best validation C-index = 0.4776
2025-08-07 12:31:43 [INFO] Early stopping triggered. Best score: 0.5008
2025-08-07 12:31:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:43 [INFO] Feature importances range: [-0.0622, -0.0131]
2025-08-07 12:31:43 [INFO] Feature importances mean: -0.0381, median: -0.0381
2025-08-07 12:31:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:43 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:31:43 [INFO] Fold 4: Best validation C-index = 0.5052
2025-08-07 12:31:58 [INFO] Early stopping triggered. Best score: 0.5173
2025-08-07 12:31:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:31:58 [INFO] Feature importances range: [-0.1058, -0.0440]
2025-08-07 12:31:58 [INFO] Feature importances mean: -0.0748, median: -0.0752
2025-08-07 12:31:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:31:58 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:31:58 [INFO] Fold 5: Best validation C-index = 0.5383
2025-08-07 12:32:09 [INFO] Early stopping triggered. Best score: 0.4955
2025-08-07 12:32:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:09 [INFO] Feature importances range: [-0.0760, -0.0233]
2025-08-07 12:32:09 [INFO] Feature importances mean: -0.0496, median: -0.0490
2025-08-07 12:32:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:09 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:32:10 [INFO] Fold 6: Best validation C-index = 0.4973
2025-08-07 12:32:12 [INFO] Early stopping triggered. Best score: 0.5852
2025-08-07 12:32:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:12 [INFO] Feature importances range: [-0.0695, -0.0158]
2025-08-07 12:32:12 [INFO] Feature importances mean: -0.0396, median: -0.0392
2025-08-07 12:32:12 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:12 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:32:12 [INFO] Fold 7: Best validation C-index = 0.4727
2025-08-07 12:32:14 [INFO] Early stopping triggered. Best score: 0.5040
2025-08-07 12:32:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:14 [INFO] Feature importances range: [-0.0592, -0.0079]
2025-08-07 12:32:14 [INFO] Feature importances mean: -0.0342, median: -0.0335
2025-08-07 12:32:14 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:14 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:32:14 [INFO] Fold 8: Best validation C-index = 0.5328
2025-08-07 12:32:24 [INFO] Early stopping triggered. Best score: 0.5238
2025-08-07 12:32:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:24 [INFO] Feature importances range: [-0.0659, -0.0005]
2025-08-07 12:32:24 [INFO] Feature importances mean: -0.0337, median: -0.0336
2025-08-07 12:32:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:24 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:32:24 [INFO] Fold 9: Best validation C-index = 0.5142
2025-08-07 12:32:24 [INFO] Found 10 common features across 10 folds
2025-08-07 12:32:24 [INFO] Common features: ['Z_CLOSED_LOOP_V700_H_Y', 'Z_CLOSED_LOOP_V280_H_Y', 'Y_CLOSED_LOOP_V700_L_Z', '100V_V700_L_Z', '300V_V280_H_X', '100A_IERROR_Peak', '420V_V280_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_L_Y', 'X_CLOSED_LOOP_V280_L_Y', '328A_IERROR_Peak']...
2025-08-07 12:32:24 [INFO] Trial completed: CV C-index = 0.5069 (from 10 successful folds)
2025-08-07 12:32:24 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:32:24 [INFO] Hidden dims: [10, 49], activation: tanh, optimizer: SGD
2025-08-07 12:32:26 [INFO] Early stopping triggered. Best score: 0.5168
2025-08-07 12:32:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:26 [INFO] Feature importances range: [-0.9887, -0.9007]
2025-08-07 12:32:26 [INFO] Feature importances mean: -0.9544, median: -0.9563
2025-08-07 12:32:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:26 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:32:26 [INFO] Fold 0: Best validation C-index = 0.5189
2025-08-07 12:32:29 [INFO] Early stopping triggered. Best score: 0.5202
2025-08-07 12:32:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:29 [INFO] Feature importances range: [-1.0811, -1.0571]
2025-08-07 12:32:29 [INFO] Feature importances mean: -1.0718, median: -1.0719
2025-08-07 12:32:29 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:29 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:32:29 [INFO] Fold 1: Best validation C-index = 0.4894
2025-08-07 12:32:39 [INFO] Early stopping triggered. Best score: 0.5085
2025-08-07 12:32:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:39 [INFO] Feature importances range: [-1.0793, -1.0542]
2025-08-07 12:32:39 [INFO] Feature importances mean: -1.0680, median: -1.0682
2025-08-07 12:32:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:39 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:32:40 [INFO] Fold 2: Best validation C-index = 0.4852
2025-08-07 12:32:43 [INFO] Early stopping triggered. Best score: 0.5000
2025-08-07 12:32:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:43 [INFO] Feature importances range: [-1.0856, -1.0601]
2025-08-07 12:32:43 [INFO] Feature importances mean: -1.0764, median: -1.0769
2025-08-07 12:32:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:43 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:32:43 [INFO] Fold 3: Best validation C-index = 0.5740
2025-08-07 12:32:53 [INFO] Early stopping triggered. Best score: 0.5453
2025-08-07 12:32:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:53 [INFO] Feature importances range: [-1.0681, -1.0321]
2025-08-07 12:32:53 [INFO] Feature importances mean: -1.0530, median: -1.0531
2025-08-07 12:32:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:53 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:32:54 [INFO] Fold 4: Best validation C-index = 0.5102
2025-08-07 12:32:55 [INFO] Early stopping triggered. Best score: 0.5102
2025-08-07 12:32:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:55 [INFO] Feature importances range: [-0.9904, -0.8360]
2025-08-07 12:32:55 [INFO] Feature importances mean: -0.9555, median: -0.9585
2025-08-07 12:32:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:55 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:32:56 [INFO] Fold 5: Best validation C-index = 0.5080
2025-08-07 12:32:58 [INFO] Early stopping triggered. Best score: 0.5233
2025-08-07 12:32:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:32:58 [INFO] Feature importances range: [-1.0802, -1.0541]
2025-08-07 12:32:58 [INFO] Feature importances mean: -1.0701, median: -1.0704
2025-08-07 12:32:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:32:58 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:32:59 [INFO] Fold 6: Best validation C-index = 0.4878
2025-08-07 12:33:09 [INFO] Early stopping triggered. Best score: 0.5343
2025-08-07 12:33:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:09 [INFO] Feature importances range: [-1.0804, -1.0544]
2025-08-07 12:33:09 [INFO] Feature importances mean: -1.0698, median: -1.0698
2025-08-07 12:33:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:09 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:33:10 [INFO] Fold 7: Best validation C-index = 0.5225
2025-08-07 12:33:13 [INFO] Early stopping triggered. Best score: 0.5000
2025-08-07 12:33:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:13 [INFO] Feature importances range: [-1.0846, -1.0602]
2025-08-07 12:33:13 [INFO] Feature importances mean: -1.0752, median: -1.0756
2025-08-07 12:33:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:13 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:33:13 [INFO] Fold 8: Best validation C-index = 0.5127
2025-08-07 12:33:23 [INFO] Early stopping triggered. Best score: 0.5202
2025-08-07 12:33:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:23 [INFO] Feature importances range: [-1.0424, -0.9863]
2025-08-07 12:33:23 [INFO] Feature importances mean: -1.0232, median: -1.0247
2025-08-07 12:33:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:23 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:33:24 [INFO] Fold 9: Best validation C-index = 0.4964
2025-08-07 12:33:24 [INFO] Found 10 common features across 10 folds
2025-08-07 12:33:24 [INFO] Common features: ['OUTPUT_VOLTAGE_DISCH_V280_L_Y', 'PULSE_280V_Y1_LOW', '200V_V700_L_Y', '100V_V700_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_Z', 'HEATSINK_TEMP_VALUE', 'PULSE_700V_Y2_LOW', 'Y_CLOSED_LOOP_V280_H_Y', 'Heat_Sink_Temp_Amplifier_Lavaflex', '100V_V700_H_Z']...
2025-08-07 12:33:24 [INFO] Trial completed: CV C-index = 0.5105 (from 10 successful folds)
2025-08-07 12:33:24 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:33:24 [INFO] Hidden dims: [38, 8, 56, 45], activation: selu, optimizer: Adagrad
2025-08-07 12:33:25 [INFO] Early stopping triggered. Best score: 0.5254
2025-08-07 12:33:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:25 [INFO] Feature importances range: [-0.1835, -0.1369]
2025-08-07 12:33:25 [INFO] Feature importances mean: -0.1613, median: -0.1613
2025-08-07 12:33:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:25 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:33:26 [INFO] Fold 0: Best validation C-index = 0.4999
2025-08-07 12:33:27 [INFO] Early stopping triggered. Best score: 0.5417
2025-08-07 12:33:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:27 [INFO] Feature importances range: [-0.2055, -0.1516]
2025-08-07 12:33:27 [INFO] Feature importances mean: -0.1746, median: -0.1745
2025-08-07 12:33:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:27 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:33:28 [INFO] Fold 1: Best validation C-index = 0.5163
2025-08-07 12:33:39 [INFO] Early stopping triggered. Best score: 0.5296
2025-08-07 12:33:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:39 [INFO] Feature importances range: [-0.2559, -0.2035]
2025-08-07 12:33:39 [INFO] Feature importances mean: -0.2329, median: -0.2331
2025-08-07 12:33:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:39 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:33:39 [INFO] Fold 2: Best validation C-index = 0.5176
2025-08-07 12:33:42 [INFO] Early stopping triggered. Best score: 0.5444
2025-08-07 12:33:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:42 [INFO] Feature importances range: [-0.2527, -0.2034]
2025-08-07 12:33:42 [INFO] Feature importances mean: -0.2309, median: -0.2310
2025-08-07 12:33:42 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:42 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:33:43 [INFO] Fold 3: Best validation C-index = 0.5182
2025-08-07 12:33:49 [INFO] Early stopping triggered. Best score: 0.5451
2025-08-07 12:33:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:49 [INFO] Feature importances range: [-0.1998, -0.1378]
2025-08-07 12:33:49 [INFO] Feature importances mean: -0.1685, median: -0.1687
2025-08-07 12:33:49 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:49 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:33:50 [INFO] Fold 4: Best validation C-index = 0.4661
2025-08-07 12:33:55 [INFO] Early stopping triggered. Best score: 0.5654
2025-08-07 12:33:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:55 [INFO] Feature importances range: [-0.2789, -0.2124]
2025-08-07 12:33:55 [INFO] Feature importances mean: -0.2363, median: -0.2367
2025-08-07 12:33:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:55 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:33:56 [INFO] Fold 5: Best validation C-index = 0.5485
2025-08-07 12:33:57 [INFO] Early stopping triggered. Best score: 0.5611
2025-08-07 12:33:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:33:57 [INFO] Feature importances range: [-0.1869, -0.1356]
2025-08-07 12:33:57 [INFO] Feature importances mean: -0.1612, median: -0.1611
2025-08-07 12:33:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:33:57 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:33:58 [INFO] Fold 6: Best validation C-index = 0.4656
2025-08-07 12:34:08 [INFO] Early stopping triggered. Best score: 0.5240
2025-08-07 12:34:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:08 [INFO] Feature importances range: [-0.2311, -0.1854]
2025-08-07 12:34:08 [INFO] Feature importances mean: -0.2079, median: -0.2077
2025-08-07 12:34:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:34:08 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:34:08 [INFO] Fold 7: Best validation C-index = 0.4788
2025-08-07 12:34:10 [INFO] Early stopping triggered. Best score: 0.5462
2025-08-07 12:34:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:10 [INFO] Feature importances range: [-0.1991, -0.1467]
2025-08-07 12:34:10 [INFO] Feature importances mean: -0.1727, median: -0.1723
2025-08-07 12:34:10 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:34:10 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:34:11 [INFO] Fold 8: Best validation C-index = 0.4753
2025-08-07 12:34:12 [INFO] Early stopping triggered. Best score: 0.5603
2025-08-07 12:34:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:12 [INFO] Feature importances range: [-0.1922, -0.1390]
2025-08-07 12:34:12 [INFO] Feature importances mean: -0.1628, median: -0.1622
2025-08-07 12:34:12 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:34:12 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:34:13 [INFO] Fold 9: Best validation C-index = 0.4727
2025-08-07 12:34:13 [INFO] Found 10 common features across 10 folds
2025-08-07 12:34:13 [INFO] Common features: ['EPI_700V_Z1_LOW', '100A_IERROR_Peak', 'EPI_280V_X2_LOW', '300V_V280_L_Z', '200V_V280_H_X', '100V_V280_L_Z', 'EPI_700V_X1_HIGH', 'BUS_VOLTAGE_VY_LOW', '100V_V700_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']...
2025-08-07 12:34:13 [INFO] Trial completed: CV C-index = 0.4959 (from 10 successful folds)
2025-08-07 12:34:13 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:34:13 [INFO] Hidden dims: [40, 52, 25], activation: relu, optimizer: Adagrad
2025-08-07 12:34:26 [INFO] Early stopping triggered. Best score: 0.5289
2025-08-07 12:34:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:26 [INFO] Feature importances range: [-0.0317, 0.0252]
2025-08-07 12:34:26 [INFO] Feature importances mean: -0.0065, median: -0.0065
2025-08-07 12:34:26 [INFO] Fold 0: Selected 39 features with threshold 0.001
2025-08-07 12:34:27 [INFO] Fold 0: Best validation C-index = 0.5527
2025-08-07 12:34:44 [INFO] Early stopping triggered. Best score: 0.4974
2025-08-07 12:34:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:44 [INFO] Feature importances range: [-0.0392, 0.0159]
2025-08-07 12:34:44 [INFO] Feature importances mean: -0.0107, median: -0.0104
2025-08-07 12:34:44 [INFO] Fold 1: Selected 24 features with threshold 0.001
2025-08-07 12:34:44 [INFO] Fold 1: Best validation C-index = 0.5211
2025-08-07 12:34:58 [INFO] Early stopping triggered. Best score: 0.5397
2025-08-07 12:34:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:34:58 [INFO] Feature importances range: [-0.0319, 0.0181]
2025-08-07 12:34:58 [INFO] Feature importances mean: -0.0060, median: -0.0060
2025-08-07 12:34:58 [INFO] Fold 2: Selected 48 features with threshold 0.001
2025-08-07 12:34:58 [INFO] Fold 2: Best validation C-index = 0.5060
2025-08-07 12:35:29 [INFO] Early stopping triggered. Best score: 0.5259
2025-08-07 12:35:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:35:29 [INFO] Feature importances range: [-0.0421, 0.0203]
2025-08-07 12:35:29 [INFO] Feature importances mean: -0.0106, median: -0.0110
2025-08-07 12:35:29 [INFO] Fold 3: Selected 29 features with threshold 0.001
2025-08-07 12:35:29 [INFO] Fold 3: Best validation C-index = 0.4936
2025-08-07 12:35:43 [INFO] Early stopping triggered. Best score: 0.5308
2025-08-07 12:35:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:35:43 [INFO] Feature importances range: [-0.0394, 0.0175]
2025-08-07 12:35:43 [INFO] Feature importances mean: -0.0053, median: -0.0056
2025-08-07 12:35:43 [INFO] Fold 4: Selected 47 features with threshold 0.001
2025-08-07 12:35:44 [INFO] Fold 4: Best validation C-index = 0.5240
2025-08-07 12:35:55 [INFO] Early stopping triggered. Best score: 0.5558
2025-08-07 12:35:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:35:55 [INFO] Feature importances range: [-0.0269, 0.0242]
2025-08-07 12:35:55 [INFO] Feature importances mean: -0.0041, median: -0.0045
2025-08-07 12:35:55 [INFO] Fold 5: Selected 48 features with threshold 0.001
2025-08-07 12:35:56 [INFO] Fold 5: Best validation C-index = 0.5311
2025-08-07 12:35:59 [INFO] Early stopping triggered. Best score: 0.5332
2025-08-07 12:35:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:35:59 [INFO] Feature importances range: [-0.0321, 0.0208]
2025-08-07 12:35:59 [INFO] Feature importances mean: -0.0056, median: -0.0048
2025-08-07 12:35:59 [INFO] Fold 6: Selected 43 features with threshold 0.001
2025-08-07 12:35:59 [INFO] Fold 6: Best validation C-index = 0.4965
2025-08-07 12:36:09 [INFO] Early stopping triggered. Best score: 0.5318
2025-08-07 12:36:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:09 [INFO] Feature importances range: [-0.0340, 0.0217]
2025-08-07 12:36:09 [INFO] Feature importances mean: -0.0033, median: -0.0035
2025-08-07 12:36:09 [INFO] Fold 7: Selected 59 features with threshold 0.001
2025-08-07 12:36:10 [INFO] Fold 7: Best validation C-index = 0.5010
2025-08-07 12:36:12 [INFO] Early stopping triggered. Best score: 0.4980
2025-08-07 12:36:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:12 [INFO] Feature importances range: [-0.0346, 0.0230]
2025-08-07 12:36:12 [INFO] Feature importances mean: -0.0027, median: -0.0021
2025-08-07 12:36:12 [INFO] Fold 8: Selected 66 features with threshold 0.001
2025-08-07 12:36:12 [INFO] Fold 8: Best validation C-index = 0.4182
2025-08-07 12:36:14 [INFO] Early stopping triggered. Best score: 0.4653
2025-08-07 12:36:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:14 [INFO] Feature importances range: [-0.0317, 0.0169]
2025-08-07 12:36:14 [INFO] Feature importances mean: -0.0034, median: -0.0036
2025-08-07 12:36:14 [INFO] Fold 9: Selected 63 features with threshold 0.001
2025-08-07 12:36:15 [INFO] Fold 9: Best validation C-index = 0.5196
2025-08-07 12:36:15 [INFO] Found 16 common features across 10 folds
2025-08-07 12:36:15 [INFO] Common features: ['100A_Flattop_Positive', '300V_V280_L_Z', '300V_V700_L_X', '328A_Flattop_Positive', 'OUTPUT_VOLTAGE_DISCH_V280_L_Z', 'X_CLOSED_LOOP_V700_H_X', 'X_CLOSED_LOOP_V700_L_Z', 'Y_CLOSED_LOOP_V700_L_Z', '328A_IERROR_RMS', 'HEATSINK_TEMP_VALUE']...
2025-08-07 12:36:15 [INFO] Trial completed: CV C-index = 0.5064 (from 10 successful folds)
2025-08-07 12:36:15 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:36:15 [INFO] Hidden dims: [31, 41, 41], activation: relu, optimizer: Adagrad
2025-08-07 12:36:24 [INFO] Early stopping triggered. Best score: 0.5263
2025-08-07 12:36:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:24 [INFO] Feature importances range: [-0.0253, 0.0244]
2025-08-07 12:36:24 [INFO] Feature importances mean: -0.0015, median: -0.0023
2025-08-07 12:36:24 [INFO] Fold 0: Selected 63 features with threshold 0.001
2025-08-07 12:36:25 [INFO] Fold 0: Best validation C-index = 0.5124
2025-08-07 12:36:39 [INFO] Early stopping triggered. Best score: 0.4574
2025-08-07 12:36:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:39 [INFO] Feature importances range: [-0.0268, 0.0257]
2025-08-07 12:36:39 [INFO] Feature importances mean: -0.0005, median: -0.0008
2025-08-07 12:36:39 [INFO] Fold 1: Selected 74 features with threshold 0.001
2025-08-07 12:36:40 [INFO] Fold 1: Best validation C-index = 0.5211
2025-08-07 12:36:47 [INFO] Early stopping triggered. Best score: 0.4722
2025-08-07 12:36:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:47 [INFO] Feature importances range: [-0.0240, 0.0262]
2025-08-07 12:36:47 [INFO] Feature importances mean: -0.0014, median: -0.0015
2025-08-07 12:36:47 [INFO] Fold 2: Selected 70 features with threshold 0.001
2025-08-07 12:36:47 [INFO] Fold 2: Best validation C-index = 0.4488
2025-08-07 12:36:49 [INFO] Early stopping triggered. Best score: 0.4546
2025-08-07 12:36:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:49 [INFO] Feature importances range: [-0.0263, 0.0219]
2025-08-07 12:36:49 [INFO] Feature importances mean: -0.0002, median: -0.0001
2025-08-07 12:36:49 [INFO] Fold 3: Selected 81 features with threshold 0.001
2025-08-07 12:36:50 [INFO] Fold 3: Best validation C-index = 0.5293
2025-08-07 12:36:51 [INFO] Early stopping triggered. Best score: 0.4925
2025-08-07 12:36:51 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:51 [INFO] Feature importances range: [-0.0350, 0.0246]
2025-08-07 12:36:51 [INFO] Feature importances mean: -0.0018, median: -0.0016
2025-08-07 12:36:51 [INFO] Fold 4: Selected 72 features with threshold 0.001
2025-08-07 12:36:52 [INFO] Fold 4: Best validation C-index = 0.4854
2025-08-07 12:36:54 [INFO] Early stopping triggered. Best score: 0.4991
2025-08-07 12:36:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:54 [INFO] Feature importances range: [-0.0330, 0.0315]
2025-08-07 12:36:54 [INFO] Feature importances mean: -0.0008, median: 0.0008
2025-08-07 12:36:54 [INFO] Fold 5: Selected 88 features with threshold 0.001
2025-08-07 12:36:55 [INFO] Fold 5: Best validation C-index = 0.5342
2025-08-07 12:36:57 [INFO] Early stopping triggered. Best score: 0.4637
2025-08-07 12:36:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:57 [INFO] Feature importances range: [-0.0258, 0.0216]
2025-08-07 12:36:57 [INFO] Feature importances mean: -0.0003, median: 0.0005
2025-08-07 12:36:57 [INFO] Fold 6: Selected 83 features with threshold 0.001
2025-08-07 12:36:57 [INFO] Fold 6: Best validation C-index = 0.4765
2025-08-07 12:36:59 [INFO] Early stopping triggered. Best score: 0.4789
2025-08-07 12:36:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:36:59 [INFO] Feature importances range: [-0.0284, 0.0343]
2025-08-07 12:36:59 [INFO] Feature importances mean: -0.0012, median: -0.0008
2025-08-07 12:36:59 [INFO] Fold 7: Selected 78 features with threshold 0.001
2025-08-07 12:37:00 [INFO] Fold 7: Best validation C-index = 0.5543
2025-08-07 12:37:01 [INFO] Early stopping triggered. Best score: 0.5552
2025-08-07 12:37:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:01 [INFO] Feature importances range: [-0.0271, 0.0317]
2025-08-07 12:37:01 [INFO] Feature importances mean: -0.0002, median: -0.0002
2025-08-07 12:37:01 [INFO] Fold 8: Selected 80 features with threshold 0.001
2025-08-07 12:37:02 [INFO] Fold 8: Best validation C-index = 0.4690
2025-08-07 12:37:05 [INFO] Early stopping triggered. Best score: 0.5251
2025-08-07 12:37:05 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:05 [INFO] Feature importances range: [-0.0287, 0.0261]
2025-08-07 12:37:05 [INFO] Feature importances mean: -0.0007, median: -0.0003
2025-08-07 12:37:05 [INFO] Fold 9: Selected 78 features with threshold 0.001
2025-08-07 12:37:06 [INFO] Fold 9: Best validation C-index = 0.5036
2025-08-07 12:37:06 [INFO] Found 71 common features across 10 folds
2025-08-07 12:37:06 [INFO] Common features: ['100A_IERROR_Peak', '100V_V280_H_Z', '100V_V280_L_Y', '200V_V280_H_Z', '300V_V280_H_Z', '300V_V280_L_X', '328A_IERROR_Peak', '420V_V280_L_Z', '420V_V700_H_Z', 'Absolute_Error']...
2025-08-07 12:37:06 [INFO] Trial completed: CV C-index = 0.5035 (from 10 successful folds)
2025-08-07 12:37:06 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:37:06 [INFO] Hidden dims: [55, 57, 25], activation: relu, optimizer: Adagrad
2025-08-07 12:37:07 [INFO] Early stopping triggered. Best score: 0.5124
2025-08-07 12:37:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:07 [INFO] Feature importances range: [-0.0398, 0.0521]
2025-08-07 12:37:07 [INFO] Feature importances mean: 0.0072, median: 0.0075
2025-08-07 12:37:07 [INFO] Fold 0: Selected 119 features with threshold 0.001
2025-08-07 12:37:08 [INFO] Fold 0: Best validation C-index = 0.4993
2025-08-07 12:37:10 [INFO] Early stopping triggered. Best score: 0.5062
2025-08-07 12:37:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:10 [INFO] Feature importances range: [-0.0578, 0.0433]
2025-08-07 12:37:10 [INFO] Feature importances mean: 0.0054, median: 0.0066
2025-08-07 12:37:10 [INFO] Fold 1: Selected 114 features with threshold 0.001
2025-08-07 12:37:11 [INFO] Fold 1: Best validation C-index = 0.5219
2025-08-07 12:37:12 [INFO] Early stopping triggered. Best score: 0.4895
2025-08-07 12:37:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:12 [INFO] Feature importances range: [-0.0315, 0.0508]
2025-08-07 12:37:12 [INFO] Feature importances mean: 0.0090, median: 0.0103
2025-08-07 12:37:12 [INFO] Fold 2: Selected 121 features with threshold 0.001
2025-08-07 12:37:13 [INFO] Fold 2: Best validation C-index = 0.5119
2025-08-07 12:37:15 [INFO] Early stopping triggered. Best score: 0.5549
2025-08-07 12:37:15 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:15 [INFO] Feature importances range: [-0.0263, 0.0456]
2025-08-07 12:37:15 [INFO] Feature importances mean: 0.0083, median: 0.0075
2025-08-07 12:37:15 [INFO] Fold 3: Selected 113 features with threshold 0.001
2025-08-07 12:37:16 [INFO] Fold 3: Best validation C-index = 0.5079
2025-08-07 12:37:19 [INFO] Early stopping triggered. Best score: 0.4986
2025-08-07 12:37:19 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:19 [INFO] Feature importances range: [-0.0382, 0.0599]
2025-08-07 12:37:19 [INFO] Feature importances mean: 0.0133, median: 0.0152
2025-08-07 12:37:19 [INFO] Fold 4: Selected 135 features with threshold 0.001
2025-08-07 12:37:20 [INFO] Fold 4: Best validation C-index = 0.5240
2025-08-07 12:37:22 [INFO] Early stopping triggered. Best score: 0.5032
2025-08-07 12:37:22 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:22 [INFO] Feature importances range: [-0.0293, 0.0504]
2025-08-07 12:37:22 [INFO] Feature importances mean: 0.0081, median: 0.0098
2025-08-07 12:37:22 [INFO] Fold 5: Selected 114 features with threshold 0.001
2025-08-07 12:37:23 [INFO] Fold 5: Best validation C-index = 0.4938
2025-08-07 12:37:24 [INFO] Early stopping triggered. Best score: 0.5373
2025-08-07 12:37:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:24 [INFO] Feature importances range: [-0.0361, 0.0405]
2025-08-07 12:37:24 [INFO] Feature importances mean: 0.0074, median: 0.0083
2025-08-07 12:37:24 [INFO] Fold 6: Selected 118 features with threshold 0.001
2025-08-07 12:37:25 [INFO] Fold 6: Best validation C-index = 0.5634
2025-08-07 12:37:26 [INFO] Early stopping triggered. Best score: 0.5260
2025-08-07 12:37:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:26 [INFO] Feature importances range: [-0.0346, 0.0432]
2025-08-07 12:37:26 [INFO] Feature importances mean: 0.0069, median: 0.0088
2025-08-07 12:37:26 [INFO] Fold 7: Selected 114 features with threshold 0.001
2025-08-07 12:37:27 [INFO] Fold 7: Best validation C-index = 0.5191
2025-08-07 12:37:28 [INFO] Early stopping triggered. Best score: 0.5186
2025-08-07 12:37:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:28 [INFO] Feature importances range: [-0.0326, 0.0464]
2025-08-07 12:37:28 [INFO] Feature importances mean: 0.0072, median: 0.0074
2025-08-07 12:37:28 [INFO] Fold 8: Selected 113 features with threshold 0.001
2025-08-07 12:37:29 [INFO] Fold 8: Best validation C-index = 0.5126
2025-08-07 12:37:31 [INFO] Early stopping triggered. Best score: 0.5161
2025-08-07 12:37:31 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:31 [INFO] Feature importances range: [-0.0340, 0.0514]
2025-08-07 12:37:31 [INFO] Feature importances mean: 0.0069, median: 0.0089
2025-08-07 12:37:31 [INFO] Fold 9: Selected 112 features with threshold 0.001
2025-08-07 12:37:32 [INFO] Fold 9: Best validation C-index = 0.4857
2025-08-07 12:37:32 [INFO] Found 157 common features across 10 folds
2025-08-07 12:37:32 [INFO] Common features: ['100A_Flattop_Positive', '100A_IERROR_Peak', '100V_V280_H_Z', '100V_V280_L_X', '100V_V280_L_Y', '100V_V700_H_X', '100V_V700_L_X', '100V_V700_L_Y', '100V_V700_L_Z', '200V_V280_H_X']...
2025-08-07 12:37:32 [INFO] Trial completed: CV C-index = 0.5140 (from 10 successful folds)
2025-08-07 12:37:32 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:37:32 [INFO] Hidden dims: [63, 54], activation: selu, optimizer: Adagrad
2025-08-07 12:37:33 [INFO] Early stopping triggered. Best score: 0.5322
2025-08-07 12:37:33 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:33 [INFO] Feature importances range: [-0.0296, 0.0470]
2025-08-07 12:37:33 [INFO] Feature importances mean: 0.0009, median: 0.0002
2025-08-07 12:37:33 [INFO] Fold 0: Selected 84 features with threshold 0.001
2025-08-07 12:37:34 [INFO] Fold 0: Best validation C-index = 0.4893
2025-08-07 12:37:36 [INFO] Early stopping triggered. Best score: 0.5017
2025-08-07 12:37:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:36 [INFO] Feature importances range: [-0.0399, 0.0259]
2025-08-07 12:37:36 [INFO] Feature importances mean: -0.0014, median: -0.0015
2025-08-07 12:37:36 [INFO] Fold 1: Selected 75 features with threshold 0.001
2025-08-07 12:37:36 [INFO] Fold 1: Best validation C-index = 0.5109
2025-08-07 12:37:43 [INFO] Early stopping triggered. Best score: 0.5107
2025-08-07 12:37:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:43 [INFO] Feature importances range: [-0.0460, 0.0774]
2025-08-07 12:37:43 [INFO] Feature importances mean: 0.0054, median: 0.0031
2025-08-07 12:37:43 [INFO] Fold 2: Selected 101 features with threshold 0.001
2025-08-07 12:37:43 [INFO] Fold 2: Best validation C-index = 0.5330
2025-08-07 12:37:48 [INFO] Early stopping triggered. Best score: 0.5433
2025-08-07 12:37:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:48 [INFO] Feature importances range: [-0.0394, 0.0464]
2025-08-07 12:37:48 [INFO] Feature importances mean: 0.0023, median: 0.0018
2025-08-07 12:37:48 [INFO] Fold 3: Selected 98 features with threshold 0.001
2025-08-07 12:37:49 [INFO] Fold 3: Best validation C-index = 0.5079
2025-08-07 12:37:51 [INFO] Early stopping triggered. Best score: 0.4846
2025-08-07 12:37:51 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:51 [INFO] Feature importances range: [-0.0302, 0.0376]
2025-08-07 12:37:51 [INFO] Feature importances mean: 0.0021, median: 0.0023
2025-08-07 12:37:51 [INFO] Fold 4: Selected 96 features with threshold 0.001
2025-08-07 12:37:51 [INFO] Fold 4: Best validation C-index = 0.5463
2025-08-07 12:37:53 [INFO] Early stopping triggered. Best score: 0.5778
2025-08-07 12:37:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:53 [INFO] Feature importances range: [-0.0282, 0.0468]
2025-08-07 12:37:53 [INFO] Feature importances mean: 0.0011, median: 0.0015
2025-08-07 12:37:53 [INFO] Fold 5: Selected 95 features with threshold 0.001
2025-08-07 12:37:54 [INFO] Fold 5: Best validation C-index = 0.5300
2025-08-07 12:37:59 [INFO] Early stopping triggered. Best score: 0.5719
2025-08-07 12:37:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:37:59 [INFO] Feature importances range: [-0.0412, 0.0581]
2025-08-07 12:37:59 [INFO] Feature importances mean: 0.0034, median: 0.0029
2025-08-07 12:37:59 [INFO] Fold 6: Selected 94 features with threshold 0.001
2025-08-07 12:37:59 [INFO] Fold 6: Best validation C-index = 0.5357
2025-08-07 12:38:01 [INFO] Early stopping triggered. Best score: 0.5327
2025-08-07 12:38:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:01 [INFO] Feature importances range: [-0.0363, 0.0323]
2025-08-07 12:38:01 [INFO] Feature importances mean: -0.0013, median: -0.0012
2025-08-07 12:38:01 [INFO] Fold 7: Selected 78 features with threshold 0.001
2025-08-07 12:38:01 [INFO] Fold 7: Best validation C-index = 0.4976
2025-08-07 12:38:03 [INFO] Early stopping triggered. Best score: 0.5037
2025-08-07 12:38:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:03 [INFO] Feature importances range: [-0.0368, 0.0357]
2025-08-07 12:38:03 [INFO] Feature importances mean: 0.0021, median: 0.0012
2025-08-07 12:38:03 [INFO] Fold 8: Selected 90 features with threshold 0.001
2025-08-07 12:38:04 [INFO] Fold 8: Best validation C-index = 0.5220
2025-08-07 12:38:06 [INFO] Early stopping triggered. Best score: 0.5372
2025-08-07 12:38:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:06 [INFO] Feature importances range: [-0.0461, 0.0433]
2025-08-07 12:38:06 [INFO] Feature importances mean: 0.0018, median: 0.0008
2025-08-07 12:38:06 [INFO] Fold 9: Selected 87 features with threshold 0.001
2025-08-07 12:38:07 [INFO] Fold 9: Best validation C-index = 0.5099
2025-08-07 12:38:07 [INFO] Found 106 common features across 10 folds
2025-08-07 12:38:07 [INFO] Common features: ['100A_Flattop_Negative', '100V_V700_H_Z', '200V_V280_H_Y', '200V_V280_H_Z', '200V_V700_H_X', '200V_V700_L_Z', '300V_V280_H_Y', '300V_V280_L_X', '300V_V280_L_Z', '300V_V700_H_X']...
2025-08-07 12:38:07 [INFO] Trial completed: CV C-index = 0.5183 (from 10 successful folds)
2025-08-07 12:38:07 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:38:07 [INFO] Hidden dims: [64, 26], activation: selu, optimizer: Adagrad
2025-08-07 12:38:09 [INFO] Early stopping triggered. Best score: 0.5192
2025-08-07 12:38:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:09 [INFO] Feature importances range: [-0.1296, -0.0737]
2025-08-07 12:38:09 [INFO] Feature importances mean: -0.1008, median: -0.1012
2025-08-07 12:38:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:09 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:38:10 [INFO] Fold 0: Best validation C-index = 0.4836
2025-08-07 12:38:11 [INFO] Early stopping triggered. Best score: 0.5315
2025-08-07 12:38:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:11 [INFO] Feature importances range: [-0.1121, -0.0633]
2025-08-07 12:38:11 [INFO] Feature importances mean: -0.0880, median: -0.0877
2025-08-07 12:38:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:11 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:38:12 [INFO] Fold 1: Best validation C-index = 0.5310
2025-08-07 12:38:15 [INFO] Early stopping triggered. Best score: 0.5429
2025-08-07 12:38:15 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:15 [INFO] Feature importances range: [-0.1627, -0.1114]
2025-08-07 12:38:15 [INFO] Feature importances mean: -0.1373, median: -0.1377
2025-08-07 12:38:15 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:15 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:38:15 [INFO] Fold 2: Best validation C-index = 0.4739
2025-08-07 12:38:17 [INFO] Early stopping triggered. Best score: 0.5041
2025-08-07 12:38:17 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:17 [INFO] Feature importances range: [-0.1249, -0.0705]
2025-08-07 12:38:17 [INFO] Feature importances mean: -0.0966, median: -0.0963
2025-08-07 12:38:17 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:17 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:38:18 [INFO] Fold 3: Best validation C-index = 0.5043
2025-08-07 12:38:21 [INFO] Early stopping triggered. Best score: 0.5501
2025-08-07 12:38:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:21 [INFO] Feature importances range: [-0.1564, -0.1037]
2025-08-07 12:38:21 [INFO] Feature importances mean: -0.1323, median: -0.1323
2025-08-07 12:38:21 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:21 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:38:21 [INFO] Fold 4: Best validation C-index = 0.5148
2025-08-07 12:38:23 [INFO] Early stopping triggered. Best score: 0.5322
2025-08-07 12:38:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:23 [INFO] Feature importances range: [-0.1297, -0.0641]
2025-08-07 12:38:23 [INFO] Feature importances mean: -0.0945, median: -0.0945
2025-08-07 12:38:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:23 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:38:23 [INFO] Fold 5: Best validation C-index = 0.4821
2025-08-07 12:38:25 [INFO] Early stopping triggered. Best score: 0.5335
2025-08-07 12:38:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:25 [INFO] Feature importances range: [-0.1272, -0.0854]
2025-08-07 12:38:25 [INFO] Feature importances mean: -0.1035, median: -0.1040
2025-08-07 12:38:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:25 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:38:26 [INFO] Fold 6: Best validation C-index = 0.5107
2025-08-07 12:38:28 [INFO] Early stopping triggered. Best score: 0.5166
2025-08-07 12:38:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:28 [INFO] Feature importances range: [-0.1218, -0.0761]
2025-08-07 12:38:28 [INFO] Feature importances mean: -0.0980, median: -0.0978
2025-08-07 12:38:28 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:28 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:38:28 [INFO] Fold 7: Best validation C-index = 0.5307
2025-08-07 12:38:30 [INFO] Early stopping triggered. Best score: 0.4947
2025-08-07 12:38:30 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:30 [INFO] Feature importances range: [-0.1254, -0.0694]
2025-08-07 12:38:30 [INFO] Feature importances mean: -0.0954, median: -0.0949
2025-08-07 12:38:30 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:30 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:38:30 [INFO] Fold 8: Best validation C-index = 0.5073
2025-08-07 12:38:32 [INFO] Early stopping triggered. Best score: 0.5442
2025-08-07 12:38:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:32 [INFO] Feature importances range: [-0.1146, -0.0662]
2025-08-07 12:38:32 [INFO] Feature importances mean: -0.0918, median: -0.0922
2025-08-07 12:38:32 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:32 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:38:32 [INFO] Fold 9: Best validation C-index = 0.5393
2025-08-07 12:38:32 [INFO] Found 10 common features across 10 folds
2025-08-07 12:38:32 [INFO] Common features: ['Hysteresis', 'EPI_280V_Y1_HIGH', 'X_CLOSED_LOOP_V280_L_Z', 'EPI_280V_Y1_LOW', '100V_V700_H_Z', '100V_V700_L_Y', '420V_V700_H_X', '100V_V700_L_X', 'ICC_TEMP(T0)', 'X_CLOSED_LOOP_V700_H_Y']...
2025-08-07 12:38:32 [INFO] Trial completed: CV C-index = 0.5078 (from 10 successful folds)
2025-08-07 12:38:32 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:38:32 [INFO] Hidden dims: [13], activation: selu, optimizer: Adagrad
2025-08-07 12:38:34 [INFO] Early stopping triggered. Best score: 0.5349
2025-08-07 12:38:34 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:34 [INFO] Feature importances range: [-0.7602, -0.7375]
2025-08-07 12:38:34 [INFO] Feature importances mean: -0.7498, median: -0.7496
2025-08-07 12:38:34 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:34 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:38:34 [INFO] Fold 0: Best validation C-index = 0.4438
2025-08-07 12:38:37 [INFO] Early stopping triggered. Best score: 0.5215
2025-08-07 12:38:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:37 [INFO] Feature importances range: [-0.8659, -0.8513]
2025-08-07 12:38:37 [INFO] Feature importances mean: -0.8581, median: -0.8580
2025-08-07 12:38:37 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:37 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 12:38:37 [INFO] Fold 1: Best validation C-index = 0.4811
2025-08-07 12:38:39 [INFO] Early stopping triggered. Best score: 0.5344
2025-08-07 12:38:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:39 [INFO] Feature importances range: [-0.8659, -0.8492]
2025-08-07 12:38:39 [INFO] Feature importances mean: -0.8585, median: -0.8586
2025-08-07 12:38:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:39 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 12:38:39 [INFO] Fold 2: Best validation C-index = 0.4673
2025-08-07 12:38:41 [INFO] Early stopping triggered. Best score: 0.5215
2025-08-07 12:38:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:41 [INFO] Feature importances range: [-0.7625, -0.7403]
2025-08-07 12:38:41 [INFO] Feature importances mean: -0.7500, median: -0.7501
2025-08-07 12:38:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:41 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 12:38:41 [INFO] Fold 3: Best validation C-index = 0.5088
2025-08-07 12:38:43 [INFO] Early stopping triggered. Best score: 0.5365
2025-08-07 12:38:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:43 [INFO] Feature importances range: [-0.7651, -0.7466]
2025-08-07 12:38:43 [INFO] Feature importances mean: -0.7558, median: -0.7562
2025-08-07 12:38:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:43 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 12:38:43 [INFO] Fold 4: Best validation C-index = 0.4844
2025-08-07 12:38:45 [INFO] Early stopping triggered. Best score: 0.5201
2025-08-07 12:38:45 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:45 [INFO] Feature importances range: [-0.7664, -0.7473]
2025-08-07 12:38:45 [INFO] Feature importances mean: -0.7562, median: -0.7558
2025-08-07 12:38:45 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:45 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 12:38:45 [INFO] Fold 5: Best validation C-index = 0.4954
2025-08-07 12:38:47 [INFO] Early stopping triggered. Best score: 0.5553
2025-08-07 12:38:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:47 [INFO] Feature importances range: [-0.7885, -0.7655]
2025-08-07 12:38:47 [INFO] Feature importances mean: -0.7774, median: -0.7772
2025-08-07 12:38:47 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:47 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 12:38:47 [INFO] Fold 6: Best validation C-index = 0.5528
2025-08-07 12:38:48 [INFO] Early stopping triggered. Best score: 0.5503
2025-08-07 12:38:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:48 [INFO] Feature importances range: [-0.7559, -0.7348]
2025-08-07 12:38:48 [INFO] Feature importances mean: -0.7439, median: -0.7439
2025-08-07 12:38:48 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:48 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 12:38:49 [INFO] Fold 7: Best validation C-index = 0.4810
2025-08-07 12:38:50 [INFO] Early stopping triggered. Best score: 0.5352
2025-08-07 12:38:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:50 [INFO] Feature importances range: [-0.7635, -0.7409]
2025-08-07 12:38:50 [INFO] Feature importances mean: -0.7497, median: -0.7493
2025-08-07 12:38:50 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:50 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 12:38:51 [INFO] Fold 8: Best validation C-index = 0.5264
2025-08-07 12:38:53 [INFO] Early stopping triggered. Best score: 0.5406
2025-08-07 12:38:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:53 [INFO] Feature importances range: [-0.8577, -0.8422]
2025-08-07 12:38:53 [INFO] Feature importances mean: -0.8501, median: -0.8502
2025-08-07 12:38:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:53 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 12:38:53 [INFO] Fold 9: Best validation C-index = 0.5088
2025-08-07 12:38:53 [INFO] Found 10 common features across 10 folds
2025-08-07 12:38:53 [INFO] Common features: ['X_CLOSED_LOOP_V280_H_Y', '300V_V280_H_Y', 'EPI_280V_X1_HIGH', '420V_V700_H_X', 'Heat_Sink_Temp_Delta_T_Amplifier_Lavaflex', '420V_V700_L_X', 'Shot_Shot_Stab', '300V_V700_L_Y', 'PULSE_700V_Z1_HIGH', 'PULSE_280V_Z2_HIGH']...
2025-08-07 12:38:53 [INFO] Trial completed: CV C-index = 0.4950 (from 10 successful folds)
2025-08-07 12:38:53 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 12:38:53 [INFO] Hidden dims: [59, 54], activation: selu, optimizer: Adagrad
2025-08-07 12:38:55 [INFO] Early stopping triggered. Best score: 0.5453
2025-08-07 12:38:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:55 [INFO] Feature importances range: [-0.0553, 0.0009]
2025-08-07 12:38:55 [INFO] Feature importances mean: -0.0270, median: -0.0269
2025-08-07 12:38:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 12:38:55 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 12:38:55 [INFO] Fold 0: Best validation C-index = 0.5462
2025-08-07 12:38:56 [INFO] Early stopping triggered. Best score: 0.5363
2025-08-07 12:38:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:56 [INFO] Feature importances range: [-0.0453, 0.0087]
2025-08-07 12:38:56 [INFO] Feature importances mean: -0.0208, median: -0.0201
2025-08-07 12:38:56 [INFO] Fold 1: Selected 4 features with threshold 0.001
2025-08-07 12:38:56 [INFO] Fold 1: Best validation C-index = 0.4651
2025-08-07 12:38:58 [INFO] Early stopping triggered. Best score: 0.4909
2025-08-07 12:38:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:58 [INFO] Feature importances range: [-0.0558, 0.0061]
2025-08-07 12:38:58 [INFO] Feature importances mean: -0.0248, median: -0.0242
2025-08-07 12:38:58 [INFO] Fold 2: Selected 1 features with threshold 0.001
2025-08-07 12:38:58 [INFO] Fold 2: Best validation C-index = 0.5222
2025-08-07 12:38:59 [INFO] Early stopping triggered. Best score: 0.5151
2025-08-07 12:38:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:38:59 [INFO] Feature importances range: [-0.0477, 0.0131]
2025-08-07 12:38:59 [INFO] Feature importances mean: -0.0196, median: -0.0198
2025-08-07 12:38:59 [INFO] Fold 3: Selected 2 features with threshold 0.001
2025-08-07 12:38:59 [INFO] Fold 3: Best validation C-index = 0.4703
2025-08-07 12:39:01 [INFO] Early stopping triggered. Best score: 0.5297
2025-08-07 12:39:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:39:01 [INFO] Feature importances range: [-0.0450, 0.0055]
2025-08-07 12:39:01 [INFO] Feature importances mean: -0.0207, median: -0.0213
2025-08-07 12:39:01 [INFO] Fold 4: Selected 4 features with threshold 0.001
2025-08-07 12:39:01 [INFO] Fold 4: Best validation C-index = 0.5605
2025-08-07 12:39:02 [INFO] Early stopping triggered. Best score: 0.5239
2025-08-07 12:39:02 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 12:39:02 [INFO] Feature importances range: [-0.0533, 0.0020]
2025-08-07 12:39:02 [INFO] Feature importances mean: -0.0241, median: -0.0236
2025-08-07 12:39:02 [INFO] Fold 5: Selected 1 features with threshold 0.001
2025-08-07 12:39:03 [INFO] Fold 5: Best validation C-index = 0.5151
2025-08-07 13:17:38 [INFO] Early stopping triggered. Best score: 0.5561
2025-08-07 13:17:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:38 [INFO] Feature importances range: [-0.0489, 0.0052]
2025-08-07 13:17:38 [INFO] Feature importances mean: -0.0198, median: -0.0195
2025-08-07 13:17:38 [INFO] Fold 6: Selected 2 features with threshold 0.001
2025-08-07 13:17:39 [INFO] Fold 6: Best validation C-index = 0.5239
2025-08-07 13:17:41 [INFO] Early stopping triggered. Best score: 0.5125
2025-08-07 13:17:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:41 [INFO] Feature importances range: [-0.0606, 0.0029]
2025-08-07 13:17:41 [INFO] Feature importances mean: -0.0324, median: -0.0323
2025-08-07 13:17:41 [INFO] Fold 7: Selected 1 features with threshold 0.001
2025-08-07 13:17:41 [INFO] Fold 7: Best validation C-index = 0.5175
2025-08-07 13:17:42 [INFO] Early stopping triggered. Best score: 0.4948
2025-08-07 13:17:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:42 [INFO] Feature importances range: [-0.0514, 0.0062]
2025-08-07 13:17:42 [INFO] Feature importances mean: -0.0209, median: -0.0195
2025-08-07 13:17:42 [INFO] Fold 8: Selected 1 features with threshold 0.001
2025-08-07 13:17:43 [INFO] Fold 8: Best validation C-index = 0.4880
2025-08-07 13:17:44 [INFO] Early stopping triggered. Best score: 0.5475
2025-08-07 13:17:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:44 [INFO] Feature importances range: [-0.0525, -0.0027]
2025-08-07 13:17:44 [INFO] Feature importances mean: -0.0273, median: -0.0267
2025-08-07 13:17:44 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:44 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:17:44 [INFO] Fold 9: Best validation C-index = 0.5469
2025-08-07 13:17:44 [INFO] Found 10 common features across 10 folds
2025-08-07 13:17:44 [INFO] Common features: ['PULSE_280V_X2_HIGH', '420V_V280_L_X', 'PULSE_700V_Y1_LOW', 'Z_CLOSED_LOOP_V700_L_Y', 'EPI_280V_Z2_HIGH', '100V_V700_H_Z', 'EPI_700V_Z1_LOW', 'IERROR_Peak_Single_Axis', 'X_CLOSED_LOOP_V280_H_X', 'Z_CLOSED_LOOP_V700_L_X']...
2025-08-07 13:17:44 [INFO] Trial completed: CV C-index = 0.5156 (from 10 successful folds)
2025-08-07 13:17:44 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:17:44 [INFO] Hidden dims: [25, 40], activation: selu, optimizer: Adam
2025-08-07 13:17:46 [INFO] Early stopping triggered. Best score: 0.5504
2025-08-07 13:17:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:46 [INFO] Feature importances range: [-0.4724, -0.4197]
2025-08-07 13:17:46 [INFO] Feature importances mean: -0.4439, median: -0.4440
2025-08-07 13:17:46 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:46 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:17:46 [INFO] Fold 0: Best validation C-index = 0.5390
2025-08-07 13:17:47 [INFO] Early stopping triggered. Best score: 0.5185
2025-08-07 13:17:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:47 [INFO] Feature importances range: [-0.4494, -0.3990]
2025-08-07 13:17:47 [INFO] Feature importances mean: -0.4235, median: -0.4243
2025-08-07 13:17:47 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:47 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:17:47 [INFO] Fold 1: Best validation C-index = 0.4686
2025-08-07 13:17:49 [INFO] Early stopping triggered. Best score: 0.5316
2025-08-07 13:17:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:49 [INFO] Feature importances range: [-0.4187, -0.3737]
2025-08-07 13:17:49 [INFO] Feature importances mean: -0.3972, median: -0.3967
2025-08-07 13:17:49 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:49 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:17:49 [INFO] Fold 2: Best validation C-index = 0.5083
2025-08-07 13:17:50 [INFO] Early stopping triggered. Best score: 0.5344
2025-08-07 13:17:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:50 [INFO] Feature importances range: [-0.4387, -0.3791]
2025-08-07 13:17:50 [INFO] Feature importances mean: -0.4053, median: -0.4051
2025-08-07 13:17:50 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:50 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:17:50 [INFO] Fold 3: Best validation C-index = 0.5087
2025-08-07 13:17:52 [INFO] Early stopping triggered. Best score: 0.5479
2025-08-07 13:17:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:52 [INFO] Feature importances range: [-0.4521, -0.4025]
2025-08-07 13:17:52 [INFO] Feature importances mean: -0.4239, median: -0.4245
2025-08-07 13:17:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:52 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:17:52 [INFO] Fold 4: Best validation C-index = 0.5218
2025-08-07 13:17:53 [INFO] Early stopping triggered. Best score: 0.5465
2025-08-07 13:17:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:53 [INFO] Feature importances range: [-0.4547, -0.4100]
2025-08-07 13:17:53 [INFO] Feature importances mean: -0.4330, median: -0.4325
2025-08-07 13:17:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:53 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:17:54 [INFO] Fold 5: Best validation C-index = 0.4728
2025-08-07 13:17:55 [INFO] Early stopping triggered. Best score: 0.5553
2025-08-07 13:17:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:55 [INFO] Feature importances range: [-0.4215, -0.3787]
2025-08-07 13:17:55 [INFO] Feature importances mean: -0.3978, median: -0.3979
2025-08-07 13:17:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:55 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:17:55 [INFO] Fold 6: Best validation C-index = 0.4609
2025-08-07 13:17:57 [INFO] Early stopping triggered. Best score: 0.5499
2025-08-07 13:17:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:57 [INFO] Feature importances range: [-0.4285, -0.3787]
2025-08-07 13:17:57 [INFO] Feature importances mean: -0.4064, median: -0.4068
2025-08-07 13:17:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:57 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:17:58 [INFO] Fold 7: Best validation C-index = 0.4752
2025-08-07 13:17:59 [INFO] Early stopping triggered. Best score: 0.5125
2025-08-07 13:17:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:17:59 [INFO] Feature importances range: [-0.4461, -0.3987]
2025-08-07 13:17:59 [INFO] Feature importances mean: -0.4241, median: -0.4246
2025-08-07 13:17:59 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:17:59 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:18:00 [INFO] Fold 8: Best validation C-index = 0.5142
2025-08-07 13:18:01 [INFO] Early stopping triggered. Best score: 0.5446
2025-08-07 13:18:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:01 [INFO] Feature importances range: [-0.5114, -0.4663]
2025-08-07 13:18:01 [INFO] Feature importances mean: -0.4889, median: -0.4891
2025-08-07 13:18:01 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:01 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:18:02 [INFO] Fold 9: Best validation C-index = 0.4568
2025-08-07 13:18:02 [INFO] Found 10 common features across 10 folds
2025-08-07 13:18:02 [INFO] Common features: ['OUTPUT_VOLTAGE_DISCH_V280_H_X', 'HST_POST_TEMP', '328A_IERROR_Peak', '420V_V700_L_X', 'Heat_Sink_Temp_Delta_T_Amplifier_Lavaflex', '100V_V700_L_X', '200V_V700_L_X', 'Y_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_H_Z', '200V_V280_L_Z']...
2025-08-07 13:18:02 [INFO] Trial completed: CV C-index = 0.4926 (from 10 successful folds)
2025-08-07 13:18:02 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:18:02 [INFO] Hidden dims: [29], activation: selu, optimizer: SGD
2025-08-07 13:18:05 [INFO] Early stopping triggered. Best score: 0.4895
2025-08-07 13:18:05 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:05 [INFO] Feature importances range: [-0.0356, 0.0292]
2025-08-07 13:18:05 [INFO] Feature importances mean: -0.0003, median: -0.0002
2025-08-07 13:18:05 [INFO] Fold 0: Selected 84 features with threshold 0.001
2025-08-07 13:18:06 [INFO] Fold 0: Best validation C-index = 0.5155
2025-08-07 13:18:07 [INFO] Early stopping triggered. Best score: 0.5326
2025-08-07 13:18:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:07 [INFO] Feature importances range: [-0.0259, 0.0317]
2025-08-07 13:18:07 [INFO] Feature importances mean: -0.0011, median: -0.0004
2025-08-07 13:18:07 [INFO] Fold 1: Selected 79 features with threshold 0.001
2025-08-07 13:18:08 [INFO] Fold 1: Best validation C-index = 0.5335
2025-08-07 13:18:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:23 [INFO] Feature importances range: [-0.0301, 0.0279]
2025-08-07 13:18:23 [INFO] Feature importances mean: -0.0026, median: -0.0027
2025-08-07 13:18:23 [INFO] Fold 2: Selected 63 features with threshold 0.001
2025-08-07 13:18:24 [INFO] Fold 2: Best validation C-index = 0.4471
2025-08-07 13:18:29 [INFO] Early stopping triggered. Best score: 0.5180
2025-08-07 13:18:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:29 [INFO] Feature importances range: [-0.0230, 0.0287]
2025-08-07 13:18:29 [INFO] Feature importances mean: -0.0001, median: -0.0003
2025-08-07 13:18:29 [INFO] Fold 3: Selected 85 features with threshold 0.001
2025-08-07 13:18:30 [INFO] Fold 3: Best validation C-index = 0.4848
2025-08-07 13:18:39 [INFO] Early stopping triggered. Best score: 0.5239
2025-08-07 13:18:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:39 [INFO] Feature importances range: [-0.0271, 0.0297]
2025-08-07 13:18:39 [INFO] Feature importances mean: -0.0015, median: -0.0009
2025-08-07 13:18:39 [INFO] Fold 4: Selected 77 features with threshold 0.001
2025-08-07 13:18:40 [INFO] Fold 4: Best validation C-index = 0.5469
2025-08-07 13:18:42 [INFO] Early stopping triggered. Best score: 0.5507
2025-08-07 13:18:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:42 [INFO] Feature importances range: [-0.0224, 0.0240]
2025-08-07 13:18:42 [INFO] Feature importances mean: -0.0008, median: -0.0008
2025-08-07 13:18:42 [INFO] Fold 5: Selected 73 features with threshold 0.001
2025-08-07 13:18:43 [INFO] Fold 5: Best validation C-index = 0.4835
2025-08-07 13:18:44 [INFO] Early stopping triggered. Best score: 0.5283
2025-08-07 13:18:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:44 [INFO] Feature importances range: [-0.0298, 0.0264]
2025-08-07 13:18:44 [INFO] Feature importances mean: -0.0012, median: -0.0009
2025-08-07 13:18:44 [INFO] Fold 6: Selected 74 features with threshold 0.001
2025-08-07 13:18:45 [INFO] Fold 6: Best validation C-index = 0.4743
2025-08-07 13:18:46 [INFO] Early stopping triggered. Best score: 0.4837
2025-08-07 13:18:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:46 [INFO] Feature importances range: [-0.0245, 0.0247]
2025-08-07 13:18:46 [INFO] Feature importances mean: 0.0008, median: 0.0012
2025-08-07 13:18:46 [INFO] Fold 7: Selected 92 features with threshold 0.001
2025-08-07 13:18:46 [INFO] Fold 7: Best validation C-index = 0.5375
2025-08-07 13:18:47 [INFO] Early stopping triggered. Best score: 0.4899
2025-08-07 13:18:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:47 [INFO] Feature importances range: [-0.0326, 0.0240]
2025-08-07 13:18:47 [INFO] Feature importances mean: -0.0013, median: -0.0015
2025-08-07 13:18:47 [INFO] Fold 8: Selected 74 features with threshold 0.001
2025-08-07 13:18:48 [INFO] Fold 8: Best validation C-index = 0.4913
2025-08-07 13:18:49 [INFO] Early stopping triggered. Best score: 0.4602
2025-08-07 13:18:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:49 [INFO] Feature importances range: [-0.0284, 0.0305]
2025-08-07 13:18:49 [INFO] Feature importances mean: -0.0002, median: -0.0009
2025-08-07 13:18:49 [INFO] Fold 9: Selected 79 features with threshold 0.001
2025-08-07 13:18:50 [INFO] Fold 9: Best validation C-index = 0.4897
2025-08-07 13:18:50 [INFO] Found 84 common features across 10 folds
2025-08-07 13:18:50 [INFO] Common features: ['100A_Flattop_Positive', '100A_IERROR_Peak', '100V_V700_H_Z', '100V_V700_L_X', '100V_V700_L_Y', '200V_V280_L_Y', '200V_V700_H_Y', '200V_V700_L_Z', '300V_V280_H_Y', '300V_V280_L_Z']...
2025-08-07 13:18:50 [INFO] Trial completed: CV C-index = 0.5004 (from 10 successful folds)
2025-08-07 13:18:50 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:18:50 [INFO] Hidden dims: [35], activation: sigmoid, optimizer: Adam
2025-08-07 13:18:51 [INFO] Early stopping triggered. Best score: 0.5754
2025-08-07 13:18:51 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:51 [INFO] Feature importances range: [-2.0348, -2.0269]
2025-08-07 13:18:51 [INFO] Feature importances mean: -2.0306, median: -2.0304
2025-08-07 13:18:51 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:51 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:18:51 [INFO] Fold 0: Best validation C-index = 0.5704
2025-08-07 13:18:52 [INFO] Early stopping triggered. Best score: 0.5658
2025-08-07 13:18:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:52 [INFO] Feature importances range: [-1.9911, -1.9817]
2025-08-07 13:18:52 [INFO] Feature importances mean: -1.9860, median: -1.9861
2025-08-07 13:18:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:52 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:18:53 [INFO] Fold 1: Best validation C-index = 0.5387
2025-08-07 13:18:54 [INFO] Early stopping triggered. Best score: 0.5248
2025-08-07 13:18:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:54 [INFO] Feature importances range: [-2.0339, -2.0261]
2025-08-07 13:18:54 [INFO] Feature importances mean: -2.0301, median: -2.0300
2025-08-07 13:18:54 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:54 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:18:54 [INFO] Fold 2: Best validation C-index = 0.5177
2025-08-07 13:18:55 [INFO] Early stopping triggered. Best score: 0.5332
2025-08-07 13:18:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:55 [INFO] Feature importances range: [-2.0468, -2.0398]
2025-08-07 13:18:55 [INFO] Feature importances mean: -2.0432, median: -2.0432
2025-08-07 13:18:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:55 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:18:56 [INFO] Fold 3: Best validation C-index = 0.4762
2025-08-07 13:18:57 [INFO] Early stopping triggered. Best score: 0.5321
2025-08-07 13:18:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:57 [INFO] Feature importances range: [-1.9567, -1.9447]
2025-08-07 13:18:57 [INFO] Feature importances mean: -1.9508, median: -1.9511
2025-08-07 13:18:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:57 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:18:57 [INFO] Fold 4: Best validation C-index = 0.4892
2025-08-07 13:18:58 [INFO] Early stopping triggered. Best score: 0.5504
2025-08-07 13:18:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:18:58 [INFO] Feature importances range: [-2.0476, -2.0398]
2025-08-07 13:18:58 [INFO] Feature importances mean: -2.0435, median: -2.0435
2025-08-07 13:18:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:18:58 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:18:59 [INFO] Fold 5: Best validation C-index = 0.5539
2025-08-07 13:19:00 [INFO] Early stopping triggered. Best score: 0.5484
2025-08-07 13:19:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:00 [INFO] Feature importances range: [-1.9559, -1.9442]
2025-08-07 13:19:00 [INFO] Feature importances mean: -1.9510, median: -1.9509
2025-08-07 13:19:00 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:00 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:19:00 [INFO] Fold 6: Best validation C-index = 0.5079
2025-08-07 13:19:01 [INFO] Early stopping triggered. Best score: 0.5152
2025-08-07 13:19:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:01 [INFO] Feature importances range: [-2.0064, -1.9960]
2025-08-07 13:19:01 [INFO] Feature importances mean: -2.0016, median: -2.0016
2025-08-07 13:19:01 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:01 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:19:02 [INFO] Fold 7: Best validation C-index = 0.4852
2025-08-07 13:19:03 [INFO] Early stopping triggered. Best score: 0.5475
2025-08-07 13:19:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:03 [INFO] Feature importances range: [-2.0219, -2.0121]
2025-08-07 13:19:03 [INFO] Feature importances mean: -2.0166, median: -2.0167
2025-08-07 13:19:03 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:03 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:19:03 [INFO] Fold 8: Best validation C-index = 0.5294
2025-08-07 13:19:04 [INFO] Early stopping triggered. Best score: 0.5605
2025-08-07 13:19:04 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:04 [INFO] Feature importances range: [-1.9748, -1.9632]
2025-08-07 13:19:04 [INFO] Feature importances mean: -1.9687, median: -1.9686
2025-08-07 13:19:04 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:04 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:19:05 [INFO] Fold 9: Best validation C-index = 0.4624
2025-08-07 13:19:05 [INFO] Found 10 common features across 10 folds
2025-08-07 13:19:05 [INFO] Common features: ['PULSE_280V_Z2_HIGH', 'X_CLOSED_LOOP_V700_L_Z', '100V_V700_L_Y', '200V_V700_L_Z', 'EPI_700V_Y2_HIGH', '420V_V280_L_Y', '200V_V280_L_Z', 'X_CLOSED_LOOP_V280_H_Z', 'BUS_VOLTAGE_VX_LOW', '420V_V280_H_Y']...
2025-08-07 13:19:05 [INFO] Trial completed: CV C-index = 0.5131 (from 10 successful folds)
2025-08-07 13:19:05 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:19:05 [INFO] Hidden dims: [59, 54], activation: selu, optimizer: Adagrad
2025-08-07 13:19:06 [INFO] Early stopping triggered. Best score: 0.5170
2025-08-07 13:19:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:06 [INFO] Feature importances range: [-0.0498, 0.0131]
2025-08-07 13:19:06 [INFO] Feature importances mean: -0.0174, median: -0.0175
2025-08-07 13:19:06 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:19:06 [INFO] Fold 0: Best validation C-index = 0.4820
2025-08-07 13:19:08 [INFO] Early stopping triggered. Best score: 0.5309
2025-08-07 13:19:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:08 [INFO] Feature importances range: [-0.0480, 0.0097]
2025-08-07 13:19:08 [INFO] Feature importances mean: -0.0234, median: -0.0238
2025-08-07 13:19:08 [INFO] Fold 1: Selected 3 features with threshold 0.001
2025-08-07 13:19:08 [INFO] Fold 1: Best validation C-index = 0.5241
2025-08-07 13:19:10 [INFO] Early stopping triggered. Best score: 0.5177
2025-08-07 13:19:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:10 [INFO] Feature importances range: [-0.0448, 0.0061]
2025-08-07 13:19:10 [INFO] Feature importances mean: -0.0192, median: -0.0189
2025-08-07 13:19:10 [INFO] Fold 2: Selected 4 features with threshold 0.001
2025-08-07 13:19:10 [INFO] Fold 2: Best validation C-index = 0.5003
2025-08-07 13:19:11 [INFO] Early stopping triggered. Best score: 0.4994
2025-08-07 13:19:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:11 [INFO] Feature importances range: [-0.0418, 0.0052]
2025-08-07 13:19:11 [INFO] Feature importances mean: -0.0220, median: -0.0224
2025-08-07 13:19:11 [INFO] Fold 3: Selected 1 features with threshold 0.001
2025-08-07 13:19:12 [INFO] Fold 3: Best validation C-index = 0.4729
2025-08-07 13:19:13 [INFO] Early stopping triggered. Best score: 0.5001
2025-08-07 13:19:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:13 [INFO] Feature importances range: [-0.0421, 0.0106]
2025-08-07 13:19:13 [INFO] Feature importances mean: -0.0178, median: -0.0164
2025-08-07 13:19:13 [INFO] Fold 4: Selected 2 features with threshold 0.001
2025-08-07 13:19:13 [INFO] Fold 4: Best validation C-index = 0.4851
2025-08-07 13:19:14 [INFO] Early stopping triggered. Best score: 0.5348
2025-08-07 13:19:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:14 [INFO] Feature importances range: [-0.0377, 0.0154]
2025-08-07 13:19:14 [INFO] Feature importances mean: -0.0174, median: -0.0181
2025-08-07 13:19:14 [INFO] Fold 5: Selected 7 features with threshold 0.001
2025-08-07 13:19:14 [INFO] Fold 5: Best validation C-index = 0.5163
2025-08-07 13:19:16 [INFO] Early stopping triggered. Best score: 0.5404
2025-08-07 13:19:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:16 [INFO] Feature importances range: [-0.0434, 0.0111]
2025-08-07 13:19:16 [INFO] Feature importances mean: -0.0186, median: -0.0186
2025-08-07 13:19:16 [INFO] Fold 6: Selected 3 features with threshold 0.001
2025-08-07 13:19:16 [INFO] Fold 6: Best validation C-index = 0.4913
2025-08-07 13:19:17 [INFO] Early stopping triggered. Best score: 0.5164
2025-08-07 13:19:17 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:17 [INFO] Feature importances range: [-0.0484, 0.0041]
2025-08-07 13:19:17 [INFO] Feature importances mean: -0.0200, median: -0.0201
2025-08-07 13:19:17 [INFO] Fold 7: Selected 3 features with threshold 0.001
2025-08-07 13:19:18 [INFO] Fold 7: Best validation C-index = 0.4695
2025-08-07 13:19:19 [INFO] Early stopping triggered. Best score: 0.4980
2025-08-07 13:19:19 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:19 [INFO] Feature importances range: [-0.0449, 0.0064]
2025-08-07 13:19:19 [INFO] Feature importances mean: -0.0212, median: -0.0207
2025-08-07 13:19:19 [INFO] Fold 8: Selected 1 features with threshold 0.001
2025-08-07 13:19:19 [INFO] Fold 8: Best validation C-index = 0.5127
2025-08-07 13:19:21 [INFO] Early stopping triggered. Best score: 0.5350
2025-08-07 13:19:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:21 [INFO] Feature importances range: [-0.0502, 0.0105]
2025-08-07 13:19:21 [INFO] Feature importances mean: -0.0191, median: -0.0185
2025-08-07 13:19:21 [INFO] Fold 9: Selected 8 features with threshold 0.001
2025-08-07 13:19:21 [INFO] Fold 9: Best validation C-index = 0.5080
2025-08-07 13:19:21 [INFO] Found 10 common features across 10 folds
2025-08-07 13:19:21 [INFO] Common features: ['420V_V700_H_Z', 'PULSE_280V_Y2_HIGH', '300V_V280_H_X', '420V_V280_L_X', 'EPI_280V_X1_HIGH', 'EPI_280V_Z1_HIGH', 'HST_POST_TEMP', 'Heat_Sink_Temp_Delta_T_Amplifier_Lavaflex', 'OUTPUT_VOLTAGE_DISCH_V700_L_Z', 'Y_CLOSED_LOOP_V280_L_Z']...
2025-08-07 13:19:21 [INFO] Trial completed: CV C-index = 0.4962 (from 10 successful folds)
2025-08-07 13:19:21 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:19:21 [INFO] Hidden dims: [54, 53], activation: selu, optimizer: Adagrad
2025-08-07 13:19:24 [INFO] Early stopping triggered. Best score: 0.5792
2025-08-07 13:19:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:24 [INFO] Feature importances range: [-0.0525, -0.0001]
2025-08-07 13:19:24 [INFO] Feature importances mean: -0.0248, median: -0.0244
2025-08-07 13:19:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:24 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:19:25 [INFO] Fold 0: Best validation C-index = 0.5175
2025-08-07 13:19:26 [INFO] Early stopping triggered. Best score: 0.5574
2025-08-07 13:19:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:26 [INFO] Feature importances range: [-0.0425, 0.0171]
2025-08-07 13:19:26 [INFO] Feature importances mean: -0.0136, median: -0.0136
2025-08-07 13:19:26 [INFO] Fold 1: Selected 13 features with threshold 0.001
2025-08-07 13:19:26 [INFO] Fold 1: Best validation C-index = 0.5207
2025-08-07 13:19:27 [INFO] Early stopping triggered. Best score: 0.5160
2025-08-07 13:19:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:27 [INFO] Feature importances range: [-0.0387, 0.0148]
2025-08-07 13:19:27 [INFO] Feature importances mean: -0.0141, median: -0.0142
2025-08-07 13:19:27 [INFO] Fold 2: Selected 12 features with threshold 0.001
2025-08-07 13:19:28 [INFO] Fold 2: Best validation C-index = 0.5174
2025-08-07 13:19:29 [INFO] Early stopping triggered. Best score: 0.5350
2025-08-07 13:19:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:29 [INFO] Feature importances range: [-0.0388, 0.0128]
2025-08-07 13:19:29 [INFO] Feature importances mean: -0.0153, median: -0.0159
2025-08-07 13:19:29 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:19:30 [INFO] Fold 3: Best validation C-index = 0.4682
2025-08-07 13:19:32 [INFO] Early stopping triggered. Best score: 0.5497
2025-08-07 13:19:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:32 [INFO] Feature importances range: [-0.0417, 0.0086]
2025-08-07 13:19:32 [INFO] Feature importances mean: -0.0148, median: -0.0142
2025-08-07 13:19:32 [INFO] Fold 4: Selected 9 features with threshold 0.001
2025-08-07 13:19:32 [INFO] Fold 4: Best validation C-index = 0.5055
2025-08-07 13:19:34 [INFO] Early stopping triggered. Best score: 0.5392
2025-08-07 13:19:34 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:34 [INFO] Feature importances range: [-0.0486, 0.0100]
2025-08-07 13:19:34 [INFO] Feature importances mean: -0.0140, median: -0.0141
2025-08-07 13:19:34 [INFO] Fold 5: Selected 12 features with threshold 0.001
2025-08-07 13:19:34 [INFO] Fold 5: Best validation C-index = 0.5221
2025-08-07 13:19:36 [INFO] Early stopping triggered. Best score: 0.5417
2025-08-07 13:19:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:36 [INFO] Feature importances range: [-0.0368, 0.0145]
2025-08-07 13:19:36 [INFO] Feature importances mean: -0.0128, median: -0.0127
2025-08-07 13:19:36 [INFO] Fold 6: Selected 14 features with threshold 0.001
2025-08-07 13:19:37 [INFO] Fold 6: Best validation C-index = 0.4852
2025-08-07 13:19:38 [INFO] Early stopping triggered. Best score: 0.5133
2025-08-07 13:19:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:38 [INFO] Feature importances range: [-0.0471, 0.0093]
2025-08-07 13:19:38 [INFO] Feature importances mean: -0.0124, median: -0.0120
2025-08-07 13:19:38 [INFO] Fold 7: Selected 15 features with threshold 0.001
2025-08-07 13:19:39 [INFO] Fold 7: Best validation C-index = 0.4904
2025-08-07 13:19:43 [INFO] Early stopping triggered. Best score: 0.4945
2025-08-07 13:19:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:43 [INFO] Feature importances range: [-0.0568, 0.0102]
2025-08-07 13:19:43 [INFO] Feature importances mean: -0.0234, median: -0.0231
2025-08-07 13:19:43 [INFO] Fold 8: Selected 1 features with threshold 0.001
2025-08-07 13:19:44 [INFO] Fold 8: Best validation C-index = 0.4747
2025-08-07 13:19:46 [INFO] Early stopping triggered. Best score: 0.5877
2025-08-07 13:19:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:46 [INFO] Feature importances range: [-0.0428, 0.0097]
2025-08-07 13:19:46 [INFO] Feature importances mean: -0.0151, median: -0.0142
2025-08-07 13:19:46 [INFO] Fold 9: Selected 9 features with threshold 0.001
2025-08-07 13:19:46 [INFO] Fold 9: Best validation C-index = 0.5487
2025-08-07 13:19:46 [INFO] Found 5 common features across 10 folds
2025-08-07 13:19:46 [INFO] Common features: ['Z_CLOSED_LOOP_V700_L_Y', '420V_V280_H_Y', 'Heat_Sink_Temp_PS_Initial', 'ICC_TEMP(T0)', '200V_V700_H_X']...
2025-08-07 13:19:46 [INFO] Trial completed: CV C-index = 0.5050 (from 10 successful folds)
2025-08-07 13:19:46 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:19:46 [INFO] Hidden dims: [61, 63], activation: tanh, optimizer: Adagrad
2025-08-07 13:19:49 [INFO] Early stopping triggered. Best score: 0.5485
2025-08-07 13:19:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:49 [INFO] Feature importances range: [-0.5130, -0.4851]
2025-08-07 13:19:49 [INFO] Feature importances mean: -0.4990, median: -0.4991
2025-08-07 13:19:49 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:49 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:19:49 [INFO] Fold 0: Best validation C-index = 0.4848
2025-08-07 13:19:50 [INFO] Early stopping triggered. Best score: 0.5369
2025-08-07 13:19:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:50 [INFO] Feature importances range: [-0.3469, -0.3107]
2025-08-07 13:19:50 [INFO] Feature importances mean: -0.3289, median: -0.3292
2025-08-07 13:19:50 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:50 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:19:51 [INFO] Fold 1: Best validation C-index = 0.4450
2025-08-07 13:19:52 [INFO] Early stopping triggered. Best score: 0.5490
2025-08-07 13:19:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:52 [INFO] Feature importances range: [-0.3380, -0.2965]
2025-08-07 13:19:52 [INFO] Feature importances mean: -0.3180, median: -0.3179
2025-08-07 13:19:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:52 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:19:52 [INFO] Fold 2: Best validation C-index = 0.5596
2025-08-07 13:19:53 [INFO] Early stopping triggered. Best score: 0.5400
2025-08-07 13:19:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:53 [INFO] Feature importances range: [-0.3522, -0.3078]
2025-08-07 13:19:53 [INFO] Feature importances mean: -0.3283, median: -0.3279
2025-08-07 13:19:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:53 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:19:54 [INFO] Fold 3: Best validation C-index = 0.4896
2025-08-07 13:19:55 [INFO] Early stopping triggered. Best score: 0.5264
2025-08-07 13:19:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:55 [INFO] Feature importances range: [-0.3200, -0.2841]
2025-08-07 13:19:55 [INFO] Feature importances mean: -0.3017, median: -0.3013
2025-08-07 13:19:55 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:55 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:19:55 [INFO] Fold 4: Best validation C-index = 0.4912
2025-08-07 13:19:56 [INFO] Early stopping triggered. Best score: 0.5330
2025-08-07 13:19:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:56 [INFO] Feature importances range: [-0.3297, -0.2822]
2025-08-07 13:19:56 [INFO] Feature importances mean: -0.3081, median: -0.3077
2025-08-07 13:19:56 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:56 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:19:57 [INFO] Fold 5: Best validation C-index = 0.4802
2025-08-07 13:19:58 [INFO] Early stopping triggered. Best score: 0.5142
2025-08-07 13:19:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:58 [INFO] Feature importances range: [-0.3272, -0.2834]
2025-08-07 13:19:58 [INFO] Feature importances mean: -0.3018, median: -0.3026
2025-08-07 13:19:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:58 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:19:58 [INFO] Fold 6: Best validation C-index = 0.5065
2025-08-07 13:19:59 [INFO] Early stopping triggered. Best score: 0.5267
2025-08-07 13:19:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:19:59 [INFO] Feature importances range: [-0.3695, -0.3281]
2025-08-07 13:19:59 [INFO] Feature importances mean: -0.3533, median: -0.3536
2025-08-07 13:19:59 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:19:59 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:20:00 [INFO] Fold 7: Best validation C-index = 0.4856
2025-08-07 13:20:01 [INFO] Early stopping triggered. Best score: 0.5421
2025-08-07 13:20:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:01 [INFO] Feature importances range: [-0.3995, -0.3626]
2025-08-07 13:20:01 [INFO] Feature importances mean: -0.3816, median: -0.3815
2025-08-07 13:20:01 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:20:01 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:20:01 [INFO] Fold 8: Best validation C-index = 0.5320
2025-08-07 13:20:03 [INFO] Early stopping triggered. Best score: 0.5414
2025-08-07 13:20:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:03 [INFO] Feature importances range: [-0.3938, -0.3533]
2025-08-07 13:20:03 [INFO] Feature importances mean: -0.3769, median: -0.3770
2025-08-07 13:20:03 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:20:03 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:20:03 [INFO] Fold 9: Best validation C-index = 0.5292
2025-08-07 13:20:03 [INFO] Found 5 common features across 10 folds
2025-08-07 13:20:03 [INFO] Common features: ['Y_CLOSED_LOOP_V280_H_X', 'PULSE_700V_X1_LOW', 'Y_CLOSED_LOOP_V700_H_X', 'Y_CLOSED_LOOP_V700_H_Y', 'PULSE_700V_Z1_LOW']...
2025-08-07 13:20:03 [INFO] Trial completed: CV C-index = 0.5004 (from 10 successful folds)
2025-08-07 13:20:03 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:20:03 [INFO] Hidden dims: [58, 47], activation: selu, optimizer: Adagrad
2025-08-07 13:20:04 [INFO] Early stopping triggered. Best score: 0.5044
2025-08-07 13:20:04 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:04 [INFO] Feature importances range: [-0.0266, 0.0228]
2025-08-07 13:20:04 [INFO] Feature importances mean: -0.0022, median: -0.0014
2025-08-07 13:20:04 [INFO] Fold 0: Selected 68 features with threshold 0.001
2025-08-07 13:20:05 [INFO] Fold 0: Best validation C-index = 0.4715
2025-08-07 13:20:06 [INFO] Early stopping triggered. Best score: 0.5103
2025-08-07 13:20:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:06 [INFO] Feature importances range: [-0.0318, 0.0194]
2025-08-07 13:20:06 [INFO] Feature importances mean: -0.0026, median: -0.0017
2025-08-07 13:20:06 [INFO] Fold 1: Selected 73 features with threshold 0.001
2025-08-07 13:20:06 [INFO] Fold 1: Best validation C-index = 0.4989
2025-08-07 13:20:07 [INFO] Early stopping triggered. Best score: 0.5145
2025-08-07 13:20:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:07 [INFO] Feature importances range: [-0.0297, 0.0205]
2025-08-07 13:20:07 [INFO] Feature importances mean: -0.0023, median: -0.0015
2025-08-07 13:20:07 [INFO] Fold 2: Selected 70 features with threshold 0.001
2025-08-07 13:20:08 [INFO] Fold 2: Best validation C-index = 0.4948
2025-08-07 13:20:09 [INFO] Early stopping triggered. Best score: 0.5445
2025-08-07 13:20:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:09 [INFO] Feature importances range: [-0.0311, 0.0301]
2025-08-07 13:20:09 [INFO] Feature importances mean: -0.0012, median: -0.0005
2025-08-07 13:20:09 [INFO] Fold 3: Selected 77 features with threshold 0.001
2025-08-07 13:20:09 [INFO] Fold 3: Best validation C-index = 0.5066
2025-08-07 13:20:11 [INFO] Early stopping triggered. Best score: 0.5418
2025-08-07 13:20:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:11 [INFO] Feature importances range: [-0.0352, 0.0177]
2025-08-07 13:20:11 [INFO] Feature importances mean: -0.0041, median: -0.0039
2025-08-07 13:20:11 [INFO] Fold 4: Selected 54 features with threshold 0.001
2025-08-07 13:20:12 [INFO] Fold 4: Best validation C-index = 0.4819
2025-08-07 13:20:13 [INFO] Early stopping triggered. Best score: 0.4658
2025-08-07 13:20:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:13 [INFO] Feature importances range: [-0.0297, 0.0263]
2025-08-07 13:20:13 [INFO] Feature importances mean: -0.0031, median: -0.0032
2025-08-07 13:20:13 [INFO] Fold 5: Selected 66 features with threshold 0.001
2025-08-07 13:20:13 [INFO] Fold 5: Best validation C-index = 0.4910
2025-08-07 13:20:17 [INFO] Early stopping triggered. Best score: 0.5176
2025-08-07 13:20:17 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:17 [INFO] Feature importances range: [-0.0310, 0.0218]
2025-08-07 13:20:17 [INFO] Feature importances mean: -0.0045, median: -0.0048
2025-08-07 13:20:17 [INFO] Fold 6: Selected 59 features with threshold 0.001
2025-08-07 13:20:17 [INFO] Fold 6: Best validation C-index = 0.5349
2025-08-07 13:20:20 [INFO] Early stopping triggered. Best score: 0.5149
2025-08-07 13:20:20 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:20 [INFO] Feature importances range: [-0.0262, 0.0237]
2025-08-07 13:20:20 [INFO] Feature importances mean: -0.0052, median: -0.0053
2025-08-07 13:20:20 [INFO] Fold 7: Selected 45 features with threshold 0.001
2025-08-07 13:20:21 [INFO] Fold 7: Best validation C-index = 0.5338
2025-08-07 13:20:23 [INFO] Early stopping triggered. Best score: 0.4800
2025-08-07 13:20:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:23 [INFO] Feature importances range: [-0.0249, 0.0333]
2025-08-07 13:20:23 [INFO] Feature importances mean: -0.0019, median: -0.0026
2025-08-07 13:20:23 [INFO] Fold 8: Selected 62 features with threshold 0.001
2025-08-07 13:20:23 [INFO] Fold 8: Best validation C-index = 0.5120
2025-08-07 13:20:28 [INFO] Early stopping triggered. Best score: 0.4773
2025-08-07 13:20:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:28 [INFO] Feature importances range: [-0.0310, 0.0183]
2025-08-07 13:20:28 [INFO] Feature importances mean: -0.0052, median: -0.0055
2025-08-07 13:20:28 [INFO] Fold 9: Selected 48 features with threshold 0.001
2025-08-07 13:20:29 [INFO] Fold 9: Best validation C-index = 0.5727
2025-08-07 13:20:29 [INFO] Found 44 common features across 10 folds
2025-08-07 13:20:29 [INFO] Common features: ['100A_IERROR_Peak', '100V_V280_L_Y', '100V_V700_L_Y', '200V_V280_L_X', '300V_V700_H_X', '300V_V700_H_Y', '300V_V700_L_Z', '328A_IERROR_RMS', '420V_V700_H_Z', 'BUS_VOLTAGE_VZ_LOW']...
2025-08-07 13:20:29 [INFO] Trial completed: CV C-index = 0.5098 (from 10 successful folds)
2025-08-07 13:20:29 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:20:29 [INFO] Hidden dims: [49, 64, 55], activation: selu, optimizer: Adagrad
2025-08-07 13:20:29 [INFO] Early stopping triggered. Best score: 0.4981
2025-08-07 13:20:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:29 [INFO] Feature importances range: [-0.0578, 0.0262]
2025-08-07 13:20:29 [INFO] Feature importances mean: -0.0097, median: -0.0083
2025-08-07 13:20:29 [INFO] Fold 0: Selected 46 features with threshold 0.001
2025-08-07 13:20:30 [INFO] Fold 0: Best validation C-index = 0.5340
2025-08-07 13:20:32 [INFO] Early stopping triggered. Best score: 0.5232
2025-08-07 13:20:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:32 [INFO] Feature importances range: [-0.0656, 0.0644]
2025-08-07 13:20:32 [INFO] Feature importances mean: -0.0137, median: -0.0128
2025-08-07 13:20:32 [INFO] Fold 1: Selected 40 features with threshold 0.001
2025-08-07 13:20:32 [INFO] Fold 1: Best validation C-index = 0.5236
2025-08-07 13:20:34 [INFO] Early stopping triggered. Best score: 0.5258
2025-08-07 13:20:34 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:34 [INFO] Feature importances range: [-0.0842, 0.0456]
2025-08-07 13:20:34 [INFO] Feature importances mean: -0.0124, median: -0.0123
2025-08-07 13:20:34 [INFO] Fold 2: Selected 48 features with threshold 0.001
2025-08-07 13:20:35 [INFO] Fold 2: Best validation C-index = 0.4978
2025-08-07 13:20:36 [INFO] Early stopping triggered. Best score: 0.5349
2025-08-07 13:20:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:36 [INFO] Feature importances range: [-0.0550, 0.0310]
2025-08-07 13:20:36 [INFO] Feature importances mean: -0.0083, median: -0.0070
2025-08-07 13:20:36 [INFO] Fold 3: Selected 56 features with threshold 0.001
2025-08-07 13:20:37 [INFO] Fold 3: Best validation C-index = 0.4577
2025-08-07 13:20:37 [INFO] Early stopping triggered. Best score: 0.5559
2025-08-07 13:20:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:37 [INFO] Feature importances range: [-0.0504, 0.0227]
2025-08-07 13:20:37 [INFO] Feature importances mean: -0.0087, median: -0.0081
2025-08-07 13:20:37 [INFO] Fold 4: Selected 54 features with threshold 0.001
2025-08-07 13:20:38 [INFO] Fold 4: Best validation C-index = 0.5133
2025-08-07 13:20:39 [INFO] Early stopping triggered. Best score: 0.5138
2025-08-07 13:20:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:39 [INFO] Feature importances range: [-0.0656, 0.0378]
2025-08-07 13:20:39 [INFO] Feature importances mean: -0.0099, median: -0.0081
2025-08-07 13:20:39 [INFO] Fold 5: Selected 48 features with threshold 0.001
2025-08-07 13:20:40 [INFO] Fold 5: Best validation C-index = 0.4562
2025-08-07 13:20:41 [INFO] Early stopping triggered. Best score: 0.5227
2025-08-07 13:20:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:41 [INFO] Feature importances range: [-0.0383, 0.0345]
2025-08-07 13:20:41 [INFO] Feature importances mean: -0.0057, median: -0.0047
2025-08-07 13:20:41 [INFO] Fold 6: Selected 53 features with threshold 0.001
2025-08-07 13:20:42 [INFO] Fold 6: Best validation C-index = 0.5060
2025-08-07 13:20:46 [INFO] Early stopping triggered. Best score: 0.5361
2025-08-07 13:20:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:46 [INFO] Feature importances range: [-0.0871, 0.0491]
2025-08-07 13:20:46 [INFO] Feature importances mean: -0.0160, median: -0.0121
2025-08-07 13:20:46 [INFO] Fold 7: Selected 40 features with threshold 0.001
2025-08-07 13:20:46 [INFO] Fold 7: Best validation C-index = 0.5042
2025-08-07 13:20:48 [INFO] Early stopping triggered. Best score: 0.5407
2025-08-07 13:20:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:48 [INFO] Feature importances range: [-0.0638, 0.0355]
2025-08-07 13:20:48 [INFO] Feature importances mean: -0.0132, median: -0.0132
2025-08-07 13:20:48 [INFO] Fold 8: Selected 36 features with threshold 0.001
2025-08-07 13:20:49 [INFO] Fold 8: Best validation C-index = 0.4916
2025-08-07 13:20:50 [INFO] Early stopping triggered. Best score: 0.5268
2025-08-07 13:20:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:50 [INFO] Feature importances range: [-0.0553, 0.0257]
2025-08-07 13:20:50 [INFO] Feature importances mean: -0.0046, median: -0.0032
2025-08-07 13:20:50 [INFO] Fold 9: Selected 67 features with threshold 0.001
2025-08-07 13:20:51 [INFO] Fold 9: Best validation C-index = 0.5428
2025-08-07 13:20:51 [INFO] Found 22 common features across 10 folds
2025-08-07 13:20:51 [INFO] Common features: ['100V_V280_L_Y', '200V_V700_H_Z', '300V_V280_H_Z', '300V_V700_L_Y', '420V_V280_L_Z', 'EPI_280V_Z2_HIGH', 'EPI_700V_Z2_HIGH', 'IERROR_Peak_Single_Axis', 'OUTPUT_VOLTAGE_DISCH_V700_L_Z', 'PULSE_280V_Y2_LOW']...
2025-08-07 13:20:51 [INFO] Trial completed: CV C-index = 0.5027 (from 10 successful folds)
2025-08-07 13:20:51 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:20:51 [INFO] Hidden dims: [43, 43], activation: selu, optimizer: Adagrad
2025-08-07 13:20:52 [INFO] Early stopping triggered. Best score: 0.5098
2025-08-07 13:20:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:52 [INFO] Feature importances range: [-0.0268, 0.0286]
2025-08-07 13:20:52 [INFO] Feature importances mean: -0.0004, median: -0.0006
2025-08-07 13:20:52 [INFO] Fold 0: Selected 77 features with threshold 0.001
2025-08-07 13:20:53 [INFO] Fold 0: Best validation C-index = 0.5216
2025-08-07 13:20:55 [INFO] Early stopping triggered. Best score: 0.4591
2025-08-07 13:20:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:55 [INFO] Feature importances range: [-0.0234, 0.0239]
2025-08-07 13:20:55 [INFO] Feature importances mean: 0.0002, median: 0.0005
2025-08-07 13:20:55 [INFO] Fold 1: Selected 82 features with threshold 0.001
2025-08-07 13:20:56 [INFO] Fold 1: Best validation C-index = 0.4835
2025-08-07 13:20:57 [INFO] Early stopping triggered. Best score: 0.5064
2025-08-07 13:20:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:20:57 [INFO] Feature importances range: [-0.0315, 0.0252]
2025-08-07 13:20:57 [INFO] Feature importances mean: -0.0012, median: -0.0012
2025-08-07 13:20:57 [INFO] Fold 2: Selected 74 features with threshold 0.001
2025-08-07 13:20:58 [INFO] Fold 2: Best validation C-index = 0.5113
2025-08-07 13:21:00 [INFO] Early stopping triggered. Best score: 0.4589
2025-08-07 13:21:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:00 [INFO] Feature importances range: [-0.0251, 0.0345]
2025-08-07 13:21:00 [INFO] Feature importances mean: -0.0013, median: -0.0010
2025-08-07 13:21:00 [INFO] Fold 3: Selected 74 features with threshold 0.001
2025-08-07 13:21:00 [INFO] Fold 3: Best validation C-index = 0.4268
2025-08-07 13:21:02 [INFO] Early stopping triggered. Best score: 0.4333
2025-08-07 13:21:02 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:02 [INFO] Feature importances range: [-0.0253, 0.0262]
2025-08-07 13:21:02 [INFO] Feature importances mean: -0.0006, median: -0.0012
2025-08-07 13:21:02 [INFO] Fold 4: Selected 76 features with threshold 0.001
2025-08-07 13:21:03 [INFO] Fold 4: Best validation C-index = 0.4751
2025-08-07 13:21:04 [INFO] Early stopping triggered. Best score: 0.4682
2025-08-07 13:21:04 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:04 [INFO] Feature importances range: [-0.0282, 0.0263]
2025-08-07 13:21:04 [INFO] Feature importances mean: -0.0001, median: 0.0005
2025-08-07 13:21:04 [INFO] Fold 5: Selected 87 features with threshold 0.001
2025-08-07 13:21:05 [INFO] Fold 5: Best validation C-index = 0.4977
2025-08-07 13:21:07 [INFO] Early stopping triggered. Best score: 0.5052
2025-08-07 13:21:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:07 [INFO] Feature importances range: [-0.0314, 0.0263]
2025-08-07 13:21:07 [INFO] Feature importances mean: -0.0010, median: -0.0009
2025-08-07 13:21:07 [INFO] Fold 6: Selected 81 features with threshold 0.001
2025-08-07 13:21:08 [INFO] Fold 6: Best validation C-index = 0.5061
2025-08-07 13:21:08 [INFO] Early stopping triggered. Best score: 0.5051
2025-08-07 13:21:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:08 [INFO] Feature importances range: [-0.0225, 0.0267]
2025-08-07 13:21:08 [INFO] Feature importances mean: -0.0001, median: -0.0004
2025-08-07 13:21:08 [INFO] Fold 7: Selected 77 features with threshold 0.001
2025-08-07 13:21:09 [INFO] Fold 7: Best validation C-index = 0.5138
2025-08-07 13:21:10 [INFO] Early stopping triggered. Best score: 0.5047
2025-08-07 13:21:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:10 [INFO] Feature importances range: [-0.0226, 0.0212]
2025-08-07 13:21:10 [INFO] Feature importances mean: 0.0005, median: 0.0008
2025-08-07 13:21:10 [INFO] Fold 8: Selected 88 features with threshold 0.001
2025-08-07 13:21:10 [INFO] Fold 8: Best validation C-index = 0.5276
2025-08-07 13:21:11 [INFO] Early stopping triggered. Best score: 0.5238
2025-08-07 13:21:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:11 [INFO] Feature importances range: [-0.0276, 0.0287]
2025-08-07 13:21:11 [INFO] Feature importances mean: 0.0002, median: -0.0002
2025-08-07 13:21:11 [INFO] Fold 9: Selected 83 features with threshold 0.001
2025-08-07 13:21:12 [INFO] Fold 9: Best validation C-index = 0.5529
2025-08-07 13:21:12 [INFO] Found 84 common features across 10 folds
2025-08-07 13:21:12 [INFO] Common features: ['100V_V280_H_Y', '200V_V280_H_X', '200V_V280_L_Y', '200V_V700_L_Z', '300V_V280_L_Z', '300V_V700_H_Z', '300V_V700_L_Y', '328A_IERROR_Peak', '420V_V280_H_X', '420V_V280_H_Z']...
2025-08-07 13:21:12 [INFO] Trial completed: CV C-index = 0.5016 (from 10 successful folds)
2025-08-07 13:21:12 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:21:12 [INFO] Hidden dims: [53], activation: tanh, optimizer: Adam
2025-08-07 13:21:13 [INFO] Early stopping triggered. Best score: 0.4810
2025-08-07 13:21:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:13 [INFO] Feature importances range: [-0.0481, 0.0075]
2025-08-07 13:21:13 [INFO] Feature importances mean: -0.0248, median: -0.0250
2025-08-07 13:21:13 [INFO] Fold 0: Selected 2 features with threshold 0.001
2025-08-07 13:21:13 [INFO] Fold 0: Best validation C-index = 0.4937
2025-08-07 13:21:14 [INFO] Early stopping triggered. Best score: 0.5159
2025-08-07 13:21:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:14 [INFO] Feature importances range: [-0.0541, -0.0036]
2025-08-07 13:21:14 [INFO] Feature importances mean: -0.0338, median: -0.0332
2025-08-07 13:21:14 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:14 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:21:15 [INFO] Fold 1: Best validation C-index = 0.4869
2025-08-07 13:21:16 [INFO] Early stopping triggered. Best score: 0.5575
2025-08-07 13:21:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:16 [INFO] Feature importances range: [-0.0496, 0.0045]
2025-08-07 13:21:16 [INFO] Feature importances mean: -0.0267, median: -0.0274
2025-08-07 13:21:16 [INFO] Fold 2: Selected 1 features with threshold 0.001
2025-08-07 13:21:16 [INFO] Fold 2: Best validation C-index = 0.5347
2025-08-07 13:21:18 [INFO] Early stopping triggered. Best score: 0.5179
2025-08-07 13:21:18 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:18 [INFO] Feature importances range: [-0.0736, -0.0153]
2025-08-07 13:21:18 [INFO] Feature importances mean: -0.0436, median: -0.0441
2025-08-07 13:21:18 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:18 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:21:18 [INFO] Fold 3: Best validation C-index = 0.4702
2025-08-07 13:21:19 [INFO] Early stopping triggered. Best score: 0.5286
2025-08-07 13:21:19 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:19 [INFO] Feature importances range: [-0.0550, -0.0024]
2025-08-07 13:21:19 [INFO] Feature importances mean: -0.0288, median: -0.0279
2025-08-07 13:21:19 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:19 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:21:20 [INFO] Fold 4: Best validation C-index = 0.5291
2025-08-07 13:21:21 [INFO] Early stopping triggered. Best score: 0.5255
2025-08-07 13:21:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:21 [INFO] Feature importances range: [-0.0481, 0.0062]
2025-08-07 13:21:21 [INFO] Feature importances mean: -0.0235, median: -0.0239
2025-08-07 13:21:21 [INFO] Fold 5: Selected 3 features with threshold 0.001
2025-08-07 13:21:21 [INFO] Fold 5: Best validation C-index = 0.5116
2025-08-07 13:21:24 [INFO] Early stopping triggered. Best score: 0.5406
2025-08-07 13:21:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:24 [INFO] Feature importances range: [-0.0999, -0.0443]
2025-08-07 13:21:24 [INFO] Feature importances mean: -0.0719, median: -0.0722
2025-08-07 13:21:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:24 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:21:24 [INFO] Fold 6: Best validation C-index = 0.4426
2025-08-07 13:21:26 [INFO] Early stopping triggered. Best score: 0.5197
2025-08-07 13:21:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:26 [INFO] Feature importances range: [-0.0717, -0.0169]
2025-08-07 13:21:26 [INFO] Feature importances mean: -0.0459, median: -0.0460
2025-08-07 13:21:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:26 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:21:27 [INFO] Fold 7: Best validation C-index = 0.4704
2025-08-07 13:21:28 [INFO] Early stopping triggered. Best score: 0.5504
2025-08-07 13:21:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:28 [INFO] Feature importances range: [-0.0548, -0.0029]
2025-08-07 13:21:28 [INFO] Feature importances mean: -0.0277, median: -0.0272
2025-08-07 13:21:28 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:21:28 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:21:28 [INFO] Fold 8: Best validation C-index = 0.5169
2025-08-07 13:21:30 [INFO] Early stopping triggered. Best score: 0.5527
2025-08-07 13:21:30 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:30 [INFO] Feature importances range: [-0.0524, 0.0039]
2025-08-07 13:21:30 [INFO] Feature importances mean: -0.0256, median: -0.0255
2025-08-07 13:21:30 [INFO] Fold 9: Selected 1 features with threshold 0.001
2025-08-07 13:21:30 [INFO] Fold 9: Best validation C-index = 0.5002
2025-08-07 13:21:30 [INFO] Found 10 common features across 10 folds
2025-08-07 13:21:30 [INFO] Common features: ['Z_CLOSED_LOOP_V280_H_X', '420V_V700_H_Y', 'EPI_IERROR_Peak', 'PULSE_700V_X1_HIGH', '420V_V280_L_X', '100V_V280_L_Y', 'PULSE_280V_Y2_LOW', 'EPI_280V_Y1_HIGH', 'PULSE_280V_X2_HIGH', '200V_V700_H_X']...
2025-08-07 13:21:30 [INFO] Trial completed: CV C-index = 0.4956 (from 10 successful folds)
2025-08-07 13:21:30 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:21:30 [INFO] Hidden dims: [57, 51, 64], activation: selu, optimizer: Adagrad
2025-08-07 13:21:31 [INFO] Early stopping triggered. Best score: 0.4686
2025-08-07 13:21:31 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:31 [INFO] Feature importances range: [-0.0305, 0.0224]
2025-08-07 13:21:31 [INFO] Feature importances mean: -0.0032, median: -0.0031
2025-08-07 13:21:31 [INFO] Fold 0: Selected 66 features with threshold 0.001
2025-08-07 13:21:32 [INFO] Fold 0: Best validation C-index = 0.5096
2025-08-07 13:21:33 [INFO] Early stopping triggered. Best score: 0.5005
2025-08-07 13:21:33 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:33 [INFO] Feature importances range: [-0.0298, 0.0241]
2025-08-07 13:21:33 [INFO] Feature importances mean: -0.0036, median: -0.0036
2025-08-07 13:21:33 [INFO] Fold 1: Selected 67 features with threshold 0.001
2025-08-07 13:21:34 [INFO] Fold 1: Best validation C-index = 0.5023
2025-08-07 13:21:40 [INFO] Early stopping triggered. Best score: 0.5180
2025-08-07 13:21:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:40 [INFO] Feature importances range: [-0.0361, 0.0334]
2025-08-07 13:21:40 [INFO] Feature importances mean: -0.0045, median: -0.0051
2025-08-07 13:21:40 [INFO] Fold 2: Selected 53 features with threshold 0.001
2025-08-07 13:21:40 [INFO] Fold 2: Best validation C-index = 0.5050
2025-08-07 13:21:42 [INFO] Early stopping triggered. Best score: 0.4628
2025-08-07 13:21:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:42 [INFO] Feature importances range: [-0.0304, 0.0259]
2025-08-07 13:21:42 [INFO] Feature importances mean: -0.0027, median: -0.0021
2025-08-07 13:21:42 [INFO] Fold 3: Selected 70 features with threshold 0.001
2025-08-07 13:21:43 [INFO] Fold 3: Best validation C-index = 0.4999
2025-08-07 13:21:44 [INFO] Early stopping triggered. Best score: 0.4759
2025-08-07 13:21:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:44 [INFO] Feature importances range: [-0.0338, 0.0231]
2025-08-07 13:21:44 [INFO] Feature importances mean: -0.0034, median: -0.0025
2025-08-07 13:21:44 [INFO] Fold 4: Selected 65 features with threshold 0.001
2025-08-07 13:21:45 [INFO] Fold 4: Best validation C-index = 0.5022
2025-08-07 13:21:48 [INFO] Early stopping triggered. Best score: 0.4839
2025-08-07 13:21:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:48 [INFO] Feature importances range: [-0.0306, 0.0216]
2025-08-07 13:21:48 [INFO] Feature importances mean: -0.0039, median: -0.0036
2025-08-07 13:21:48 [INFO] Fold 5: Selected 59 features with threshold 0.001
2025-08-07 13:21:48 [INFO] Fold 5: Best validation C-index = 0.5090
2025-08-07 13:21:53 [INFO] Early stopping triggered. Best score: 0.5211
2025-08-07 13:21:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:53 [INFO] Feature importances range: [-0.0291, 0.0250]
2025-08-07 13:21:53 [INFO] Feature importances mean: -0.0029, median: -0.0029
2025-08-07 13:21:53 [INFO] Fold 6: Selected 65 features with threshold 0.001
2025-08-07 13:21:54 [INFO] Fold 6: Best validation C-index = 0.5406
2025-08-07 13:21:55 [INFO] Early stopping triggered. Best score: 0.5684
2025-08-07 13:21:55 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:55 [INFO] Feature importances range: [-0.0322, 0.0216]
2025-08-07 13:21:55 [INFO] Feature importances mean: -0.0034, median: -0.0037
2025-08-07 13:21:55 [INFO] Fold 7: Selected 64 features with threshold 0.001
2025-08-07 13:21:56 [INFO] Fold 7: Best validation C-index = 0.4480
2025-08-07 13:21:57 [INFO] Early stopping triggered. Best score: 0.5547
2025-08-07 13:21:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:57 [INFO] Feature importances range: [-0.0342, 0.0254]
2025-08-07 13:21:57 [INFO] Feature importances mean: -0.0031, median: -0.0029
2025-08-07 13:21:57 [INFO] Fold 8: Selected 56 features with threshold 0.001
2025-08-07 13:21:58 [INFO] Fold 8: Best validation C-index = 0.4999
2025-08-07 13:21:59 [INFO] Early stopping triggered. Best score: 0.5451
2025-08-07 13:21:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:21:59 [INFO] Feature importances range: [-0.0330, 0.0208]
2025-08-07 13:21:59 [INFO] Feature importances mean: -0.0026, median: -0.0024
2025-08-07 13:21:59 [INFO] Fold 9: Selected 69 features with threshold 0.001
2025-08-07 13:22:00 [INFO] Fold 9: Best validation C-index = 0.5323
2025-08-07 13:22:00 [INFO] Found 44 common features across 10 folds
2025-08-07 13:22:00 [INFO] Common features: ['100V_V280_L_Y', '200V_V280_L_Y', '300V_V280_H_Z', '300V_V700_L_X', '328A_IERROR_RMS', '420V_V280_H_Y', '420V_V700_L_X', 'BUS_VOLTAGE_VX_LOW', 'EPI_280V_X1_HIGH', 'Echo_Echo_Stab']...
2025-08-07 13:22:00 [INFO] Trial completed: CV C-index = 0.5049 (from 10 successful folds)
2025-08-07 13:22:00 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:22:00 [INFO] Hidden dims: [21, 56, 41, 15], activation: sigmoid, optimizer: Adagrad
2025-08-07 13:22:06 [INFO] Early stopping triggered. Best score: 0.5569
2025-08-07 13:22:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:06 [INFO] Feature importances range: [-0.2915, -0.2429]
2025-08-07 13:22:06 [INFO] Feature importances mean: -0.2667, median: -0.2658
2025-08-07 13:22:06 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:06 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:22:06 [INFO] Fold 0: Best validation C-index = 0.4902
2025-08-07 13:22:08 [INFO] Early stopping triggered. Best score: 0.5179
2025-08-07 13:22:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:08 [INFO] Feature importances range: [-0.1434, -0.0976]
2025-08-07 13:22:08 [INFO] Feature importances mean: -0.1211, median: -0.1215
2025-08-07 13:22:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:08 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:22:08 [INFO] Fold 1: Best validation C-index = 0.4947
2025-08-07 13:22:10 [INFO] Early stopping triggered. Best score: 0.5123
2025-08-07 13:22:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:10 [INFO] Feature importances range: [-0.1449, -0.0917]
2025-08-07 13:22:10 [INFO] Feature importances mean: -0.1210, median: -0.1206
2025-08-07 13:22:10 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:10 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:22:10 [INFO] Fold 2: Best validation C-index = 0.4800
2025-08-07 13:22:16 [INFO] Early stopping triggered. Best score: 0.5533
2025-08-07 13:22:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:16 [INFO] Feature importances range: [-0.3068, -0.2548]
2025-08-07 13:22:16 [INFO] Feature importances mean: -0.2802, median: -0.2798
2025-08-07 13:22:16 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:16 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:22:17 [INFO] Fold 3: Best validation C-index = 0.5164
2025-08-07 13:22:23 [INFO] Early stopping triggered. Best score: 0.5722
2025-08-07 13:22:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:23 [INFO] Feature importances range: [-0.3211, -0.2639]
2025-08-07 13:22:23 [INFO] Feature importances mean: -0.2916, median: -0.2925
2025-08-07 13:22:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:23 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:22:23 [INFO] Fold 4: Best validation C-index = 0.5524
2025-08-07 13:22:25 [INFO] Early stopping triggered. Best score: 0.5112
2025-08-07 13:22:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:25 [INFO] Feature importances range: [-0.1455, -0.1003]
2025-08-07 13:22:25 [INFO] Feature importances mean: -0.1196, median: -0.1192
2025-08-07 13:22:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:25 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:22:25 [INFO] Fold 5: Best validation C-index = 0.4314
2025-08-07 13:22:27 [INFO] Early stopping triggered. Best score: 0.4898
2025-08-07 13:22:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:27 [INFO] Feature importances range: [-0.1468, -0.0897]
2025-08-07 13:22:27 [INFO] Feature importances mean: -0.1196, median: -0.1207
2025-08-07 13:22:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:27 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:22:27 [INFO] Fold 6: Best validation C-index = 0.4709
2025-08-07 13:22:29 [INFO] Early stopping triggered. Best score: 0.5558
2025-08-07 13:22:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:29 [INFO] Feature importances range: [-0.1410, -0.0932]
2025-08-07 13:22:29 [INFO] Feature importances mean: -0.1190, median: -0.1193
2025-08-07 13:22:29 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:29 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:22:29 [INFO] Fold 7: Best validation C-index = 0.4909
2025-08-07 13:22:31 [INFO] Early stopping triggered. Best score: 0.4871
2025-08-07 13:22:31 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:31 [INFO] Feature importances range: [-0.1480, -0.0892]
2025-08-07 13:22:31 [INFO] Feature importances mean: -0.1215, median: -0.1214
2025-08-07 13:22:31 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:31 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:22:31 [INFO] Fold 8: Best validation C-index = 0.5565
2025-08-07 13:22:37 [INFO] Early stopping triggered. Best score: 0.5411
2025-08-07 13:22:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:37 [INFO] Feature importances range: [-0.2792, -0.2190]
2025-08-07 13:22:37 [INFO] Feature importances mean: -0.2497, median: -0.2498
2025-08-07 13:22:37 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:37 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:22:37 [INFO] Fold 9: Best validation C-index = 0.5470
2025-08-07 13:22:37 [INFO] Found 10 common features across 10 folds
2025-08-07 13:22:37 [INFO] Common features: ['300V_V280_L_Z', '100V_V280_H_Z', '200V_V280_H_Y', '300V_V700_H_Y', 'OUTPUT_VOLTAGE_DISCH_V700_H_Z', 'Y_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_H_X', '200V_V280_L_X', '100A_IERROR_RMS', 'X_CLOSED_LOOP_V700_H_Z']...
2025-08-07 13:22:37 [INFO] Trial completed: CV C-index = 0.5030 (from 10 successful folds)
2025-08-07 13:22:37 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:22:37 [INFO] Hidden dims: [64, 59], activation: selu, optimizer: Adam
2025-08-07 13:22:39 [INFO] Early stopping triggered. Best score: 0.5413
2025-08-07 13:22:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:39 [INFO] Feature importances range: [-0.2157, -0.1685]
2025-08-07 13:22:39 [INFO] Feature importances mean: -0.1950, median: -0.1951
2025-08-07 13:22:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:39 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:22:39 [INFO] Fold 0: Best validation C-index = 0.5053
2025-08-07 13:22:42 [INFO] Early stopping triggered. Best score: 0.5452
2025-08-07 13:22:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:42 [INFO] Feature importances range: [-0.5834, -0.5508]
2025-08-07 13:22:42 [INFO] Feature importances mean: -0.5664, median: -0.5668
2025-08-07 13:22:42 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:42 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:22:43 [INFO] Fold 1: Best validation C-index = 0.5195
2025-08-07 13:22:45 [INFO] Early stopping triggered. Best score: 0.5389
2025-08-07 13:22:45 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:45 [INFO] Feature importances range: [-0.3269, -0.2800]
2025-08-07 13:22:45 [INFO] Feature importances mean: -0.3035, median: -0.3040
2025-08-07 13:22:45 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:45 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:22:45 [INFO] Fold 2: Best validation C-index = 0.5370
2025-08-07 13:22:47 [INFO] Early stopping triggered. Best score: 0.5415
2025-08-07 13:22:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:47 [INFO] Feature importances range: [-0.2096, -0.1532]
2025-08-07 13:22:47 [INFO] Feature importances mean: -0.1841, median: -0.1843
2025-08-07 13:22:47 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:47 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:22:47 [INFO] Fold 3: Best validation C-index = 0.5288
2025-08-07 13:22:48 [INFO] Early stopping triggered. Best score: 0.5315
2025-08-07 13:22:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:48 [INFO] Feature importances range: [-0.2172, -0.1642]
2025-08-07 13:22:48 [INFO] Feature importances mean: -0.1887, median: -0.1893
2025-08-07 13:22:48 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:48 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:22:49 [INFO] Fold 4: Best validation C-index = 0.5008
2025-08-07 13:22:51 [INFO] Early stopping triggered. Best score: 0.5561
2025-08-07 13:22:51 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:51 [INFO] Feature importances range: [-0.4194, -0.3696]
2025-08-07 13:22:51 [INFO] Feature importances mean: -0.3947, median: -0.3948
2025-08-07 13:22:51 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:51 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:22:52 [INFO] Fold 5: Best validation C-index = 0.5264
2025-08-07 13:22:57 [INFO] Early stopping triggered. Best score: 0.5666
2025-08-07 13:22:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:22:57 [INFO] Feature importances range: [-0.5842, -0.5464]
2025-08-07 13:22:57 [INFO] Feature importances mean: -0.5663, median: -0.5662
2025-08-07 13:22:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:22:57 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:22:58 [INFO] Fold 6: Best validation C-index = 0.5052
2025-08-07 13:23:00 [INFO] Early stopping triggered. Best score: 0.5361
2025-08-07 13:23:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:00 [INFO] Feature importances range: [-0.1976, -0.1457]
2025-08-07 13:23:00 [INFO] Feature importances mean: -0.1782, median: -0.1776
2025-08-07 13:23:00 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:23:00 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:23:00 [INFO] Fold 7: Best validation C-index = 0.4949
2025-08-07 13:23:04 [INFO] Early stopping triggered. Best score: 0.5289
2025-08-07 13:23:04 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:04 [INFO] Feature importances range: [-0.4512, -0.4125]
2025-08-07 13:23:04 [INFO] Feature importances mean: -0.4319, median: -0.4322
2025-08-07 13:23:04 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:23:04 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:23:04 [INFO] Fold 8: Best validation C-index = 0.5102
2025-08-07 13:23:08 [INFO] Early stopping triggered. Best score: 0.5182
2025-08-07 13:23:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:08 [INFO] Feature importances range: [-0.3124, -0.2592]
2025-08-07 13:23:08 [INFO] Feature importances mean: -0.2856, median: -0.2846
2025-08-07 13:23:08 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:23:08 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:23:08 [INFO] Fold 9: Best validation C-index = 0.5043
2025-08-07 13:23:08 [INFO] Found 10 common features across 10 folds
2025-08-07 13:23:08 [INFO] Common features: ['PULSE_700V_Y1_LOW', 'PULSE_700V_X1_LOW', '420V_V700_H_Z', 'Z_CLOSED_LOOP_V280_L_Z', 'X_CLOSED_LOOP_V280_H_Y', 'Y_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V280_H_Z', '100A_IERROR_Peak', 'OUTPUT_VOLTAGE_DISCH_V700_L_Z', '420V_V700_L_Y']...
2025-08-07 13:23:08 [INFO] Trial completed: CV C-index = 0.5132 (from 10 successful folds)
2025-08-07 13:23:08 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:23:08 [INFO] Hidden dims: [54, 56, 26], activation: relu, optimizer: Adagrad
2025-08-07 13:23:14 [INFO] Early stopping triggered. Best score: 0.5360
2025-08-07 13:23:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:14 [INFO] Feature importances range: [-0.0421, 0.0688]
2025-08-07 13:23:14 [INFO] Feature importances mean: 0.0177, median: 0.0180
2025-08-07 13:23:14 [INFO] Fold 0: Selected 134 features with threshold 0.001
2025-08-07 13:23:16 [INFO] Fold 0: Best validation C-index = 0.5424
2025-08-07 13:23:19 [INFO] Early stopping triggered. Best score: 0.5588
2025-08-07 13:23:19 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:19 [INFO] Feature importances range: [-0.0301, 0.0510]
2025-08-07 13:23:19 [INFO] Feature importances mean: 0.0142, median: 0.0146
2025-08-07 13:23:19 [INFO] Fold 1: Selected 133 features with threshold 0.001
2025-08-07 13:23:21 [INFO] Fold 1: Best validation C-index = 0.5355
2025-08-07 13:23:24 [INFO] Early stopping triggered. Best score: 0.5486
2025-08-07 13:23:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:24 [INFO] Feature importances range: [-0.0334, 0.0559]
2025-08-07 13:23:24 [INFO] Feature importances mean: 0.0105, median: 0.0115
2025-08-07 13:23:24 [INFO] Fold 2: Selected 128 features with threshold 0.001
2025-08-07 13:23:26 [INFO] Fold 2: Best validation C-index = 0.5243
2025-08-07 13:23:28 [INFO] Early stopping triggered. Best score: 0.5264
2025-08-07 13:23:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:28 [INFO] Feature importances range: [-0.0405, 0.0515]
2025-08-07 13:23:28 [INFO] Feature importances mean: 0.0086, median: 0.0103
2025-08-07 13:23:28 [INFO] Fold 3: Selected 123 features with threshold 0.001
2025-08-07 13:23:30 [INFO] Fold 3: Best validation C-index = 0.5393
2025-08-07 13:23:32 [INFO] Early stopping triggered. Best score: 0.5362
2025-08-07 13:23:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:32 [INFO] Feature importances range: [-0.0453, 0.0501]
2025-08-07 13:23:32 [INFO] Feature importances mean: 0.0104, median: 0.0101
2025-08-07 13:23:32 [INFO] Fold 4: Selected 122 features with threshold 0.001
2025-08-07 13:23:33 [INFO] Fold 4: Best validation C-index = 0.5260
2025-08-07 13:23:37 [INFO] Early stopping triggered. Best score: 0.5815
2025-08-07 13:23:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:37 [INFO] Feature importances range: [-0.0421, 0.0587]
2025-08-07 13:23:37 [INFO] Feature importances mean: 0.0118, median: 0.0100
2025-08-07 13:23:37 [INFO] Fold 5: Selected 128 features with threshold 0.001
2025-08-07 13:23:38 [INFO] Fold 5: Best validation C-index = 0.5361
2025-08-07 13:23:41 [INFO] Early stopping triggered. Best score: 0.4581
2025-08-07 13:23:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:41 [INFO] Feature importances range: [-0.0461, 0.0571]
2025-08-07 13:23:41 [INFO] Feature importances mean: 0.0096, median: 0.0100
2025-08-07 13:23:41 [INFO] Fold 6: Selected 121 features with threshold 0.001
2025-08-07 13:23:42 [INFO] Fold 6: Best validation C-index = 0.4680
2025-08-07 13:23:46 [INFO] Early stopping triggered. Best score: 0.5169
2025-08-07 13:23:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:46 [INFO] Feature importances range: [-0.0484, 0.0652]
2025-08-07 13:23:46 [INFO] Feature importances mean: 0.0153, median: 0.0152
2025-08-07 13:23:46 [INFO] Fold 7: Selected 133 features with threshold 0.001
2025-08-07 13:23:48 [INFO] Fold 7: Best validation C-index = 0.5124
2025-08-07 13:23:50 [INFO] Early stopping triggered. Best score: 0.4727
2025-08-07 13:23:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:50 [INFO] Feature importances range: [-0.0390, 0.0441]
2025-08-07 13:23:50 [INFO] Feature importances mean: 0.0080, median: 0.0092
2025-08-07 13:23:50 [INFO] Fold 8: Selected 123 features with threshold 0.001
2025-08-07 13:23:52 [INFO] Fold 8: Best validation C-index = 0.5212
2025-08-07 13:23:56 [INFO] Early stopping triggered. Best score: 0.5105
2025-08-07 13:23:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:23:56 [INFO] Feature importances range: [-0.0439, 0.0654]
2025-08-07 13:23:56 [INFO] Feature importances mean: 0.0146, median: 0.0154
2025-08-07 13:23:56 [INFO] Fold 9: Selected 138 features with threshold 0.001
2025-08-07 13:23:58 [INFO] Fold 9: Best validation C-index = 0.4900
2025-08-07 13:23:58 [INFO] Found 171 common features across 10 folds
2025-08-07 13:23:58 [INFO] Common features: ['100A_Flattop_Negative', '100A_Flattop_Positive', '100A_IERROR_Peak', '100A_IERROR_RMS', '100V_V280_H_X', '100V_V280_H_Y', '100V_V280_L_X', '100V_V280_L_Y', '100V_V280_L_Z', '100V_V700_H_Z']...
2025-08-07 13:23:58 [INFO] Trial completed: CV C-index = 0.5195 (from 10 successful folds)
2025-08-07 13:23:58 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:23:58 [INFO] Hidden dims: [51, 61, 20], activation: relu, optimizer: Adagrad
2025-08-07 13:24:00 [INFO] Early stopping triggered. Best score: 0.5336
2025-08-07 13:24:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:00 [INFO] Feature importances range: [-0.0513, 0.0514]
2025-08-07 13:24:00 [INFO] Feature importances mean: 0.0004, median: -0.0021
2025-08-07 13:24:00 [INFO] Fold 0: Selected 87 features with threshold 0.001
2025-08-07 13:24:01 [INFO] Fold 0: Best validation C-index = 0.5163
2025-08-07 13:24:09 [INFO] Early stopping triggered. Best score: 0.5135
2025-08-07 13:24:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:09 [INFO] Feature importances range: [-0.0903, 0.0899]
2025-08-07 13:24:09 [INFO] Feature importances mean: 0.0122, median: 0.0121
2025-08-07 13:24:09 [INFO] Fold 1: Selected 114 features with threshold 0.001
2025-08-07 13:24:10 [INFO] Fold 1: Best validation C-index = 0.4813
2025-08-07 13:24:13 [INFO] Early stopping triggered. Best score: 0.5413
2025-08-07 13:24:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:13 [INFO] Feature importances range: [-0.0536, 0.0566]
2025-08-07 13:24:13 [INFO] Feature importances mean: 0.0003, median: 0.0014
2025-08-07 13:24:13 [INFO] Fold 2: Selected 90 features with threshold 0.001
2025-08-07 13:24:14 [INFO] Fold 2: Best validation C-index = 0.5059
2025-08-07 13:24:17 [INFO] Early stopping triggered. Best score: 0.5303
2025-08-07 13:24:17 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:17 [INFO] Feature importances range: [-0.0658, 0.0597]
2025-08-07 13:24:17 [INFO] Feature importances mean: -0.0026, median: 0.0001
2025-08-07 13:24:17 [INFO] Fold 3: Selected 87 features with threshold 0.001
2025-08-07 13:24:18 [INFO] Fold 3: Best validation C-index = 0.5454
2025-08-07 13:24:21 [INFO] Early stopping triggered. Best score: 0.5187
2025-08-07 13:24:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:21 [INFO] Feature importances range: [-0.0633, 0.0468]
2025-08-07 13:24:21 [INFO] Feature importances mean: -0.0020, median: -0.0032
2025-08-07 13:24:21 [INFO] Fold 4: Selected 80 features with threshold 0.001
2025-08-07 13:24:22 [INFO] Fold 4: Best validation C-index = 0.5093
2025-08-07 13:24:25 [INFO] Early stopping triggered. Best score: 0.5424
2025-08-07 13:24:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:25 [INFO] Feature importances range: [-0.0583, 0.0603]
2025-08-07 13:24:25 [INFO] Feature importances mean: 0.0027, median: 0.0039
2025-08-07 13:24:25 [INFO] Fold 5: Selected 99 features with threshold 0.001
2025-08-07 13:24:26 [INFO] Fold 5: Best validation C-index = 0.5129
2025-08-07 13:24:31 [INFO] Early stopping triggered. Best score: 0.5304
2025-08-07 13:24:31 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:31 [INFO] Feature importances range: [-0.0650, 0.0732]
2025-08-07 13:24:31 [INFO] Feature importances mean: 0.0025, median: 0.0039
2025-08-07 13:24:31 [INFO] Fold 6: Selected 97 features with threshold 0.001
2025-08-07 13:24:32 [INFO] Fold 6: Best validation C-index = 0.5095
2025-08-07 13:24:36 [INFO] Early stopping triggered. Best score: 0.5338
2025-08-07 13:24:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:36 [INFO] Feature importances range: [-0.0549, 0.0620]
2025-08-07 13:24:36 [INFO] Feature importances mean: 0.0026, median: 0.0030
2025-08-07 13:24:36 [INFO] Fold 7: Selected 96 features with threshold 0.001
2025-08-07 13:24:37 [INFO] Fold 7: Best validation C-index = 0.5174
2025-08-07 13:24:40 [INFO] Early stopping triggered. Best score: 0.5430
2025-08-07 13:24:40 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:40 [INFO] Feature importances range: [-0.0494, 0.0635]
2025-08-07 13:24:40 [INFO] Feature importances mean: -0.0021, median: -0.0045
2025-08-07 13:24:40 [INFO] Fold 8: Selected 82 features with threshold 0.001
2025-08-07 13:24:41 [INFO] Fold 8: Best validation C-index = 0.5323
2025-08-07 13:24:44 [INFO] Early stopping triggered. Best score: 0.5660
2025-08-07 13:24:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:44 [INFO] Feature importances range: [-0.0635, 0.0632]
2025-08-07 13:24:44 [INFO] Feature importances mean: -0.0004, median: -0.0026
2025-08-07 13:24:44 [INFO] Fold 9: Selected 82 features with threshold 0.001
2025-08-07 13:24:45 [INFO] Fold 9: Best validation C-index = 0.5413
2025-08-07 13:24:45 [INFO] Found 104 common features across 10 folds
2025-08-07 13:24:45 [INFO] Common features: ['100A_IERROR_RMS', '100V_V280_L_Z', '100V_V700_L_Y', '200V_V280_L_Z', '300V_V280_L_X', '300V_V280_L_Z', '328A_Flattop_Negative', '420V_V700_H_Z', '420V_V700_L_Y', 'Absolute_Error']...
2025-08-07 13:24:45 [INFO] Trial completed: CV C-index = 0.5171 (from 10 successful folds)
2025-08-07 13:24:45 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:24:45 [INFO] Hidden dims: [50, 62, 18], activation: relu, optimizer: Adagrad
2025-08-07 13:24:47 [INFO] Early stopping triggered. Best score: 0.4994
2025-08-07 13:24:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:47 [INFO] Feature importances range: [-0.0294, 0.0190]
2025-08-07 13:24:47 [INFO] Feature importances mean: -0.0062, median: -0.0069
2025-08-07 13:24:47 [INFO] Fold 0: Selected 41 features with threshold 0.001
2025-08-07 13:24:48 [INFO] Fold 0: Best validation C-index = 0.5115
2025-08-07 13:24:51 [INFO] Early stopping triggered. Best score: 0.5638
2025-08-07 13:24:51 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:51 [INFO] Feature importances range: [-0.0339, 0.0228]
2025-08-07 13:24:51 [INFO] Feature importances mean: -0.0057, median: -0.0069
2025-08-07 13:24:51 [INFO] Fold 1: Selected 45 features with threshold 0.001
2025-08-07 13:24:52 [INFO] Fold 1: Best validation C-index = 0.4847
2025-08-07 13:24:59 [INFO] Early stopping triggered. Best score: 0.5362
2025-08-07 13:24:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:24:59 [INFO] Feature importances range: [-0.0317, 0.0294]
2025-08-07 13:25:00 [INFO] Feature importances mean: -0.0084, median: -0.0086
2025-08-07 13:25:00 [INFO] Fold 2: Selected 39 features with threshold 0.001
2025-08-07 13:25:00 [INFO] Fold 2: Best validation C-index = 0.5268
2025-08-07 13:25:03 [INFO] Early stopping triggered. Best score: 0.5529
2025-08-07 13:25:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:03 [INFO] Feature importances range: [-0.0373, 0.0351]
2025-08-07 13:25:03 [INFO] Feature importances mean: -0.0061, median: -0.0066
2025-08-07 13:25:03 [INFO] Fold 3: Selected 46 features with threshold 0.001
2025-08-07 13:25:04 [INFO] Fold 3: Best validation C-index = 0.4960
2025-08-07 13:25:12 [INFO] Early stopping triggered. Best score: 0.5341
2025-08-07 13:25:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:12 [INFO] Feature importances range: [-0.0335, 0.0226]
2025-08-07 13:25:12 [INFO] Feature importances mean: -0.0092, median: -0.0094
2025-08-07 13:25:12 [INFO] Fold 4: Selected 31 features with threshold 0.001
2025-08-07 13:25:13 [INFO] Fold 4: Best validation C-index = 0.5126
2025-08-07 13:25:15 [INFO] Early stopping triggered. Best score: 0.4648
2025-08-07 13:25:15 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:15 [INFO] Feature importances range: [-0.0274, 0.0193]
2025-08-07 13:25:15 [INFO] Feature importances mean: -0.0046, median: -0.0049
2025-08-07 13:25:15 [INFO] Fold 5: Selected 57 features with threshold 0.001
2025-08-07 13:25:16 [INFO] Fold 5: Best validation C-index = 0.5322
2025-08-07 13:25:20 [INFO] Early stopping triggered. Best score: 0.5131
2025-08-07 13:25:20 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:20 [INFO] Feature importances range: [-0.0388, 0.0261]
2025-08-07 13:25:20 [INFO] Feature importances mean: -0.0065, median: -0.0077
2025-08-07 13:25:20 [INFO] Fold 6: Selected 45 features with threshold 0.001
2025-08-07 13:25:21 [INFO] Fold 6: Best validation C-index = 0.5011
2025-08-07 13:25:29 [INFO] Early stopping triggered. Best score: 0.4641
2025-08-07 13:25:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:29 [INFO] Feature importances range: [-0.0418, 0.0285]
2025-08-07 13:25:29 [INFO] Feature importances mean: -0.0089, median: -0.0082
2025-08-07 13:25:29 [INFO] Fold 7: Selected 33 features with threshold 0.001
2025-08-07 13:25:30 [INFO] Fold 7: Best validation C-index = 0.4908
2025-08-07 13:25:32 [INFO] Early stopping triggered. Best score: 0.5445
2025-08-07 13:25:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:32 [INFO] Feature importances range: [-0.0295, 0.0294]
2025-08-07 13:25:32 [INFO] Feature importances mean: -0.0044, median: -0.0044
2025-08-07 13:25:32 [INFO] Fold 8: Selected 53 features with threshold 0.001
2025-08-07 13:25:32 [INFO] Fold 8: Best validation C-index = 0.4613
2025-08-07 13:25:38 [INFO] Early stopping triggered. Best score: 0.5201
2025-08-07 13:25:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:38 [INFO] Feature importances range: [-0.0326, 0.0266]
2025-08-07 13:25:38 [INFO] Feature importances mean: -0.0079, median: -0.0074
2025-08-07 13:25:38 [INFO] Fold 9: Selected 31 features with threshold 0.001
2025-08-07 13:25:39 [INFO] Fold 9: Best validation C-index = 0.4888
2025-08-07 13:25:39 [INFO] Found 14 common features across 10 folds
2025-08-07 13:25:39 [INFO] Common features: ['100V_V700_L_X', 'Absolute_Error', 'EPI_280V_Y1_HIGH', 'EPI_700V_X2_HIGH', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_280V_Z1_LOW', 'PULSE_700V_Z1_HIGH', 'EPI_IERROR_RMS', 'Heat_Sink_Temp_Delta_T_Amplifier_Lavaflex', 'X_CLOSED_LOOP_V280_H_X']...
2025-08-07 13:25:39 [INFO] Trial completed: CV C-index = 0.5006 (from 10 successful folds)
2025-08-07 13:25:39 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:25:39 [INFO] Hidden dims: [43, 60, 18], activation: relu, optimizer: Adagrad
2025-08-07 13:25:45 [INFO] Early stopping triggered. Best score: 0.5408
2025-08-07 13:25:45 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:45 [INFO] Feature importances range: [-0.0497, 0.0101]
2025-08-07 13:25:45 [INFO] Feature importances mean: -0.0168, median: -0.0175
2025-08-07 13:25:45 [INFO] Fold 0: Selected 4 features with threshold 0.001
2025-08-07 13:25:46 [INFO] Fold 0: Best validation C-index = 0.5644
2025-08-07 13:25:48 [INFO] Early stopping triggered. Best score: 0.5165
2025-08-07 13:25:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:48 [INFO] Feature importances range: [-0.0364, 0.0165]
2025-08-07 13:25:48 [INFO] Feature importances mean: -0.0102, median: -0.0096
2025-08-07 13:25:48 [INFO] Fold 1: Selected 15 features with threshold 0.001
2025-08-07 13:25:49 [INFO] Fold 1: Best validation C-index = 0.5256
2025-08-07 13:25:54 [INFO] Early stopping triggered. Best score: 0.5384
2025-08-07 13:25:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:54 [INFO] Feature importances range: [-0.0374, 0.0075]
2025-08-07 13:25:54 [INFO] Feature importances mean: -0.0142, median: -0.0147
2025-08-07 13:25:54 [INFO] Fold 2: Selected 11 features with threshold 0.001
2025-08-07 13:25:54 [INFO] Fold 2: Best validation C-index = 0.4745
2025-08-07 13:25:59 [INFO] Early stopping triggered. Best score: 0.4863
2025-08-07 13:25:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:25:59 [INFO] Feature importances range: [-0.0453, 0.0097]
2025-08-07 13:25:59 [INFO] Feature importances mean: -0.0150, median: -0.0148
2025-08-07 13:25:59 [INFO] Fold 3: Selected 11 features with threshold 0.001
2025-08-07 13:26:00 [INFO] Fold 3: Best validation C-index = 0.5177
2025-08-07 13:26:02 [INFO] Early stopping triggered. Best score: 0.5026
2025-08-07 13:26:02 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:02 [INFO] Feature importances range: [-0.0356, 0.0079]
2025-08-07 13:26:02 [INFO] Feature importances mean: -0.0120, median: -0.0121
2025-08-07 13:26:02 [INFO] Fold 4: Selected 18 features with threshold 0.001
2025-08-07 13:26:03 [INFO] Fold 4: Best validation C-index = 0.5107
2025-08-07 13:26:07 [INFO] Early stopping triggered. Best score: 0.5380
2025-08-07 13:26:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:07 [INFO] Feature importances range: [-0.0438, 0.0224]
2025-08-07 13:26:07 [INFO] Feature importances mean: -0.0140, median: -0.0136
2025-08-07 13:26:07 [INFO] Fold 5: Selected 11 features with threshold 0.001
2025-08-07 13:26:08 [INFO] Fold 5: Best validation C-index = 0.5075
2025-08-07 13:26:10 [INFO] Early stopping triggered. Best score: 0.5366
2025-08-07 13:26:10 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:10 [INFO] Feature importances range: [-0.0311, 0.0145]
2025-08-07 13:26:10 [INFO] Feature importances mean: -0.0102, median: -0.0100
2025-08-07 13:26:10 [INFO] Fold 6: Selected 16 features with threshold 0.001
2025-08-07 13:26:11 [INFO] Fold 6: Best validation C-index = 0.5102
2025-08-07 13:26:16 [INFO] Early stopping triggered. Best score: 0.4813
2025-08-07 13:26:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:16 [INFO] Feature importances range: [-0.0411, 0.0049]
2025-08-07 13:26:16 [INFO] Feature importances mean: -0.0178, median: -0.0176
2025-08-07 13:26:16 [INFO] Fold 7: Selected 3 features with threshold 0.001
2025-08-07 13:26:16 [INFO] Fold 7: Best validation C-index = 0.4910
2025-08-07 13:26:20 [INFO] Early stopping triggered. Best score: 0.4794
2025-08-07 13:26:20 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:20 [INFO] Feature importances range: [-0.0385, 0.0102]
2025-08-07 13:26:20 [INFO] Feature importances mean: -0.0131, median: -0.0131
2025-08-07 13:26:20 [INFO] Fold 8: Selected 12 features with threshold 0.001
2025-08-07 13:26:20 [INFO] Fold 8: Best validation C-index = 0.4963
2025-08-07 13:26:23 [INFO] Early stopping triggered. Best score: 0.5595
2025-08-07 13:26:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:23 [INFO] Feature importances range: [-0.0413, 0.0176]
2025-08-07 13:26:23 [INFO] Feature importances mean: -0.0122, median: -0.0128
2025-08-07 13:26:23 [INFO] Fold 9: Selected 15 features with threshold 0.001
2025-08-07 13:26:24 [INFO] Fold 9: Best validation C-index = 0.4901
2025-08-07 13:26:24 [INFO] Found 10 common features across 10 folds
2025-08-07 13:26:24 [INFO] Common features: ['Heat_Sink_Temp_Amplifier_Initial', 'Heat_Sink_Temp_Delta_T_Power_Supply_Lavaflex', 'X_CLOSED_LOOP_V700_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', '100V_V280_H_Y', 'EPI_280V_Z1_HIGH', 'EPI_280V_Z1_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_280V_X2_LOW', 'PULSE_700V_X1_HIGH']...
2025-08-07 13:26:24 [INFO] Trial completed: CV C-index = 0.5088 (from 10 successful folds)
2025-08-07 13:26:24 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:26:24 [INFO] Hidden dims: [46, 36, 29, 45], activation: relu, optimizer: Adagrad
2025-08-07 13:26:28 [INFO] Early stopping triggered. Best score: 0.5369
2025-08-07 13:26:28 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:28 [INFO] Feature importances range: [-0.5854, 0.3170]
2025-08-07 13:26:28 [INFO] Feature importances mean: -0.0437, median: -0.0013
2025-08-07 13:26:28 [INFO] Fold 0: Selected 88 features with threshold 0.001
2025-08-07 13:26:29 [INFO] Fold 0: Best validation C-index = 0.5200
2025-08-07 13:26:32 [INFO] Early stopping triggered. Best score: 0.5589
2025-08-07 13:26:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:32 [INFO] Feature importances range: [-0.5474, 0.3119]
2025-08-07 13:26:32 [INFO] Feature importances mean: -0.0432, median: -0.0007
2025-08-07 13:26:32 [INFO] Fold 1: Selected 89 features with threshold 0.001
2025-08-07 13:26:33 [INFO] Fold 1: Best validation C-index = 0.5172
2025-08-07 13:26:37 [INFO] Early stopping triggered. Best score: 0.5416
2025-08-07 13:26:37 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:37 [INFO] Feature importances range: [-0.5853, 0.3457]
2025-08-07 13:26:37 [INFO] Feature importances mean: -0.0993, median: -0.0266
2025-08-07 13:26:37 [INFO] Fold 2: Selected 75 features with threshold 0.001
2025-08-07 13:26:38 [INFO] Fold 2: Best validation C-index = 0.5479
2025-08-07 13:26:41 [INFO] Early stopping triggered. Best score: 0.5292
2025-08-07 13:26:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:41 [INFO] Feature importances range: [-0.5646, 0.3330]
2025-08-07 13:26:41 [INFO] Feature importances mean: -0.0510, median: -0.0134
2025-08-07 13:26:41 [INFO] Fold 3: Selected 77 features with threshold 0.001
2025-08-07 13:26:42 [INFO] Fold 3: Best validation C-index = 0.4845
2025-08-07 13:26:45 [INFO] Early stopping triggered. Best score: 0.5466
2025-08-07 13:26:45 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:45 [INFO] Feature importances range: [-0.5829, 0.3570]
2025-08-07 13:26:45 [INFO] Feature importances mean: -0.1171, median: -0.0327
2025-08-07 13:26:45 [INFO] Fold 4: Selected 75 features with threshold 0.001
2025-08-07 13:26:46 [INFO] Fold 4: Best validation C-index = 0.5414
2025-08-07 13:26:49 [INFO] Early stopping triggered. Best score: 0.5359
2025-08-07 13:26:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:49 [INFO] Feature importances range: [-0.5586, 0.3287]
2025-08-07 13:26:49 [INFO] Feature importances mean: -0.0154, median: 0.0007
2025-08-07 13:26:49 [INFO] Fold 5: Selected 89 features with threshold 0.001
2025-08-07 13:26:50 [INFO] Fold 5: Best validation C-index = 0.4740
2025-08-07 13:26:54 [INFO] Early stopping triggered. Best score: 0.5293
2025-08-07 13:26:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:54 [INFO] Feature importances range: [-0.5753, 0.2909]
2025-08-07 13:26:54 [INFO] Feature importances mean: -0.0556, median: -0.0161
2025-08-07 13:26:54 [INFO] Fold 6: Selected 80 features with threshold 0.001
2025-08-07 13:26:55 [INFO] Fold 6: Best validation C-index = 0.5103
2025-08-07 13:26:58 [INFO] Early stopping triggered. Best score: 0.5604
2025-08-07 13:26:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:26:58 [INFO] Feature importances range: [-0.5394, 0.2927]
2025-08-07 13:26:58 [INFO] Feature importances mean: -0.0634, median: -0.0134
2025-08-07 13:26:58 [INFO] Fold 7: Selected 77 features with threshold 0.001
2025-08-07 13:26:59 [INFO] Fold 7: Best validation C-index = 0.5346
2025-08-07 13:27:03 [INFO] Early stopping triggered. Best score: 0.5464
2025-08-07 13:27:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:03 [INFO] Feature importances range: [-0.6115, 0.3592]
2025-08-07 13:27:03 [INFO] Feature importances mean: -0.0721, median: -0.0145
2025-08-07 13:27:03 [INFO] Fold 8: Selected 84 features with threshold 0.001
2025-08-07 13:27:04 [INFO] Fold 8: Best validation C-index = 0.4858
2025-08-07 13:27:08 [INFO] Early stopping triggered. Best score: 0.5190
2025-08-07 13:27:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:08 [INFO] Feature importances range: [-0.5637, 0.3495]
2025-08-07 13:27:08 [INFO] Feature importances mean: -0.0470, median: 0.0041
2025-08-07 13:27:08 [INFO] Fold 9: Selected 91 features with threshold 0.001
2025-08-07 13:27:09 [INFO] Fold 9: Best validation C-index = 0.5305
2025-08-07 13:27:09 [INFO] Found 86 common features across 10 folds
2025-08-07 13:27:09 [INFO] Common features: ['100A_Flattop_Negative', '100A_Flattop_Positive', '100A_IERROR_Peak', '100A_IERROR_RMS', '328A_Flattop_Negative', '328A_Flattop_Positive', 'Absolute_Error', 'BUS_VOLTAGE_VBUS', 'BUS_VOLTAGE_VX_LOW', 'BUS_VOLTAGE_VY_LOW']...
2025-08-07 13:27:09 [INFO] Trial completed: CV C-index = 0.5146 (from 10 successful folds)
2025-08-07 13:27:09 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:27:09 [INFO] Hidden dims: [51, 59, 17], activation: relu, optimizer: SGD
2025-08-07 13:27:12 [INFO] Early stopping triggered. Best score: 0.5733
2025-08-07 13:27:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:12 [INFO] Feature importances range: [-0.0269, 0.0219]
2025-08-07 13:27:12 [INFO] Feature importances mean: -0.0006, median: -0.0010
2025-08-07 13:27:12 [INFO] Fold 0: Selected 79 features with threshold 0.001
2025-08-07 13:27:13 [INFO] Fold 0: Best validation C-index = 0.4877
2025-08-07 13:27:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:36 [INFO] Feature importances range: [-0.0323, 0.0278]
2025-08-07 13:27:36 [INFO] Feature importances mean: -0.0038, median: -0.0039
2025-08-07 13:27:36 [INFO] Fold 1: Selected 57 features with threshold 0.001
2025-08-07 13:27:37 [INFO] Fold 1: Best validation C-index = 0.5229
2025-08-07 13:27:48 [INFO] Early stopping triggered. Best score: 0.5359
2025-08-07 13:27:48 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:48 [INFO] Feature importances range: [-0.0320, 0.0231]
2025-08-07 13:27:48 [INFO] Feature importances mean: -0.0011, median: -0.0017
2025-08-07 13:27:48 [INFO] Fold 2: Selected 72 features with threshold 0.001
2025-08-07 13:27:49 [INFO] Fold 2: Best validation C-index = 0.4860
2025-08-07 13:27:52 [INFO] Early stopping triggered. Best score: 0.4859
2025-08-07 13:27:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:52 [INFO] Feature importances range: [-0.0254, 0.0317]
2025-08-07 13:27:52 [INFO] Feature importances mean: 0.0004, median: -0.0000
2025-08-07 13:27:52 [INFO] Fold 3: Selected 83 features with threshold 0.001
2025-08-07 13:27:53 [INFO] Fold 3: Best validation C-index = 0.4841
2025-08-07 13:27:59 [INFO] Early stopping triggered. Best score: 0.5311
2025-08-07 13:27:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:27:59 [INFO] Feature importances range: [-0.0352, 0.0252]
2025-08-07 13:27:59 [INFO] Feature importances mean: -0.0015, median: -0.0011
2025-08-07 13:27:59 [INFO] Fold 4: Selected 74 features with threshold 0.001
2025-08-07 13:28:00 [INFO] Fold 4: Best validation C-index = 0.5276
2025-08-07 13:28:04 [INFO] Early stopping triggered. Best score: 0.5053
2025-08-07 13:28:04 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:04 [INFO] Feature importances range: [-0.0278, 0.0276]
2025-08-07 13:28:04 [INFO] Feature importances mean: -0.0012, median: -0.0015
2025-08-07 13:28:04 [INFO] Fold 5: Selected 77 features with threshold 0.001
2025-08-07 13:28:05 [INFO] Fold 5: Best validation C-index = 0.4750
2025-08-07 13:28:09 [INFO] Early stopping triggered. Best score: 0.4796
2025-08-07 13:28:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:09 [INFO] Feature importances range: [-0.0262, 0.0220]
2025-08-07 13:28:09 [INFO] Feature importances mean: -0.0002, median: 0.0003
2025-08-07 13:28:09 [INFO] Fold 6: Selected 85 features with threshold 0.001
2025-08-07 13:28:11 [INFO] Fold 6: Best validation C-index = 0.4918
2025-08-07 13:28:15 [INFO] Early stopping triggered. Best score: 0.5079
2025-08-07 13:28:15 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:15 [INFO] Feature importances range: [-0.0354, 0.0302]
2025-08-07 13:28:15 [INFO] Feature importances mean: -0.0006, median: -0.0002
2025-08-07 13:28:15 [INFO] Fold 7: Selected 83 features with threshold 0.001
2025-08-07 13:28:16 [INFO] Fold 7: Best validation C-index = 0.5353
2025-08-07 13:28:18 [INFO] Early stopping triggered. Best score: 0.4917
2025-08-07 13:28:18 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:18 [INFO] Feature importances range: [-0.0314, 0.0230]
2025-08-07 13:28:18 [INFO] Feature importances mean: 0.0001, median: 0.0002
2025-08-07 13:28:18 [INFO] Fold 8: Selected 84 features with threshold 0.001
2025-08-07 13:28:19 [INFO] Fold 8: Best validation C-index = 0.4757
2025-08-07 13:28:21 [INFO] Early stopping triggered. Best score: 0.4821
2025-08-07 13:28:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:21 [INFO] Feature importances range: [-0.0257, 0.0278]
2025-08-07 13:28:21 [INFO] Feature importances mean: -0.0008, median: -0.0012
2025-08-07 13:28:21 [INFO] Fold 9: Selected 76 features with threshold 0.001
2025-08-07 13:28:22 [INFO] Fold 9: Best validation C-index = 0.4957
2025-08-07 13:28:22 [INFO] Found 76 common features across 10 folds
2025-08-07 13:28:22 [INFO] Common features: ['100A_Flattop_Positive', '100V_V280_H_Y', '200V_V280_H_Z', '200V_V280_L_Y', '200V_V700_L_Y', '200V_V700_L_Z', '300V_V280_L_Y', '300V_V700_H_Y', '328A_Flattop_Negative', '328A_IERROR_RMS']...
2025-08-07 13:28:22 [INFO] Trial completed: CV C-index = 0.4982 (from 10 successful folds)
2025-08-07 13:28:22 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:28:22 [INFO] Hidden dims: [47, 57, 28], activation: relu, optimizer: Adagrad
2025-08-07 13:28:25 [INFO] Early stopping triggered. Best score: 0.4748
2025-08-07 13:28:25 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:25 [INFO] Feature importances range: [-0.0591, -0.0021]
2025-08-07 13:28:25 [INFO] Feature importances mean: -0.0325, median: -0.0326
2025-08-07 13:28:25 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:28:25 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:28:26 [INFO] Fold 0: Best validation C-index = 0.4845
2025-08-07 13:28:29 [INFO] Early stopping triggered. Best score: 0.4741
2025-08-07 13:28:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:29 [INFO] Feature importances range: [-0.0585, -0.0068]
2025-08-07 13:28:29 [INFO] Feature importances mean: -0.0332, median: -0.0338
2025-08-07 13:28:29 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:28:29 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:28:29 [INFO] Fold 1: Best validation C-index = 0.5165
2025-08-07 13:28:43 [INFO] Early stopping triggered. Best score: 0.5494
2025-08-07 13:28:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:43 [INFO] Feature importances range: [-0.0987, -0.0464]
2025-08-07 13:28:43 [INFO] Feature importances mean: -0.0739, median: -0.0734
2025-08-07 13:28:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:28:43 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:28:43 [INFO] Fold 2: Best validation C-index = 0.4780
2025-08-07 13:28:59 [INFO] Early stopping triggered. Best score: 0.5132
2025-08-07 13:28:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:28:59 [INFO] Feature importances range: [-0.1059, -0.0564]
2025-08-07 13:28:59 [INFO] Feature importances mean: -0.0820, median: -0.0824
2025-08-07 13:28:59 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:28:59 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:29:00 [INFO] Fold 3: Best validation C-index = 0.4865
2025-08-07 13:29:03 [INFO] Early stopping triggered. Best score: 0.4879
2025-08-07 13:29:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:03 [INFO] Feature importances range: [-0.0609, -0.0062]
2025-08-07 13:29:03 [INFO] Feature importances mean: -0.0351, median: -0.0355
2025-08-07 13:29:03 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:03 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:29:04 [INFO] Fold 4: Best validation C-index = 0.5310
2025-08-07 13:29:11 [INFO] Early stopping triggered. Best score: 0.5667
2025-08-07 13:29:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:11 [INFO] Feature importances range: [-0.0733, -0.0217]
2025-08-07 13:29:11 [INFO] Feature importances mean: -0.0502, median: -0.0504
2025-08-07 13:29:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:11 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:29:11 [INFO] Fold 5: Best validation C-index = 0.5567
2025-08-07 13:29:14 [INFO] Early stopping triggered. Best score: 0.5456
2025-08-07 13:29:14 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:14 [INFO] Feature importances range: [-0.0583, -0.0054]
2025-08-07 13:29:14 [INFO] Feature importances mean: -0.0325, median: -0.0323
2025-08-07 13:29:14 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:14 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:29:15 [INFO] Fold 6: Best validation C-index = 0.5331
2025-08-07 13:29:18 [INFO] Early stopping triggered. Best score: 0.4743
2025-08-07 13:29:18 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:18 [INFO] Feature importances range: [-0.0586, -0.0074]
2025-08-07 13:29:18 [INFO] Feature importances mean: -0.0375, median: -0.0359
2025-08-07 13:29:18 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:18 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:29:18 [INFO] Fold 7: Best validation C-index = 0.5037
2025-08-07 13:29:21 [INFO] Early stopping triggered. Best score: 0.5745
2025-08-07 13:29:21 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:21 [INFO] Feature importances range: [-0.0535, -0.0055]
2025-08-07 13:29:21 [INFO] Feature importances mean: -0.0315, median: -0.0324
2025-08-07 13:29:21 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:21 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:29:22 [INFO] Fold 8: Best validation C-index = 0.5110
2025-08-07 13:29:26 [INFO] Early stopping triggered. Best score: 0.5232
2025-08-07 13:29:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:26 [INFO] Feature importances range: [-0.0595, -0.0102]
2025-08-07 13:29:26 [INFO] Feature importances mean: -0.0353, median: -0.0359
2025-08-07 13:29:26 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:26 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:29:26 [INFO] Fold 9: Best validation C-index = 0.4817
2025-08-07 13:29:26 [INFO] Found 5 common features across 10 folds
2025-08-07 13:29:26 [INFO] Common features: ['Y_CLOSED_LOOP_V280_H_Y', 'PULSE_280V_Y1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'X_CLOSED_LOOP_V280_H_X', 'X_CLOSED_LOOP_V280_L_Z']...
2025-08-07 13:29:26 [INFO] Trial completed: CV C-index = 0.5083 (from 10 successful folds)
2025-08-07 13:29:26 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:29:26 [INFO] Hidden dims: [40, 7, 41, 13], activation: relu, optimizer: Adagrad
2025-08-07 13:29:29 [INFO] Early stopping triggered. Best score: 0.5472
2025-08-07 13:29:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:29 [INFO] Feature importances range: [-0.7958, -0.7928]
2025-08-07 13:29:29 [INFO] Feature importances mean: -0.7945, median: -0.7945
2025-08-07 13:29:29 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:29 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:29:30 [INFO] Fold 0: Best validation C-index = 0.5432
2025-08-07 13:29:32 [INFO] Early stopping triggered. Best score: 0.5653
2025-08-07 13:29:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:32 [INFO] Feature importances range: [-0.8037, -0.8011]
2025-08-07 13:29:32 [INFO] Feature importances mean: -0.8025, median: -0.8026
2025-08-07 13:29:32 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:32 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:29:33 [INFO] Fold 1: Best validation C-index = 0.5467
2025-08-07 13:29:36 [INFO] Early stopping triggered. Best score: 0.5053
2025-08-07 13:29:36 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:36 [INFO] Feature importances range: [-0.7960, -0.7932]
2025-08-07 13:29:36 [INFO] Feature importances mean: -0.7945, median: -0.7945
2025-08-07 13:29:36 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:36 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:29:36 [INFO] Fold 2: Best validation C-index = 0.5141
2025-08-07 13:29:39 [INFO] Early stopping triggered. Best score: 0.5219
2025-08-07 13:29:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:39 [INFO] Feature importances range: [-0.8011, -0.7983]
2025-08-07 13:29:39 [INFO] Feature importances mean: -0.7999, median: -0.7999
2025-08-07 13:29:39 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:39 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:29:40 [INFO] Fold 3: Best validation C-index = 0.4576
2025-08-07 13:29:43 [INFO] Early stopping triggered. Best score: 0.5644
2025-08-07 13:29:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:43 [INFO] Feature importances range: [-0.7983, -0.7954]
2025-08-07 13:29:43 [INFO] Feature importances mean: -0.7972, median: -0.7972
2025-08-07 13:29:43 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:43 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:29:43 [INFO] Fold 4: Best validation C-index = 0.5277
2025-08-07 13:29:46 [INFO] Early stopping triggered. Best score: 0.5103
2025-08-07 13:29:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:46 [INFO] Feature importances range: [-0.8011, -0.7981]
2025-08-07 13:29:46 [INFO] Feature importances mean: -0.7999, median: -0.7999
2025-08-07 13:29:46 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:46 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:29:47 [INFO] Fold 5: Best validation C-index = 0.4873
2025-08-07 13:29:49 [INFO] Early stopping triggered. Best score: 0.5313
2025-08-07 13:29:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:50 [INFO] Feature importances range: [-0.7990, -0.7960]
2025-08-07 13:29:50 [INFO] Feature importances mean: -0.7973, median: -0.7973
2025-08-07 13:29:50 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:50 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:29:50 [INFO] Fold 6: Best validation C-index = 0.5002
2025-08-07 13:29:53 [INFO] Early stopping triggered. Best score: 0.5070
2025-08-07 13:29:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:53 [INFO] Feature importances range: [-0.7960, -0.7931]
2025-08-07 13:29:53 [INFO] Feature importances mean: -0.7945, median: -0.7945
2025-08-07 13:29:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:53 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:29:54 [INFO] Fold 7: Best validation C-index = 0.5056
2025-08-07 13:29:57 [INFO] Early stopping triggered. Best score: 0.5174
2025-08-07 13:29:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:29:57 [INFO] Feature importances range: [-0.8038, -0.8008]
2025-08-07 13:29:57 [INFO] Feature importances mean: -0.8025, median: -0.8025
2025-08-07 13:29:57 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:29:57 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:29:57 [INFO] Fold 8: Best validation C-index = 0.4915
2025-08-07 13:30:00 [INFO] Early stopping triggered. Best score: 0.5359
2025-08-07 13:30:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:00 [INFO] Feature importances range: [-0.8061, -0.8035]
2025-08-07 13:30:00 [INFO] Feature importances mean: -0.8050, median: -0.8050
2025-08-07 13:30:00 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:30:00 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:30:01 [INFO] Fold 9: Best validation C-index = 0.5074
2025-08-07 13:30:01 [INFO] Found 10 common features across 10 folds
2025-08-07 13:30:01 [INFO] Common features: ['Y_CLOSED_LOOP_V700_H_Y', 'EPI_280V_Y2_LOW', 'PULSE_700V_Z2_LOW', 'Y_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V280_L_Z', '100V_V700_L_X', '100V_V280_L_Z', '200V_V700_L_Y', 'EPI_IERROR_RMS', 'OUTPUT_VOLTAGE_DISCH_V700_H_X']...
2025-08-07 13:30:01 [INFO] Trial completed: CV C-index = 0.5081 (from 10 successful folds)
2025-08-07 13:30:01 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:30:01 [INFO] Hidden dims: [34, 27, 32], activation: relu, optimizer: Adagrad
2025-08-07 13:30:03 [INFO] Early stopping triggered. Best score: 0.5523
2025-08-07 13:30:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:03 [INFO] Feature importances range: [-0.0412, 0.0393]
2025-08-07 13:30:03 [INFO] Feature importances mean: -0.0018, median: -0.0023
2025-08-07 13:30:03 [INFO] Fold 0: Selected 78 features with threshold 0.001
2025-08-07 13:30:05 [INFO] Fold 0: Best validation C-index = 0.5102
2025-08-07 13:30:08 [INFO] Early stopping triggered. Best score: 0.5082
2025-08-07 13:30:08 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:08 [INFO] Feature importances range: [-0.0423, 0.0370]
2025-08-07 13:30:08 [INFO] Feature importances mean: -0.0018, median: -0.0011
2025-08-07 13:30:08 [INFO] Fold 1: Selected 83 features with threshold 0.001
2025-08-07 13:30:09 [INFO] Fold 1: Best validation C-index = 0.5206
2025-08-07 13:30:16 [INFO] Early stopping triggered. Best score: 0.5305
2025-08-07 13:30:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:16 [INFO] Feature importances range: [-0.0464, 0.0408]
2025-08-07 13:30:16 [INFO] Feature importances mean: -0.0014, median: -0.0002
2025-08-07 13:30:16 [INFO] Fold 2: Selected 88 features with threshold 0.001
2025-08-07 13:30:17 [INFO] Fold 2: Best validation C-index = 0.4956
2025-08-07 13:30:20 [INFO] Early stopping triggered. Best score: 0.5044
2025-08-07 13:30:20 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:20 [INFO] Feature importances range: [-0.0469, 0.0413]
2025-08-07 13:30:20 [INFO] Feature importances mean: -0.0012, median: -0.0012
2025-08-07 13:30:20 [INFO] Fold 3: Selected 84 features with threshold 0.001
2025-08-07 13:30:22 [INFO] Fold 3: Best validation C-index = 0.5107
2025-08-07 13:30:26 [INFO] Early stopping triggered. Best score: 0.5572
2025-08-07 13:30:26 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:26 [INFO] Feature importances range: [-0.0446, 0.0369]
2025-08-07 13:30:26 [INFO] Feature importances mean: -0.0034, median: -0.0027
2025-08-07 13:30:26 [INFO] Fold 4: Selected 73 features with threshold 0.001
2025-08-07 13:30:28 [INFO] Fold 4: Best validation C-index = 0.5111
2025-08-07 13:30:35 [INFO] Early stopping triggered. Best score: 0.5274
2025-08-07 13:30:35 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:35 [INFO] Feature importances range: [-0.0541, 0.0727]
2025-08-07 13:30:35 [INFO] Feature importances mean: -0.0018, median: -0.0031
2025-08-07 13:30:35 [INFO] Fold 5: Selected 80 features with threshold 0.001
2025-08-07 13:30:37 [INFO] Fold 5: Best validation C-index = 0.4940
2025-08-07 13:30:43 [INFO] Early stopping triggered. Best score: 0.4978
2025-08-07 13:30:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:43 [INFO] Feature importances range: [-0.0534, 0.0451]
2025-08-07 13:30:43 [INFO] Feature importances mean: -0.0041, median: -0.0034
2025-08-07 13:30:43 [INFO] Fold 6: Selected 78 features with threshold 0.001
2025-08-07 13:30:44 [INFO] Fold 6: Best validation C-index = 0.4952
2025-08-07 13:30:47 [INFO] Early stopping triggered. Best score: 0.5104
2025-08-07 13:30:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:47 [INFO] Feature importances range: [-0.0431, 0.0366]
2025-08-07 13:30:47 [INFO] Feature importances mean: -0.0051, median: -0.0037
2025-08-07 13:30:47 [INFO] Fold 7: Selected 72 features with threshold 0.001
2025-08-07 13:30:48 [INFO] Fold 7: Best validation C-index = 0.4914
2025-08-07 13:30:52 [INFO] Early stopping triggered. Best score: 0.5231
2025-08-07 13:30:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:52 [INFO] Feature importances range: [-0.0402, 0.0341]
2025-08-07 13:30:52 [INFO] Feature importances mean: -0.0040, median: -0.0044
2025-08-07 13:30:52 [INFO] Fold 8: Selected 65 features with threshold 0.001
2025-08-07 13:30:53 [INFO] Fold 8: Best validation C-index = 0.5059
2025-08-07 13:30:58 [INFO] Early stopping triggered. Best score: 0.5045
2025-08-07 13:30:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:30:58 [INFO] Feature importances range: [-0.0483, 0.0453]
2025-08-07 13:30:58 [INFO] Feature importances mean: -0.0024, median: -0.0028
2025-08-07 13:30:58 [INFO] Fold 9: Selected 77 features with threshold 0.001
2025-08-07 13:31:00 [INFO] Fold 9: Best validation C-index = 0.5070
2025-08-07 13:31:00 [INFO] Found 84 common features across 10 folds
2025-08-07 13:31:00 [INFO] Common features: ['100A_Flattop_Positive', '100V_V280_H_Z', '100V_V280_L_X', '100V_V700_H_Z', '200V_V280_H_Y', '200V_V700_H_X', '200V_V700_H_Y', '200V_V700_L_Z', '300V_V700_L_X', '300V_V700_L_Y']...
2025-08-07 13:31:00 [INFO] Trial completed: CV C-index = 0.5042 (from 10 successful folds)
2025-08-07 13:31:00 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:31:00 [INFO] Hidden dims: [53, 50, 12, 61], activation: tanh, optimizer: SGD
2025-08-07 13:31:02 [INFO] Early stopping triggered. Best score: 0.5264
2025-08-07 13:31:02 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:02 [INFO] Feature importances range: [-0.0274, 0.0274]
2025-08-07 13:31:02 [INFO] Feature importances mean: -0.0018, median: -0.0026
2025-08-07 13:31:02 [INFO] Fold 0: Selected 67 features with threshold 0.001
2025-08-07 13:31:04 [INFO] Fold 0: Best validation C-index = 0.4473
2025-08-07 13:31:07 [INFO] Early stopping triggered. Best score: 0.4363
2025-08-07 13:31:07 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:07 [INFO] Feature importances range: [-0.0228, 0.0326]
2025-08-07 13:31:07 [INFO] Feature importances mean: 0.0004, median: 0.0007
2025-08-07 13:31:07 [INFO] Fold 1: Selected 89 features with threshold 0.001
2025-08-07 13:31:08 [INFO] Fold 1: Best validation C-index = 0.4645
2025-08-07 13:31:12 [INFO] Early stopping triggered. Best score: 0.5148
2025-08-07 13:31:12 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:12 [INFO] Feature importances range: [-0.0323, 0.0246]
2025-08-07 13:31:12 [INFO] Feature importances mean: -0.0024, median: -0.0023
2025-08-07 13:31:12 [INFO] Fold 2: Selected 71 features with threshold 0.001
2025-08-07 13:31:13 [INFO] Fold 2: Best validation C-index = 0.5633
2025-08-07 13:31:39 [INFO] Early stopping triggered. Best score: 0.5337
2025-08-07 13:31:39 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:39 [INFO] Feature importances range: [-0.0381, 0.0181]
2025-08-07 13:31:39 [INFO] Feature importances mean: -0.0112, median: -0.0103
2025-08-07 13:31:39 [INFO] Fold 3: Selected 30 features with threshold 0.001
2025-08-07 13:31:40 [INFO] Fold 3: Best validation C-index = 0.4973
2025-08-07 13:31:43 [INFO] Early stopping triggered. Best score: 0.5078
2025-08-07 13:31:43 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:43 [INFO] Feature importances range: [-0.0258, 0.0292]
2025-08-07 13:31:43 [INFO] Feature importances mean: -0.0007, median: -0.0008
2025-08-07 13:31:43 [INFO] Fold 4: Selected 76 features with threshold 0.001
2025-08-07 13:31:44 [INFO] Fold 4: Best validation C-index = 0.5014
2025-08-07 13:31:47 [INFO] Early stopping triggered. Best score: 0.5518
2025-08-07 13:31:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:47 [INFO] Feature importances range: [-0.0292, 0.0280]
2025-08-07 13:31:47 [INFO] Feature importances mean: -0.0009, median: -0.0012
2025-08-07 13:31:47 [INFO] Fold 5: Selected 76 features with threshold 0.001
2025-08-07 13:31:48 [INFO] Fold 5: Best validation C-index = 0.4771
2025-08-07 13:31:50 [INFO] Early stopping triggered. Best score: 0.4850
2025-08-07 13:31:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:50 [INFO] Feature importances range: [-0.0314, 0.0337]
2025-08-07 13:31:50 [INFO] Feature importances mean: -0.0029, median: -0.0019
2025-08-07 13:31:50 [INFO] Fold 6: Selected 64 features with threshold 0.001
2025-08-07 13:31:52 [INFO] Fold 6: Best validation C-index = 0.5104
2025-08-07 13:31:57 [INFO] Early stopping triggered. Best score: 0.4910
2025-08-07 13:31:57 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:31:57 [INFO] Feature importances range: [-0.0296, 0.0249]
2025-08-07 13:31:57 [INFO] Feature importances mean: -0.0014, median: -0.0009
2025-08-07 13:31:57 [INFO] Fold 7: Selected 76 features with threshold 0.001
2025-08-07 13:31:58 [INFO] Fold 7: Best validation C-index = 0.4648
2025-08-07 13:32:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:27 [INFO] Feature importances range: [-0.0553, 0.0288]
2025-08-07 13:32:27 [INFO] Feature importances mean: -0.0125, median: -0.0115
2025-08-07 13:32:27 [INFO] Fold 8: Selected 31 features with threshold 0.001
2025-08-07 13:32:28 [INFO] Fold 8: Best validation C-index = 0.5520
2025-08-07 13:32:31 [INFO] Early stopping triggered. Best score: 0.5278
2025-08-07 13:32:31 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:31 [INFO] Feature importances range: [-0.0289, 0.0283]
2025-08-07 13:32:31 [INFO] Feature importances mean: -0.0003, median: -0.0006
2025-08-07 13:32:31 [INFO] Fold 9: Selected 77 features with threshold 0.001
2025-08-07 13:32:32 [INFO] Fold 9: Best validation C-index = 0.4389
2025-08-07 13:32:32 [INFO] Found 46 common features across 10 folds
2025-08-07 13:32:32 [INFO] Common features: ['100V_V280_H_Y', '300V_V280_H_X', '300V_V280_L_Y', '328A_Flattop_Positive', '420V_V280_L_Z', 'BUS_VOLTAGE_VBUS', 'BUS_VOLTAGE_VX_LOW', 'EPI_280V_Y1_HIGH', 'EPI_700V_X2_LOW', 'EPI_700V_Y1_LOW']...
2025-08-07 13:32:32 [INFO] Trial completed: CV C-index = 0.4917 (from 10 successful folds)
2025-08-07 13:32:32 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:32:32 [INFO] Hidden dims: [59, 56, 21], activation: selu, optimizer: Adagrad
2025-08-07 13:32:34 [INFO] Early stopping triggered. Best score: 0.5280
2025-08-07 13:32:34 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:34 [INFO] Feature importances range: [-0.0577, -0.0004]
2025-08-07 13:32:34 [INFO] Feature importances mean: -0.0283, median: -0.0286
2025-08-07 13:32:34 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:32:34 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:32:35 [INFO] Fold 0: Best validation C-index = 0.5225
2025-08-07 13:32:38 [INFO] Early stopping triggered. Best score: 0.5631
2025-08-07 13:32:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:38 [INFO] Feature importances range: [-0.0612, -0.0019]
2025-08-07 13:32:38 [INFO] Feature importances mean: -0.0297, median: -0.0292
2025-08-07 13:32:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:32:38 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:32:39 [INFO] Fold 1: Best validation C-index = 0.4930
2025-08-07 13:32:42 [INFO] Early stopping triggered. Best score: 0.5573
2025-08-07 13:32:42 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:42 [INFO] Feature importances range: [-0.0531, -0.0045]
2025-08-07 13:32:42 [INFO] Feature importances mean: -0.0308, median: -0.0313
2025-08-07 13:32:42 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:32:42 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:32:43 [INFO] Fold 2: Best validation C-index = 0.5559
2025-08-07 13:32:45 [INFO] Early stopping triggered. Best score: 0.5033
2025-08-07 13:32:45 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:45 [INFO] Feature importances range: [-0.0595, 0.0019]
2025-08-07 13:32:45 [INFO] Feature importances mean: -0.0276, median: -0.0275
2025-08-07 13:32:45 [INFO] Fold 3: Selected 1 features with threshold 0.001
2025-08-07 13:32:46 [INFO] Fold 3: Best validation C-index = 0.5216
2025-08-07 13:32:49 [INFO] Early stopping triggered. Best score: 0.5581
2025-08-07 13:32:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:49 [INFO] Feature importances range: [-0.0543, -0.0019]
2025-08-07 13:32:49 [INFO] Feature importances mean: -0.0321, median: -0.0318
2025-08-07 13:32:49 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:32:49 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:32:50 [INFO] Fold 4: Best validation C-index = 0.5027
2025-08-07 13:32:52 [INFO] Early stopping triggered. Best score: 0.5168
2025-08-07 13:32:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:52 [INFO] Feature importances range: [-0.0618, -0.0050]
2025-08-07 13:32:52 [INFO] Feature importances mean: -0.0271, median: -0.0268
2025-08-07 13:32:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:32:52 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:32:53 [INFO] Fold 5: Best validation C-index = 0.5068
2025-08-07 13:32:56 [INFO] Early stopping triggered. Best score: 0.5558
2025-08-07 13:32:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:32:56 [INFO] Feature importances range: [-0.0494, 0.0033]
2025-08-07 13:32:56 [INFO] Feature importances mean: -0.0263, median: -0.0268
2025-08-07 13:32:56 [INFO] Fold 6: Selected 3 features with threshold 0.001
2025-08-07 13:32:57 [INFO] Fold 6: Best validation C-index = 0.4600
2025-08-07 13:33:00 [INFO] Early stopping triggered. Best score: 0.5306
2025-08-07 13:33:00 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:00 [INFO] Feature importances range: [-0.0578, -0.0061]
2025-08-07 13:33:00 [INFO] Feature importances mean: -0.0335, median: -0.0326
2025-08-07 13:33:00 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:00 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:33:01 [INFO] Fold 7: Best validation C-index = 0.4779
2025-08-07 13:33:03 [INFO] Early stopping triggered. Best score: 0.4700
2025-08-07 13:33:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:03 [INFO] Feature importances range: [-0.0500, 0.0015]
2025-08-07 13:33:03 [INFO] Feature importances mean: -0.0263, median: -0.0259
2025-08-07 13:33:03 [INFO] Fold 8: Selected 1 features with threshold 0.001
2025-08-07 13:33:04 [INFO] Fold 8: Best validation C-index = 0.4927
2025-08-07 13:33:06 [INFO] Early stopping triggered. Best score: 0.4925
2025-08-07 13:33:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:06 [INFO] Feature importances range: [-0.0537, -0.0003]
2025-08-07 13:33:06 [INFO] Feature importances mean: -0.0267, median: -0.0265
2025-08-07 13:33:06 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:06 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:33:07 [INFO] Fold 9: Best validation C-index = 0.4621
2025-08-07 13:33:07 [INFO] Found 10 common features across 10 folds
2025-08-07 13:33:07 [INFO] Common features: ['X_CLOSED_LOOP_V700_L_Z', 'PULSE_280V_Z2_HIGH', 'EPI_700V_Z1_HIGH', '100V_V700_L_Y', '300V_V280_H_Y', 'X_CLOSED_LOOP_V280_H_Z', '100V_V280_H_X', 'X_CLOSED_LOOP_V700_H_X', 'Shot_Shot_Stab', '300V_V700_L_Z']...
2025-08-07 13:33:07 [INFO] Trial completed: CV C-index = 0.4995 (from 10 successful folds)
2025-08-07 13:33:07 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:33:07 [INFO] Hidden dims: [62, 54], activation: selu, optimizer: Adagrad
2025-08-07 13:33:07 [WARNING] Error in fold 0: NaNs detected in inputs, please correct or drop.
2025-08-07 13:33:09 [INFO] Early stopping triggered. Best score: 0.5245
2025-08-07 13:33:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:09 [INFO] Feature importances range: [-0.9407, -0.8503]
2025-08-07 13:33:09 [INFO] Feature importances mean: -0.9048, median: -0.9074
2025-08-07 13:33:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:09 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:33:09 [INFO] Fold 1: Best validation C-index = 0.5280
2025-08-07 13:33:10 [WARNING] Error in fold 2: NaNs detected in inputs, please correct or drop.
2025-08-07 13:33:11 [INFO] Early stopping triggered. Best score: 0.5214
2025-08-07 13:33:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:11 [INFO] Feature importances range: [-0.9104, -0.7818]
2025-08-07 13:33:11 [INFO] Feature importances mean: -0.8775, median: -0.8881
2025-08-07 13:33:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:11 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:33:12 [INFO] Fold 3: Best validation C-index = 0.5631
2025-08-07 13:33:13 [INFO] Early stopping triggered. Best score: 0.5263
2025-08-07 13:33:13 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:13 [INFO] Feature importances range: [-0.8866, -0.7507]
2025-08-07 13:33:13 [INFO] Feature importances mean: -0.8386, median: -0.8439
2025-08-07 13:33:13 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:13 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:33:13 [INFO] Fold 4: Best validation C-index = 0.5336
2025-08-07 13:33:14 [WARNING] Error in fold 5: NaNs detected in inputs, please correct or drop.
2025-08-07 13:33:15 [INFO] Early stopping triggered. Best score: 0.5470
2025-08-07 13:33:15 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:15 [INFO] Feature importances range: [-0.9300, -0.8490]
2025-08-07 13:33:15 [INFO] Feature importances mean: -0.9020, median: -0.9079
2025-08-07 13:33:15 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:15 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:33:16 [INFO] Fold 6: Best validation C-index = 0.5460
2025-08-07 13:33:18 [INFO] Early stopping triggered. Best score: 0.5138
2025-08-07 13:33:18 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:18 [INFO] Feature importances range: [-0.9936, -0.8316]
2025-08-07 13:33:18 [INFO] Feature importances mean: -0.9427, median: -0.9515
2025-08-07 13:33:18 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:18 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:33:18 [INFO] Fold 7: Best validation C-index = 0.5003
2025-08-07 13:33:18 [WARNING] Error in fold 8: NaNs detected in inputs, please correct or drop.
2025-08-07 13:33:20 [INFO] Early stopping triggered. Best score: 0.5289
2025-08-07 13:33:20 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:20 [INFO] Feature importances range: [-0.9478, -0.8467]
2025-08-07 13:33:20 [INFO] Feature importances mean: -0.9187, median: -0.9196
2025-08-07 13:33:20 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:20 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:33:20 [INFO] Fold 9: Best validation C-index = 0.4852
2025-08-07 13:33:20 [INFO] Found 14 common features across 6 folds
2025-08-07 13:33:20 [INFO] Common features: ['X_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_H_Y', 'Y_CLOSED_LOOP_V700_H_Z', '420V_V280_L_Z', 'Z_CLOSED_LOOP_V700_H_X', 'Z_CLOSED_LOOP_V280_H_X', 'Y_CLOSED_LOOP_V700_H_Y', 'Z_CLOSED_LOOP_V280_L_Z', 'Y_CLOSED_LOOP_V700_L_Y', 'Y_CLOSED_LOOP_V700_L_X']...
2025-08-07 13:33:20 [INFO] Trial completed: CV C-index = 0.5260 (from 6 successful folds)
2025-08-07 13:33:20 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:33:20 [INFO] Hidden dims: [62, 64], activation: sigmoid, optimizer: Adagrad
2025-08-07 13:33:23 [INFO] Early stopping triggered. Best score: 0.5715
2025-08-07 13:33:23 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:23 [INFO] Feature importances range: [-1.0174, -1.0141]
2025-08-07 13:33:23 [INFO] Feature importances mean: -1.0157, median: -1.0157
2025-08-07 13:33:23 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:23 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:33:24 [INFO] Fold 0: Best validation C-index = 0.5074
2025-08-07 13:33:27 [INFO] Early stopping triggered. Best score: 0.5314
2025-08-07 13:33:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:27 [INFO] Feature importances range: [-1.0013, -0.9972]
2025-08-07 13:33:27 [INFO] Feature importances mean: -0.9992, median: -0.9993
2025-08-07 13:33:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:27 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:33:27 [INFO] Fold 1: Best validation C-index = 0.5479
2025-08-07 13:33:29 [INFO] Early stopping triggered. Best score: 0.5280
2025-08-07 13:33:29 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:29 [INFO] Feature importances range: [-0.9940, -0.9901]
2025-08-07 13:33:29 [INFO] Feature importances mean: -0.9918, median: -0.9918
2025-08-07 13:33:29 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:29 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:33:30 [INFO] Fold 2: Best validation C-index = 0.5346
2025-08-07 13:33:32 [INFO] Early stopping triggered. Best score: 0.5417
2025-08-07 13:33:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:32 [INFO] Feature importances range: [-0.9986, -0.9937]
2025-08-07 13:33:32 [INFO] Feature importances mean: -0.9957, median: -0.9956
2025-08-07 13:33:32 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:32 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:33:33 [INFO] Fold 3: Best validation C-index = 0.5446
2025-08-07 13:33:35 [INFO] Early stopping triggered. Best score: 0.5115
2025-08-07 13:33:35 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:35 [INFO] Feature importances range: [-1.0475, -1.0447]
2025-08-07 13:33:35 [INFO] Feature importances mean: -1.0460, median: -1.0460
2025-08-07 13:33:35 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:35 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:33:36 [INFO] Fold 4: Best validation C-index = 0.5044
2025-08-07 13:33:38 [INFO] Early stopping triggered. Best score: 0.5168
2025-08-07 13:33:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:38 [INFO] Feature importances range: [-0.9978, -0.9940]
2025-08-07 13:33:38 [INFO] Feature importances mean: -0.9956, median: -0.9956
2025-08-07 13:33:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:38 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:33:39 [INFO] Fold 5: Best validation C-index = 0.5254
2025-08-07 13:33:41 [INFO] Early stopping triggered. Best score: 0.5359
2025-08-07 13:33:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:41 [INFO] Feature importances range: [-1.0043, -1.0005]
2025-08-07 13:33:41 [INFO] Feature importances mean: -1.0027, median: -1.0028
2025-08-07 13:33:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:41 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:33:42 [INFO] Fold 6: Best validation C-index = 0.5195
2025-08-07 13:33:44 [INFO] Early stopping triggered. Best score: 0.5231
2025-08-07 13:33:44 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:44 [INFO] Feature importances range: [-1.0176, -1.0140]
2025-08-07 13:33:44 [INFO] Feature importances mean: -1.0158, median: -1.0158
2025-08-07 13:33:44 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:44 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:33:45 [INFO] Fold 7: Best validation C-index = 0.5135
2025-08-07 13:33:47 [INFO] Early stopping triggered. Best score: 0.5271
2025-08-07 13:33:47 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:47 [INFO] Feature importances range: [-1.0080, -1.0047]
2025-08-07 13:33:47 [INFO] Feature importances mean: -1.0062, median: -1.0061
2025-08-07 13:33:47 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:47 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:33:48 [INFO] Fold 8: Best validation C-index = 0.5158
2025-08-07 13:33:50 [INFO] Early stopping triggered. Best score: 0.5446
2025-08-07 13:33:50 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:50 [INFO] Feature importances range: [-1.0203, -1.0173]
2025-08-07 13:33:50 [INFO] Feature importances mean: -1.0189, median: -1.0189
2025-08-07 13:33:50 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:50 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:33:51 [INFO] Fold 9: Best validation C-index = 0.4884
2025-08-07 13:33:51 [INFO] Found 5 common features across 10 folds
2025-08-07 13:33:51 [INFO] Common features: ['PULSE_700V_Z1_LOW', '420V_V700_L_X', '200V_V700_H_X', 'X_CLOSED_LOOP_V280_L_Y', '420V_V700_H_Z']...
2025-08-07 13:33:51 [INFO] Trial completed: CV C-index = 0.5201 (from 10 successful folds)
2025-08-07 13:33:51 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:33:51 [INFO] Hidden dims: [62, 64], activation: sigmoid, optimizer: Adagrad
2025-08-07 13:33:52 [INFO] Early stopping triggered. Best score: 0.5355
2025-08-07 13:33:52 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:52 [INFO] Feature importances range: [-0.8482, -0.8392]
2025-08-07 13:33:52 [INFO] Feature importances mean: -0.8448, median: -0.8448
2025-08-07 13:33:52 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:52 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:33:53 [INFO] Fold 0: Best validation C-index = 0.4859
2025-08-07 13:33:54 [INFO] Early stopping triggered. Best score: 0.5216
2025-08-07 13:33:54 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:54 [INFO] Feature importances range: [-0.8971, -0.8913]
2025-08-07 13:33:54 [INFO] Feature importances mean: -0.8940, median: -0.8939
2025-08-07 13:33:54 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:54 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:33:54 [INFO] Fold 1: Best validation C-index = 0.4978
2025-08-07 13:33:56 [INFO] Early stopping triggered. Best score: 0.5215
2025-08-07 13:33:56 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:56 [INFO] Feature importances range: [-0.9035, -0.8977]
2025-08-07 13:33:56 [INFO] Feature importances mean: -0.9006, median: -0.9006
2025-08-07 13:33:56 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:56 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:33:56 [INFO] Fold 2: Best validation C-index = 0.5333
2025-08-07 13:33:58 [INFO] Early stopping triggered. Best score: 0.5218
2025-08-07 13:33:58 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:58 [INFO] Feature importances range: [-0.8498, -0.8410]
2025-08-07 13:33:58 [INFO] Feature importances mean: -0.8449, median: -0.8450
2025-08-07 13:33:58 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:58 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:33:58 [INFO] Fold 3: Best validation C-index = 0.5227
2025-08-07 13:33:59 [INFO] Early stopping triggered. Best score: 0.5330
2025-08-07 13:33:59 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:33:59 [INFO] Feature importances range: [-0.8836, -0.8757]
2025-08-07 13:33:59 [INFO] Feature importances mean: -0.8794, median: -0.8793
2025-08-07 13:33:59 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:33:59 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:34:00 [INFO] Fold 4: Best validation C-index = 0.4908
2025-08-07 13:34:01 [INFO] Early stopping triggered. Best score: 0.5700
2025-08-07 13:34:01 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:01 [INFO] Feature importances range: [-0.8899, -0.8834]
2025-08-07 13:34:01 [INFO] Feature importances mean: -0.8868, median: -0.8868
2025-08-07 13:34:01 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:01 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:34:02 [INFO] Fold 5: Best validation C-index = 0.4438
2025-08-07 13:34:03 [INFO] Early stopping triggered. Best score: 0.5192
2025-08-07 13:34:03 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:03 [INFO] Feature importances range: [-0.8665, -0.8594]
2025-08-07 13:34:03 [INFO] Feature importances mean: -0.8629, median: -0.8628
2025-08-07 13:34:03 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:03 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:34:04 [INFO] Fold 6: Best validation C-index = 0.4637
2025-08-07 13:34:06 [INFO] Early stopping triggered. Best score: 0.5185
2025-08-07 13:34:06 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:06 [INFO] Feature importances range: [-0.9031, -0.8979]
2025-08-07 13:34:06 [INFO] Feature importances mean: -0.9006, median: -0.9006
2025-08-07 13:34:06 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:06 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:34:07 [INFO] Fold 7: Best validation C-index = 0.4887
2025-08-07 13:34:09 [INFO] Early stopping triggered. Best score: 0.5097
2025-08-07 13:34:09 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:09 [INFO] Feature importances range: [-0.8825, -0.8759]
2025-08-07 13:34:09 [INFO] Feature importances mean: -0.8792, median: -0.8792
2025-08-07 13:34:09 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:09 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:34:09 [INFO] Fold 8: Best validation C-index = 0.4879
2025-08-07 13:34:11 [INFO] Early stopping triggered. Best score: 0.5197
2025-08-07 13:34:11 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:11 [INFO] Feature importances range: [-0.8603, -0.8511]
2025-08-07 13:34:11 [INFO] Feature importances mean: -0.8543, median: -0.8543
2025-08-07 13:34:11 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:11 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:34:13 [INFO] Fold 9: Best validation C-index = 0.4928
2025-08-07 13:34:13 [INFO] Found 7 common features across 10 folds
2025-08-07 13:34:13 [INFO] Common features: ['Z_CLOSED_LOOP_V700_H_Z', '200V_V280_H_X', 'EPI_700V_Y1_LOW', 'Y_CLOSED_LOOP_V700_H_Z', 'EPI_700V_X1_LOW', 'BUS_VOLTAGE_VY_LOW', '300V_V280_L_Y']...
2025-08-07 13:34:13 [INFO] Trial completed: CV C-index = 0.4907 (from 10 successful folds)
2025-08-07 13:34:13 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:34:13 [INFO] Hidden dims: [61, 48], activation: sigmoid, optimizer: Adagrad
2025-08-07 13:34:16 [INFO] Early stopping triggered. Best score: 0.5304
2025-08-07 13:34:16 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:16 [INFO] Feature importances range: [-1.0506, -1.0461]
2025-08-07 13:34:16 [INFO] Feature importances mean: -1.0478, median: -1.0478
2025-08-07 13:34:16 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:16 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:34:17 [INFO] Fold 0: Best validation C-index = 0.5049
2025-08-07 13:34:19 [INFO] Early stopping triggered. Best score: 0.5479
2025-08-07 13:34:19 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:19 [INFO] Feature importances range: [-1.0298, -1.0250]
2025-08-07 13:34:19 [INFO] Feature importances mean: -1.0275, median: -1.0276
2025-08-07 13:34:19 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:19 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:34:20 [INFO] Fold 1: Best validation C-index = 0.5124
2025-08-07 13:34:22 [INFO] Early stopping triggered. Best score: 0.5477
2025-08-07 13:34:22 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:22 [INFO] Feature importances range: [-1.0380, -1.0339]
2025-08-07 13:34:22 [INFO] Feature importances mean: -1.0360, median: -1.0360
2025-08-07 13:34:22 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:22 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:34:22 [INFO] Fold 2: Best validation C-index = 0.4522
2025-08-07 13:34:24 [INFO] Early stopping triggered. Best score: 0.5169
2025-08-07 13:34:24 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:24 [INFO] Feature importances range: [-1.0424, -1.0380]
2025-08-07 13:34:24 [INFO] Feature importances mean: -1.0401, median: -1.0401
2025-08-07 13:34:24 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:24 [INFO] Fold 3: Selected 10 features with threshold 0.001
2025-08-07 13:34:25 [INFO] Fold 3: Best validation C-index = 0.4982
2025-08-07 13:34:27 [INFO] Early stopping triggered. Best score: 0.5452
2025-08-07 13:34:27 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:27 [INFO] Feature importances range: [-1.0466, -1.0424]
2025-08-07 13:34:27 [INFO] Feature importances mean: -1.0440, median: -1.0440
2025-08-07 13:34:27 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:27 [INFO] Fold 4: Selected 10 features with threshold 0.001
2025-08-07 13:34:28 [INFO] Fold 4: Best validation C-index = 0.5122
2025-08-07 13:34:30 [INFO] Early stopping triggered. Best score: 0.5514
2025-08-07 13:34:30 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:30 [INFO] Feature importances range: [-1.0297, -1.0251]
2025-08-07 13:34:30 [INFO] Feature importances mean: -1.0276, median: -1.0277
2025-08-07 13:34:30 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:30 [INFO] Fold 5: Selected 10 features with threshold 0.001
2025-08-07 13:34:30 [INFO] Fold 5: Best validation C-index = 0.5335
2025-08-07 13:34:32 [INFO] Early stopping triggered. Best score: 0.5206
2025-08-07 13:34:32 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:32 [INFO] Feature importances range: [-1.0381, -1.0343]
2025-08-07 13:34:32 [INFO] Feature importances mean: -1.0361, median: -1.0361
2025-08-07 13:34:32 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:32 [INFO] Fold 6: Selected 10 features with threshold 0.001
2025-08-07 13:34:33 [INFO] Fold 6: Best validation C-index = 0.5231
2025-08-07 13:34:35 [INFO] Early stopping triggered. Best score: 0.5440
2025-08-07 13:34:35 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:35 [INFO] Feature importances range: [-1.0337, -1.0296]
2025-08-07 13:34:35 [INFO] Feature importances mean: -1.0318, median: -1.0318
2025-08-07 13:34:35 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:35 [INFO] Fold 7: Selected 10 features with threshold 0.001
2025-08-07 13:34:35 [INFO] Fold 7: Best validation C-index = 0.5153
2025-08-07 13:34:38 [INFO] Early stopping triggered. Best score: 0.5272
2025-08-07 13:34:38 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:38 [INFO] Feature importances range: [-1.0703, -1.0668]
2025-08-07 13:34:38 [INFO] Feature importances mean: -1.0688, median: -1.0688
2025-08-07 13:34:38 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:38 [INFO] Fold 8: Selected 10 features with threshold 0.001
2025-08-07 13:34:38 [INFO] Fold 8: Best validation C-index = 0.5457
2025-08-07 13:34:41 [INFO] Early stopping triggered. Best score: 0.5163
2025-08-07 13:34:41 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:41 [INFO] Feature importances range: [-1.0673, -1.0637]
2025-08-07 13:34:41 [INFO] Feature importances mean: -1.0654, median: -1.0654
2025-08-07 13:34:41 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:41 [INFO] Fold 9: Selected 10 features with threshold 0.001
2025-08-07 13:34:42 [INFO] Fold 9: Best validation C-index = 0.5158
2025-08-07 13:34:42 [INFO] Found 10 common features across 10 folds
2025-08-07 13:34:42 [INFO] Common features: ['Z_CLOSED_LOOP_V700_H_X', 'X_CLOSED_LOOP_V280_H_Z', '420V_V280_H_Z', 'Y_CLOSED_LOOP_V280_L_Z', 'OUTPUT_VOLTAGE_DISCH_V280_L_Z', 'Y_CLOSED_LOOP_V280_L_X', 'EPI_280V_X1_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'X_CLOSED_LOOP_V700_H_Z', '300V_V280_L_Y']...
2025-08-07 13:34:42 [INFO] Trial completed: CV C-index = 0.5113 (from 10 successful folds)
2025-08-07 13:34:42 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 13:34:42 [INFO] Hidden dims: [64, 43], activation: sigmoid, optimizer: Adagrad
2025-08-07 13:34:46 [INFO] Early stopping triggered. Best score: 0.5336
2025-08-07 13:34:46 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:46 [INFO] Feature importances range: [-0.8229, -0.7949]
2025-08-07 13:34:46 [INFO] Feature importances mean: -0.8085, median: -0.8090
2025-08-07 13:34:46 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:46 [INFO] Fold 0: Selected 10 features with threshold 0.001
2025-08-07 13:34:46 [INFO] Fold 0: Best validation C-index = 0.4867
2025-08-07 13:34:49 [INFO] Early stopping triggered. Best score: 0.5443
2025-08-07 13:34:49 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:49 [INFO] Feature importances range: [-0.6696, -0.6196]
2025-08-07 13:34:49 [INFO] Feature importances mean: -0.6402, median: -0.6407
2025-08-07 13:34:49 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:49 [INFO] Fold 1: Selected 10 features with threshold 0.001
2025-08-07 13:34:50 [INFO] Fold 1: Best validation C-index = 0.5147
2025-08-07 13:34:53 [INFO] Early stopping triggered. Best score: 0.5419
2025-08-07 13:34:53 [INFO] Successfully used get_gates(mode='raw') - got raw mu values
2025-08-07 13:34:53 [INFO] Feature importances range: [-0.6638, -0.6292]
2025-08-07 13:34:53 [INFO] Feature importances mean: -0.6462, median: -0.6462
2025-08-07 13:34:53 [INFO] No features above threshold, selected top 10 features
2025-08-07 13:34:53 [INFO] Fold 2: Selected 10 features with threshold 0.001
2025-08-07 13:34:53 [INFO] Fold 2: Best validation C-index = 0.5175
