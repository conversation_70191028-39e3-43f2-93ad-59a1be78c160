2025-08-07 14:34:04 [INFO] === STG Survival Analysis with Optuna Optimization ===
2025-08-07 14:34:04 [INFO] Logging to: logs\FeatureSelecSTG_Cox_20250807_143404.log
2025-08-07 14:34:04 [INFO] MPI processes: 1
2025-08-07 14:34:04 [INFO] Parameters: {'PATH': 'processed_equipment_data_all.csv', 'seed': 9, 'n_splits': 10, 'times': 1, 'result_dir': 'results', 'test_size': 0.1, 'max_epochs': 1000, 'n_trials': 1, 'threshold_gates': 0.01, 'backend': 'Weighted_cox', 'penalizer': 0.03}
2025-08-07 14:34:04 [INFO] Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-08-07 14:34:04 [INFO] Data scaler splite with random state 9
2025-08-07 14:34:04 [INFO] Process 0: Running 1 rounds starting from round 0
2025-08-07 14:34:04 [INFO] Process 0: Starting round 1/1
2025-08-07 14:34:04 [INFO] --- Starting STG Round 0---
2025-08-07 14:34:04 [INFO] Data split - Train/Val: (1777, 182), Test: (198, 182)
2025-08-07 14:34:04 [INFO] === Stage 1: Starting STG hyperparameter optimization ===
2025-08-07 14:34:04 [INFO] Use 1 Optuna trials
2025-08-07 14:34:04 [INFO] === Starting STG cross-validation optimization ===
2025-08-07 14:34:04 [INFO] Hidden dims: [33], activation: relu, optimizer: Adam
2025-08-07 14:34:23 [INFO] Feature importances range: [-0.0378, 0.0135]
2025-08-07 14:34:23 [INFO] Feature importances mean: -0.0108, median: -0.0111
2025-08-07 14:34:43 [WARNING] Could not extract feature selection results: all the input arrays must have same number of dimensions, but the array at index 0 has 2 dimension(s) and the array at index 1 has 1 dimension(s)
2025-08-07 14:34:44 [INFO] Feature importances range: [-0.0356, 0.0183]
2025-08-07 14:34:44 [INFO] Feature importances mean: -0.0074, median: -0.0076
