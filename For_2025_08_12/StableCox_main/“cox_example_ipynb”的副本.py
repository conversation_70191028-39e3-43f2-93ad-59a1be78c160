from sksurv.column import encode_categorical
from sksurv.metrics import concordance_index_ipcw, cumulative_dynamic_auc, brier_score
from sksurv.datasets import load_gbsg2
from sksurv.ensemble import RandomSurvivalForest
from sklearn.model_selection import train_test_split
import numpy as np
import pandas as pd

# 加载数据并划分
gbsg_X, gbsg_y = load_gbsg2()
gbsg_X = encode_categorical(gbsg_X)

X_train, X_test, y_train, y_test = train_test_split(
    gbsg_X, gbsg_y, stratify=gbsg_y["cens"], random_state=1
)


# 训练一个模型
rsf = RandomSurvivalForest(n_estimators=100, random_state=0)
rsf.fit(X_train, y_train)

# 1. 计算IPCW C-index
c_index_ipcw = concordance_index_ipcw(y_train, y_test, rsf.predict(X_test))
print(f"IPCW Concordance Index: {c_index_ipcw[0]:.4f}")

# 2. 计算时间依赖性AUC
# 定义评估的时间点
times = np.linspace(y_test["time"].min(), y_test["time"].max(), 10)
# 获取生存函数预测
surv_funcs = rsf.predict_survival_function(X_test)
# 将生存函数转换为特定时间点的风险分数
risk_scores_at_times = [[1 - func(t) for func in surv_funcs] for t in times]

auc, mean_auc = cumulative_dynamic_auc(y_train, y_test, risk_scores_at_times, times)
print(f"Time-dependent AUC at {times} days: {auc}")
print(f"Mean AUC over specified times: {mean_auc:.4f}")

# 3. 计算积分布里尔分数 (IBS)
# 定义评估的时间范围
time_grid = np.linspace(y_test["time"].min(), y_test["time"].max(), 100)
# 获取生存函数预测
surv_probs = rsf.predict_survival_function(X_test, return_array=True)
# 注意：predict_survival_function返回的time points可能与time_grid不完全匹配，需要插值
# 为了简化，这里假设rsf.event_times_与time_grid对齐，实际应用中需要更严谨的处理
# brier_score函数内部处理了插值
score = brier_score(y_train, y_test, surv_probs, rsf.is_event_times_)[1]
# 计算IBS
ibs = np.trapz(score, rsf.event_times_)
print(f"Integrated Brier Score (approx): {ibs:.4f}")


################################################################################
import pandas as pd
from lifelines import CoxPHFitter
from lifelines.datasets import load_rossi
from lifelines.utils import concordance_index
import matplotlib.pyplot as plt
# 加载示例数据
rossi_dataset = load_rossi()

# 拟合一个Cox模型
cph = CoxPHFitter()
cph.fit(rossi_dataset, duration_col='week', event_col='arrest')
cph.print_summary()
# 进行PH假设检验
# p_value_threshold: 警告用户的p值阈值
# show_plots: 是否显示Schoenfeld残差图
cph.check_assumptions(rossi_dataset, p_value_threshold=0.05, show_plots=True)
plt.show()

print(cph.concordance_index_)

# 假设 'prio' 变量违背了PH假设
# 将其作为分层变量，而不是作为普通协变量
cph_stratified = CoxPHFitter()
cph_stratified.fit(rossi_dataset,
                   duration_col='week',
                   event_col='arrest',
                   strata=['prio'])
cph_stratified.print_summary()
# 注意：'prio' 不会出现在系数表中，因为它的效应被基线风险吸收了

from lifelines import CoxTimeVaryingFitter
from lifelines.utils import to_long_format, add_covariate_to_timeline

# 这是一个概念性示例，实际数据转换会更复杂
# 假设我们有一个时变协变量 'var2' 的数据集 cv
# base_df 是包含 id, start, stop, event 的基础数据集
# long_df = add_covariate_to_timeline(base_df, cv,...)

# ctv = CoxTimeVaryingFitter()
# ctv.fit(long_df, id_col="id", event_col="event",
#         start_col="start", stop_col="stop", show_progress=True)
# ctv.print_summary()


import pandas as pd
import patsy
from lifelines import CoxPHFitter
from lifelines.datasets import load_rossi

rossi = load_rossi()

# 使用patsy为'age'变量创建自然样条基函数（4个自由度）
# 'ns(age, df=4)'
spline_terms = patsy.dmatrix("ns(age, df=4) - 1", data=rossi, return_type='dataframe')

# 将样条基与原始数据合并
rossi_spline = pd.concat([rossi.drop('age', axis=1), spline_terms], axis=1)

# 拟合带有样条项的Cox模型
cph_spline = CoxPHFitter(penalizer=0.1) # 添加L2正则化以控制系数大小
cph_spline.fit(rossi_spline, duration_col='week', event_col='arrest')

# 绘制'age'的非线性效应
cph_spline.plot_partial_effects_on_outcome(covariates='age', values=np.arange(16, 45))
plt.show()