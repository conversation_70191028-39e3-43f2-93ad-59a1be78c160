# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.metrics import brier_score, cumulative_dynamic_auc, integrated_brier_score
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings
warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
import optuna
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

def compute_comprehensive_metrics(model, df_train, df_test, feature_cols, backend='Weighted_cox'):
    """
    计算综合评价指标：C-index, Time-dependent AUC, Brier Score, IBS, RMSE
    """
    try:
        # 准备数据
        df_test_with_weights = df_test.copy()
        df_test_with_weights["Weights"] = np.ones((df_test_with_weights.shape[0],))
        test_data_for_score = df_test_with_weights[feature_cols + [duration_col, event_col, "Weights"]]

        # 1. C-index and predictions
        if backend in ["Weighted_cox"]:
            c_index = concordance_index(test_data_for_score[duration_col],
                                      -model.predict_partial_hazard(test_data_for_score),
                                      test_data_for_score[event_col])
            test_predictions = model.predict_partial_hazard(test_data_for_score)
            model_type = 'cox'
        elif backend in ["LogLogistic", "Weibull", "LogNormal"]:
            c_index = model.score(test_data_for_score, scoring_method='concordance_index')
            # AFT模型：使用预期生存时间的负值作为风险评分
            test_predictions = -model.predict_expectation(test_data_for_score)
            model_type = 'aft'
        else:
            c_index = model.score(test_data_for_score, scoring_method='concordance_index')
            test_predictions = -model.predict_partial_hazard(test_data_for_score) if hasattr(model, 'predict_partial_hazard') else None
            model_type = 'other'

        # 2. 为sksurv格式准备数据
        y_train = Surv.from_dataframe(event_col, duration_col, df_train)
        y_test = Surv.from_dataframe(event_col, duration_col, df_test)

        # 3. Time-dependent AUC
        td_auc = np.nan
        try:
            if test_predictions is not None:
                # 获取训练集和测试集中事件发生的时间点
                train_event_times = df_train[df_train[event_col] == 1][duration_col]
                test_event_times = df_test[df_test[event_col] == 1][duration_col]

                # 关键检查：确保测试集没有超出训练集时间范围的事件
                max_train_time = df_train[duration_col].max()
                max_test_time = df_test[duration_col].max()

                if max_test_time > max_train_time:
                    logging.warning(f"Test data has times beyond training data: test_max={max_test_time}, train_max={max_train_time}")
                    # 在这种情况下，我们需要更加保守
                    effective_max_time = max_train_time * 0.8
                else:
                    effective_max_time = max_train_time * 0.9

                if len(train_event_times) > 0:
                    # 使用训练集事件时间的分位数，但严格限制在有效最大时间内
                    train_event_times_sorted = np.sort(train_event_times.values)

                    # 只选择那些远小于有效最大时间的分位数
                    candidate_percentiles = [25, 50, 75]
                    candidate_times = np.percentile(train_event_times_sorted, candidate_percentiles)

                    # 严格过滤：确保所有时间点都远小于有效最大时间
                    times = candidate_times[candidate_times < effective_max_time]
                    times = times[times > 0]
                    times = np.unique(times)

                    # 如果测试集有事件超出训练集范围，进一步限制
                    if len(test_event_times) > 0:
                        max_test_event_time = test_event_times.max()
                        if max_test_event_time > max_train_time:
                            # 更严格的限制
                            times = times[times < max_train_time * 0.7]

                    # 如果过滤后没有时间点，使用最保守的策略
                    if len(times) == 0:
                        min_time = train_event_times.min()
                        conservative_time = min(min_time * 3, effective_max_time * 0.3)
                        if conservative_time > 0 and conservative_time < effective_max_time:
                            times = np.array([conservative_time])

                    if len(times) > 0:
                        risk_scores = test_predictions.values if hasattr(test_predictions, 'values') else test_predictions

                        # 最终安全检查：确保所有时间点都严格小于训练集最大时间
                        max_train_observed = df_train[duration_col].max()
                        times_final = times[times < max_train_observed * 0.85]

                        if len(times_final) > 0:
                            try:
                                auc_scores, mean_auc = cumulative_dynamic_auc(y_train, y_test, risk_scores, times_final)
                                td_auc = mean_auc
                                logging.debug(f"AUC calculated at times: {times_final}")
                            except Exception as auc_error:
                                logging.warning(f"AUC calculation failed even with safe times: {str(auc_error)}")
                                td_auc = np.nan
                        else:
                            logging.warning("All time points filtered out for safety")
                    else:
                        logging.warning("No valid time points for AUC calculation after strict filtering")
                else:
                    logging.warning("No events found in training data for AUC calculation")
        except Exception as e:
            logging.warning(f"Time-dependent AUC calculation failed: {str(e)}")

        # 4. Brier Score and IBS
        mean_brier = np.nan
        ibs = np.nan
        try:
            # 计算安全的评估时间点
            train_event_times = df_train[df_train[event_col] == 1][duration_col]
            test_event_times = df_test[df_test[event_col] == 1][duration_col]
            eval_times = []  # 初始化eval_times

            if len(train_event_times) > 0:
                # 计算安全的时间上限
                max_train_time = df_train[duration_col].max()
                max_test_time = df_test[duration_col].max()
                safe_upper_limit = min(max_train_time, max_test_time) * 0.75  # 更保守

                if len(test_event_times) > 0:
                    max_test_event_time = test_event_times.max()
                    safe_upper_limit = min(safe_upper_limit, max_test_event_time * 0.8)

                # 使用训练集事件时间的分位数
                train_event_times_sorted = np.sort(train_event_times.values)
                candidate_times = np.percentile(train_event_times_sorted, [30, 60])

                # 过滤时间点
                eval_times = candidate_times[candidate_times < safe_upper_limit]
                eval_times = eval_times[eval_times > 0]
                eval_times = np.unique(eval_times)

            # 根据模型类型计算生存概率
            if len(eval_times) > 0:
                if backend in ["Weighted_cox"] and hasattr(model, 'predict_survival_function'):
                    # Cox模型：使用简化的指数模型近似
                    survival_probs = []
                    for t in eval_times:
                        t_scalar = float(t)
                        exp_neg_hazard = np.exp(-test_predictions * t_scalar)
                        probs = np.clip(exp_neg_hazard, 0.01, 0.99)
                        survival_probs.append(probs)
                    survival_probs = np.array(survival_probs).T

                elif backend in ["LogLogistic", "Weibull", "LogNormal"] and hasattr(model, 'predict_survival_function'):
                    # AFT模型：使用模型的生存函数预测
                    survival_probs = []
                    for t in eval_times:
                        t_scalar = float(t)
                        # 使用AFT模型的生存函数
                        surv_func = model.predict_survival_function(test_data_for_score)
                        if hasattr(surv_func, 'iloc'):
                            # 如果是DataFrame格式
                            time_index = surv_func.index
                            closest_time_idx = np.argmin(np.abs(time_index - t_scalar))
                            probs = surv_func.iloc[closest_time_idx].values
                        else:
                            # 如果是其他格式，使用简化方法
                            expected_times = model.predict_expectation(test_data_for_score)
                            # 使用指数分布近似：S(t) = exp(-t/E[T])
                            probs = np.exp(-t_scalar / np.maximum(expected_times, 1))

                        probs = np.clip(probs, 0.01, 0.99)
                        survival_probs.append(probs)
                    survival_probs = np.array(survival_probs).T
                else:
                    # 其他模型类型：使用简化方法
                    survival_probs = []
                    for t in eval_times:
                        t_scalar = float(t)
                        exp_neg_hazard = np.exp(-test_predictions * t_scalar * 0.001)
                        probs = np.clip(exp_neg_hazard, 0.01, 0.99)
                        survival_probs.append(probs)
                    survival_probs = np.array(survival_probs).T

                if len(eval_times) > 0:
                    # 简化的生存概率预测
                    survival_probs = []
                    for t in eval_times:
                        t_scalar = float(t)
                        # 使用简单的指数模型近似生存概率
                        exp_neg_hazard = np.exp(-test_predictions * t_scalar)
                        probs = np.clip(exp_neg_hazard, 0.01, 0.99)
                        survival_probs.append(probs)

                    if len(survival_probs) > 0:
                        survival_probs = np.array(survival_probs).T

                        # 计算Brier Score
                        brier_scores = []
                        for i, t in enumerate(eval_times):
                            try:
                                t_scalar = float(t)
                                bs_result = brier_score(y_train, y_test, survival_probs[:, i], t_scalar)
                                if len(bs_result) > 1:
                                    bs = bs_result[1]
                                    if not np.isnan(bs) and not np.isinf(bs):
                                        brier_scores.append(bs)
                            except:
                                continue

                        if len(brier_scores) > 0:
                            mean_brier = np.mean(brier_scores)

                            # 计算IBS
                            try:
                                ibs = integrated_brier_score(y_train, y_test, survival_probs, eval_times)
                            except:
                                # 手动计算IBS
                                try:
                                    times_diff = np.diff(np.concatenate(([0], eval_times)))
                                    ibs = np.sum(np.array(brier_scores) * times_diff) / eval_times[-1]
                                except:
                                    ibs = np.nan
                        else:
                            logging.warning("No valid Brier scores calculated")
                    else:
                        logging.warning("No survival probabilities calculated")
                else:
                    logging.warning("No valid evaluation times for Brier score calculation")
            else:
                logging.warning("Invalid time range or no events for Brier score calculation")
        except Exception as e:
            logging.warning(f"Brier Score calculation failed: {str(e)}")

        # 5. RMSE for duration prediction
        duration_rmse = np.nan
        try:
            if test_predictions is not None:
                if backend in ["LogLogistic", "Weibull", "LogNormal"]:
                    # AFT模型：直接使用预期生存时间，但需要处理异常值
                    predicted_duration = model.predict_expectation(test_data_for_score)
                    # 处理异常值：限制在合理范围内
                    max_reasonable_time = df_train[duration_col].max() * 3
                    min_reasonable_time = df_train[duration_col].min() * 0.1
                    predicted_duration = np.clip(predicted_duration, min_reasonable_time, max_reasonable_time)
                elif backend in ["Weighted_cox"]:
                    # Cox模型：使用基线中位数生存时间进行转换
                    baseline_median = df_train[duration_col].median()
                    # 简单转换：高风险 -> 短生存时间
                    predicted_duration = baseline_median * np.exp(-test_predictions * 0.5)
                else:
                    # 其他模型：使用基线方法
                    baseline_median = df_train[duration_col].median()
                    predicted_duration = baseline_median * np.exp(-test_predictions * 0.5)

                # 只对未删失的样本计算RMSE
                uncensored_mask = df_test[event_col] == 1
                if uncensored_mask.sum() > 0:
                    true_durations = df_test.loc[uncensored_mask, duration_col]
                    pred_durations = predicted_duration[uncensored_mask]
                    duration_rmse = np.sqrt(mean_squared_error(true_durations, pred_durations))
                    logging.debug(f"RMSE calculated for {uncensored_mask.sum()} uncensored samples using {backend} model")
        except Exception as e:
            logging.warning(f"Duration RMSE calculation failed: {str(e)}")

        return {
            'c_index': c_index,
            'time_dependent_auc': td_auc,
            'brier_score': mean_brier,
            'integrated_brier_score': ibs,
            'duration_rmse': duration_rmse
        }

    except Exception as e:
        logging.error(f"Comprehensive evaluation failed: {str(e)}")
        return {
            'c_index': 0.5,
            'time_dependent_auc': np.nan,
            'brier_score': np.nan,
            'integrated_brier_score': np.nan,
            'duration_rmse': np.nan
        }

def calculate_weighted_score(metrics, weights=None):
    """
    计算加权评分用于Optuna优化
    """
    if weights is None:
        weights = {
            'c_index': 0.5,
            'time_dependent_auc': 0.3,
            'brier_score': 0.1,
            'duration_rmse': 0.1
        }

    score = 0.0
    valid_weights_sum = 0.0

    # C-index (越高越好)
    if not np.isnan(metrics['c_index']):
        score += weights['c_index'] * metrics['c_index']
        valid_weights_sum += weights['c_index']

    # AUC (越高越好)
    if not np.isnan(metrics['time_dependent_auc']):
        score += weights['time_dependent_auc'] * metrics['time_dependent_auc']
        valid_weights_sum += weights['time_dependent_auc']

    # Brier Score (越低越好，需要转换)
    if not np.isnan(metrics['brier_score']):
        normalized_brier = max(0, min(1, metrics['brier_score']))
        score += weights['brier_score'] * (1 - normalized_brier)
        valid_weights_sum += weights['brier_score']

    # RMSE (越低越好，需要归一化并转换)
    if not np.isnan(metrics['duration_rmse']):
        normalized_rmse = 1 / (1 + metrics['duration_rmse'])
        score += weights['duration_rmse'] * normalized_rmse
        valid_weights_sum += weights['duration_rmse']

    # 按有效权重归一化
    if valid_weights_sum > 0:
        score = score / valid_weights_sum
    else:
        score = 0.5  # 默认值

    return score

def data_processing(PATH, params):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols

def cross_validation_objective(trial, df_trainval, feature_cols, params, round_idx):
    """
    Stage 1: Objective function for hyperparameter optimization - Now uses comprehensive metrics
    Perform cross-validation on trainval, return weighted score for Optuna optimization
    """
    # Optuna建议的超参数 - 减少搜索空间
    penalizer = trial.suggest_float('penalizer', 0.0001, 1.5, log=True)  # 缩小范围
    penalizer2 = trial.suggest_float('penalizer2', 0.0001, 1.5, log=True)  # 缩小范围
    topN = trial.suggest_int('topN', 2, 20)  # 缩小范围

    # 新增：当backend为Weighted_cox时，优化SRDO参数
    if params.get('backend') == 'Weighted_cox' and params.get('reweighting') == 'SRDO':
        # SRDO隐藏层大小优化
        n_layers = trial.suggest_int('n_layers', 1, 3)
        srdo_hidden_layers = ()
        for i in range(n_layers):
            dim = trial.suggest_int(f'hidden_dim_{i + 1}', 2, 128, step=2)
            srdo_hidden_layers += (dim,)

        # 权重裁剪范围优化
        w_clip_min = trial.suggest_float('w_clip_min', 0.001, 0.01, log=True)
        w_clip_max = trial.suggest_float('w_clip_max', 2.0, 5.0)

    else:
        # 使用默认值
        srdo_hidden_layers = (64, 108)
        w_clip_min = 0.01
        w_clip_max = 4.0

    # 更新参数
    trial_params = params.copy()
    trial_params.update({
        'penalizer': penalizer,
        'penalizer2': penalizer2,
        'topN': topN,
        'srdo_hidden_layers': srdo_hidden_layers,
        'w_clip_min': w_clip_min,
        'w_clip_max': w_clip_max
    })

    from sklearn.model_selection import StratifiedKFold
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 减少交叉验证折数以减少计算
    n_splits_reduced = trial_params.get('n_splits', 10)  # 最多5折
    skf = StratifiedKFold(n_splits=n_splits_reduced, shuffle=True, random_state=params['seed'] + round_idx)
    X_trainval, y_trainval = df_trainval[feature_cols], df_trainval[event_col]

    # 收集所有fold的综合指标
    fold_metrics = {
        'c_index': [],
        'time_dependent_auc': [],
        'brier_score': [],
        'integrated_brier_score': [],
        'duration_rmse': []
    }

    for fold, (train_idx, val_idx) in enumerate(skf.split(X_trainval, y_trainval)):
        try:
            # 当前折叠的训练集和验证集
            train_data_fold = df_trainval.iloc[train_idx].copy()
            val_data_fold = df_trainval.iloc[val_idx].copy()

            # 计算权重（优化：减少SRDO的迭代次数）
            X_train_fold_np = train_data_fold[feature_cols].values
            n_fold, p_fold = X_train_fold_np.shape
            W = np.ones((n_fold, 1))

            if trial_params.get('reweighting', 'SRDO') == "DWR":
                W = DWR(X_train_fold_np, logger=logging, device=device)
            elif trial_params.get('reweighting', 'SRDO') == "SRDO":
                p_s_fold = p_fold // 2
                # 优化：减少SRDO的迭代次数以加速
                reduced_iters = trial_params.get('iters_balance', 2500)
                # 使用优化的hidden_layer_sizes
                W = SRDO(X_train_fold_np, p_s_fold,
                        hidden_layer_sizes=trial_params['srdo_hidden_layers'],
                        decorrelation_type=trial_params.get('decorrelation_type', 'global'),
                        max_iter=reduced_iters)

            # 权重处理 - 使用优化的裁剪范围
            mean_value = max(np.mean(W), 1e-8)
            W = W * (1 / mean_value)
            W = np.clip(W, trial_params['w_clip_min'], trial_params['w_clip_max'])

            # 训练模型并进行特征选择
            if trial_params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
                model_func = get_algorithm_class(trial_params.get('backend', 'Weighted_cox'))
                model = model_func(
                    X=train_data_fold[feature_cols + [duration_col, event_col]],
                    duration_col=duration_col,
                    event_col=event_col,
                    W=W,
                    pen=penalizer,
                    **trial_params
                )

                # 特征选择
                summary = model.summary
                if topN > 0:
                    sorted_indices = summary['p'].sort_values().head(topN).index
                else:
                    if (summary['p'] < 0.1).any():
                        sorted_indices = summary[summary['p'] < 0.1].index
                    else:
                        sorted_indices = summary.index[:5]  # 至少选择5个特征

                selected_features_fold = list(sorted_indices)
                # 在 cross_validation_objective 函数中修改
                if trial_params.get('backend', 'Weighted_cox') in ["LogLogistic", "Weibull", "LogNormal"]:
                    # 参数化模型：过滤掉 Intercept，只保留特征相关的项
                    feature_summary = summary[~summary.index.get_level_values(1).str.contains('Intercept', na=False)]
                    if topN > 0:
                        sorted_indices = feature_summary['p'].sort_values().head(topN).index
                    else:
                        if (feature_summary['p'] < 0.1).any():
                            sorted_indices = feature_summary[feature_summary['p'] < 0.1].index
                        else:
                            sorted_indices = feature_summary.index[:5]

                    # 提取特征名（tuple 的第二个元素）
                    selected_features_fold = [col[1] for col in sorted_indices]
                    selected_features_fold = list(set(selected_features_fold))

                # 用选择的特征重新训练最终模型
                val_data_fold["Weights"] = np.ones((val_data_fold.shape[0],))
                val_data_for_score = val_data_fold[selected_features_fold + [duration_col, event_col, "Weights"]]

                if trial_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
                    final_model = CoxPHFitter(penalizer=penalizer2)
                    final_model.fit(train_data_fold[selected_features_fold + [duration_col, event_col]],
                                  duration_col=duration_col, event_col=event_col)

                    # 计算综合评估指标
                    metrics = compute_comprehensive_metrics(final_model, train_data_fold, val_data_fold,
                                                          selected_features_fold, trial_params.get('backend', 'Weighted_cox'))
                else:
                    model_func = get_algorithm_class(trial_params.get('backend', 'LogNormal'))
                    final_model = model_func(
                        X=train_data_fold[selected_features_fold + [duration_col, event_col]],
                        duration_col=duration_col,
                        event_col=event_col,
                        W=W,
                        pen=penalizer2,
                    )
                    # 计算综合评估指标
                    metrics = compute_comprehensive_metrics(final_model, train_data_fold, val_data_fold,
                                                          selected_features_fold, trial_params.get('backend', 'LogNormal'))

                # 收集各个指标
                for metric_name, metric_value in metrics.items():
                    fold_metrics[metric_name].append(metric_value)

        except Exception as e:
            # 如果某个fold失败，记录但继续
            logging.warning(f"Fold {fold} failed: {str(e)}")
            # 添加默认值
            fold_metrics['c_index'].append(0.5)
            for metric in ['time_dependent_auc', 'brier_score', 'integrated_brier_score', 'duration_rmse']:
                fold_metrics[metric].append(np.nan)
            continue

    # 如果没有成功的fold，返回很低的分数
    if not fold_metrics['c_index']:
        return 0.5

    # 计算平均指标
    avg_metrics = {}
    for metric_name, values in fold_metrics.items():
        valid_values = [v for v in values if not np.isnan(v)]
        if len(valid_values) > 0:
            avg_metrics[metric_name] = np.mean(valid_values)
        else:
            avg_metrics[metric_name] = np.nan if metric_name != 'c_index' else 0.5

    # 计算加权评分用于Optuna优化
    weighted_score = calculate_weighted_score(avg_metrics)

    # 存储详细的CV指标到trial的user_attrs中
    for metric, value in avg_metrics.items():
        trial.set_user_attr(f'cv_{metric}_mean', value)

    return weighted_score


def train_final_model_with_best_params(df_trainval, df_test, feature_cols, best_params):
    """
    Stage 2: Train the final model on the entire trainval with the best hyperparameters, and then evaluate on the test set
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 首先在整个trainval上训练标准化器（用所有特征）
    df_trainval_scaled = df_trainval.copy()

    # 计算权重 - 使用优化的参数
    X_trainval_np = df_trainval_scaled[feature_cols].values
    n_trainval, p_trainval = X_trainval_np.shape
    W = np.ones((n_trainval, 1))

    if best_params.get('reweighting', 'SRDO') == "DWR":
        W = DWR(X_trainval_np, logger=logging, device=device)
    elif best_params.get('reweighting', 'SRDO') == "SRDO":
        p_s_trainval = p_trainval // 2
        # 使用优化后的SRDO参数 - 修复bug：正确重构hidden_layer_sizes
        if 'n_layers' in best_params:
            # 从Optuna优化结果中重构hidden_layer_sizes
            n_layers = best_params['n_layers']
            srdo_hidden_layers = ()
            for i in range(n_layers):
                dim = best_params.get(f'hidden_dim_{i + 1}')
                if dim is not None:
                    srdo_hidden_layers += (dim,)

            # 如果重构失败，使用默认值
            if len(srdo_hidden_layers) == 0:
                srdo_hidden_layers = (64, 108)
        else:
            # 如果没有优化SRDO参数，使用默认值
            srdo_hidden_layers = best_params.get('srdo_hidden_layers', (64, 108))

        W = SRDO(X_trainval_np, p_s_trainval,
                hidden_layer_sizes=srdo_hidden_layers,
                decorrelation_type=best_params.get('decorrelation_type', 'global'),
                max_iter=best_params.get('iters_balance', 2500))
        logging.info(f"Using optimized SRDO parameters: hidden_layers={srdo_hidden_layers}")

    # 权重处理 - 使用优化的裁剪范围
    mean_value = max(np.mean(W), 1e-8)
    W = W * (1 / mean_value)
    w_clip_min = best_params.get('w_clip_min', 0.004482532833926257)
    w_clip_max = best_params.get('w_clip_max', 2.6)
    W = np.clip(W, w_clip_min, w_clip_max)
    logging.info(f"Using optimized weight clipping: min={w_clip_min:.6f}, max={w_clip_max:.2f}")

    # 用最佳参数训练模型并进行特征选择
    if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
        model_func = get_algorithm_class(best_params.get('backend', 'Weighted_cox'))
        model = model_func(
            X=df_trainval_scaled[feature_cols + [duration_col, event_col]],
            duration_col=duration_col,
            event_col=event_col,
            W=W,
            pen=best_params['penalizer'],
            **best_params
        )

        # 特征选择
        summary = model.summary

        if best_params['topN'] > 0:
            sorted_indices = summary['p'].sort_values().head(best_params['topN']).index
        else:
            if (summary['p'] < 0.1).any():
                sorted_indices = summary[summary['p'] < 0.1].index
            else:
                sorted_indices = summary.index[:5]

        selected_features = list(sorted_indices)
        # 在 cross_validation_objective 函数中修改
        if best_params.get('backend', 'Weighted_cox') in ["LogLogistic", "Weibull", "LogNormal"]:
            # 参数化模型：过滤掉 Intercept，只保留特征相关的项
            feature_summary = summary[~summary.index.get_level_values(1).str.contains('Intercept', na=False)]
            if best_params['topN'] > 0:
                sorted_indices = feature_summary['p'].sort_values().head(best_params['topN']).index
            else:
                if (feature_summary['p'] < 0.1).any():
                    sorted_indices = feature_summary[feature_summary['p'] < 0.1].index
                else:
                    sorted_indices = feature_summary.index[:5]

            # 提取特征名（tuple 的第二个元素）
            selected_features = [col[1] for col in sorted_indices]
            selected_features = list(set(selected_features))

        logging.info(f"Final model selected {len(selected_features)} features: {selected_features}")

        # 重新标准化训练验证集（只用选择的特征）
        df_trainval_final = df_trainval.copy()

        # 用选择的特征训练最终模型
        if best_params.get('backend', 'Weighted_cox') in ["Weighted_cox"]:
            final_model = CoxPHFitter(penalizer=best_params['penalizer2'])
            final_model.fit(df_trainval_final[selected_features + [duration_col, event_col]],
                          duration_col=duration_col, event_col=event_col)
        else:
            model_func = get_algorithm_class(best_params.get('backend', 'LogNormal'))
            final_model = model_func(
                X=df_trainval_final[selected_features + [duration_col, event_col]],
                duration_col=duration_col,
                event_col=event_col,
                W=W,
                pen=best_params['penalizer2'],
            )

        # 准备测试集（现在特征名称匹配了）
        df_test_scaled = df_test.copy()
        df_test_scaled["Weights"] = np.ones((df_test_scaled.shape[0],))
        test_data_for_score = df_test_scaled[selected_features + [duration_col, event_col, "Weights"]]

        # 计算综合评估指标
        comprehensive_metrics = compute_comprehensive_metrics(
            final_model, df_trainval_final, df_test, selected_features, best_params.get('backend', 'Weighted_cox')
        )

        logging.info("=== Final Model Comprehensive Metrics ===")
        for metric_name, metric_value in comprehensive_metrics.items():
            if not np.isnan(metric_value):
                logging.info(f"Final test {metric_name}: {metric_value:.4f}")
            else:
                logging.warning(f"Final test {metric_name}: Failed to calculate")

        return comprehensive_metrics, selected_features, final_model

    return None, [], None


def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting Round {round_idx} ---")

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Hyperparameter optimization (using Optuna)
    # ===============================
    logging.info("=== Stage 1: Starting hyperparameter optimization ===")

    n_trials_reduced = min(200, n_trials if n_trials else 25)

    logging.info(f"Use {n_trials_reduced} Optuna trials")

    study = optuna.create_study(direction='maximize',
                               sampler=optuna.samplers.TPESampler())

    # 定义优化目标函数
    def objective_wrapper(trial):
        return cross_validation_objective(trial, df_trainval, feature_cols, params, round_idx)

    # 执行超参数优化
    study.optimize(objective_wrapper, n_trials=n_trials_reduced)

    # 获取最佳参数
    best_params = params.copy()
    best_params.update(study.best_params)
    best_cv_score = study.best_value

    logging.info(f"Hyperparameter optimization completed! Best validation C-index: {best_cv_score:.4f}")
    logging.info(f"Best parameters: {study.best_params}")

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final model training and evaluation ===")

    comprehensive_metrics, selected_features, final_model = train_final_model_with_best_params(
        df_trainval, df_test, feature_cols, best_params
    )

    if comprehensive_metrics is not None:
        logging.info(f"Number of selected features: {len(selected_features)}")

        # 获取最佳trial的CV指标
        best_trial = study.best_trial
        best_cv_metrics = {}
        for attr_name, attr_value in best_trial.user_attrs.items():
            best_cv_metrics[attr_name] = attr_value

        # 记录最佳CV指标
        if 'cv_c_index_mean' in best_cv_metrics:
            logging.info(f"Best CV metrics - C-index: {best_cv_metrics.get('cv_c_index_mean', 'N/A'):.4f}, "
                        f"AUC: {best_cv_metrics.get('cv_time_dependent_auc_mean', 'N/A'):.4f}, "
                        f"Brier: {best_cv_metrics.get('cv_brier_score_mean', 'N/A'):.4f}, "
                        f"RMSE: {best_cv_metrics.get('cv_duration_rmse_mean', 'N/A'):.4f}")

        # 构建完整的结果字典，包含所有评价指标
        result = {
            "best_cv_score": best_cv_score,
            "best_params": best_params,
            "selected_features_count": len(selected_features)
        }

        # 添加CV期间的最佳指标
        for cv_metric, cv_value in best_cv_metrics.items():
            result[f"best_{cv_metric}"] = cv_value

        # 添加所有最终测试评估指标
        for metric_name, metric_value in comprehensive_metrics.items():
            result[f"final_test_{metric_name}"] = metric_value

        return result
    else:
        logging.warning("Final model training failed")
        return {}

def main(params):
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)
    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"StableCoxCox_Optuna_{timestamp}.log")

        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 获取根logger并添加处理器
        logger = logging.getLogger()
        logger.handlers.clear()  # 清除现有处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info(f"StableCox Optuna Optimization Started")
        logging.info(f"Log file: {log_filename}")
        logging.info(f"MPI Configuration: {size} processes")
        logging.info(f"Parameters: {params}")
        logging.info(f'Model using in this file is: {params["backend"]}')

    PATH = params['PATH']
    setup_seed(params['seed'])

    df_trainval, df_test, feature_cols = data_processing(PATH, params)

    all_indices = list(range(params['times']))
    my_indices = [i for i in all_indices if i % size == rank]

    my_results_list = dd(list)

    for i in my_indices:
        if rank == 0:
            logging.info(f"Starting Round {i + 1}/{params['times']}")
        # df_trainval, df_test, feature_cols = data_processing(PATH, i)

        results_per_run = objective(params['n_trials'], params, i, logging, PATH, df_trainval, df_test, feature_cols)
        if results_per_run:
            for k, v in results_per_run.items():
                my_results_list[k].append(v)

    all_results_list = comm.gather(my_results_list, root=0)

    if rank == 0:
        final_results_list = dd(list)
        for res in all_results_list:
            for k, v in res.items():
                final_results_list[k].extend(v)

        # 保存最终结果
        if params.get('backend', 'Weighted_cox') in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
            if final_results_list:
                logging.info("=" * 70)
                logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
                logging.info("=" * 70)

                for metric_name, value_list in final_results_list.items():
                    if metric_name == 'best_params':
                        continue

                    numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                    if numeric_values:
                        final_mean = np.mean(numeric_values)
                        final_std = np.std(numeric_values)
                        cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                        logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

                if 'final_test_c_index' in final_results_list:
                    final_test_scores = final_results_list['final_test_c_index']
                    final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                    if final_test_scores:
                        logging.info("-" * 50)
                        logging.info("TEST SET PERFORMANCE ANALYSIS")
                        logging.info("-" * 50)
                        logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                        mean_score = np.mean(final_test_scores)
                        std_score = np.std(final_test_scores)
                        cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                        logging.info("Final Test Set Performance Summary:")
                        logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                        logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                        logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                        logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                        logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                        # Test if variance is too high
                        if cv_score > 10:
                            logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                            logging.warning("1. Increasing test set size")
                            logging.warning("2. Using more Optuna trials")
                            logging.warning("3. Fixing data splits across runs")
                            logging.warning("4. Ensemble methods")
                        logging.info("-" * 50)

                        final_test_auc_scores = final_results_list['final_test_time_dependent_auc']
                        final_test_auc_scores = [s for s in final_test_auc_scores if isinstance(s, (int, float, np.number))]
                        final_test_ibs = final_results_list['final_test_integrated_brier_score']
                        final_test_ibs = [s for s in final_test_ibs if isinstance(s, (int, float, np.number))]
                        final_test_rmse = final_results_list['final_test_duration_rmse']
                        final_test_rmse = [s for s in final_test_rmse if isinstance(s, (int, float, np.number))]
                        logging.info(f"Final Test AUC: Mean={np.mean(final_test_auc_scores):.4f}, Std={np.std(final_test_auc_scores):.4f}")
                        logging.info(f"Final Test IBS: Mean={np.mean(final_test_ibs):.4f}, Std={np.std(final_test_ibs):.4f}")
                        logging.info(f"Final Test RMSE: Mean={np.mean(final_test_rmse):.4f}, Std={np.std(final_test_rmse):.4f}")

                        logging.info("=" * 50)

                    else:
                        logging.info("No valid test scores to analyze")
            else:
                logging.info("No results were generated from the runs.")

if __name__ == "__main__":
    import time
    from datetime import datetime

    begin_time = time.time()


    params = {
        'PATH': r'processed_equipment_data.csv',
        'seed': 9, # 10 最好
        'n_splits': 10,
        'reweighting': 'none',
        'decorrelation_type': 'global',
        'iters_balance': 2500,
        'backend': 'Weighted_cox', #["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]
        'paradigm': 'regr',
        'penalizer': 0.03,
        'penalizer2': 0.03,
        'topN': 5,
        'times': 10,
        'result_dir': 'results',
        'n_trials': 10,
        'test_size': 0.1,
    }

    main(params)
    logging.info(f"Total time: {time.time() - begin_time:.2f} seconds")
#基本运行： mpiexec -n 10 python .\ZZ_StableCoxCox_v2_Optuna.py
#后台运行 nohup mpiexec -n 4 python ZZ_StableCoxCox_v2_Optuna.py &
#实时查看日志 tail -f logs/StableCox_Optuna_*.log
