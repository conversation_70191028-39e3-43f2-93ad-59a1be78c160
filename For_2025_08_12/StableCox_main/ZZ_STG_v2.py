# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxP<PERSON>itter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings

warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import StratifiedKFold
import optuna
from optuna.samplers import TPESampler
import io
import contextlib
import re

# STG相关导入
try:
    from stg import STG
    import stg.utils as stg_utils

    STG_AVAILABLE = True
except ImportError:
    logging.warning("STG package not found. Please install stg package to use this functionality.")
    STG_AVAILABLE = False

# 兼容性修复
import collections
import collections.abc

collections.Sequence = collections.abc.Sequence
collections.Mapping = collections.abc.Mapping
collections.Set = collections.abc.Set

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

# device = 'cuda' if torch.cuda.is_available() else 'cpu'
device = 'cpu'

class STGEarlyStopping:
    """
    Early stopping for STG model training - 简化版本，不依赖state_dict
    """

    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0

    def __call__(self, score):
        if self.best_score is None:
            self.best_score = score
            return False
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = score
            self.counter = 0
        return False


class STGNativeEarlyStopping:
    """
    Early stopping hook for STG's fit method.
    Performs validation internally to decide when to stop.
    """

    def __init__(self, stg_instance, val_data, patience=20, min_delta=0.001):
        self.stg_instance = stg_instance
        self.val_data = val_data
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None

    def __call__(self, model):  # model is the internal torch model, not the STG wrapper
        # Use the stg_instance to call public methods
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            # Use the validation data provided during initialization
            self.stg_instance.evaluate(self.val_data['X'], {'E': self.val_data['E'], 'T': self.val_data['T']})
        output = f.getvalue()

        match = re.search(r"test_CI=([0-9.]+)", output)
        if not match:
            return False  # Continue if score not found

        score = float(match.group(1))

        if self.best_score is None:
            self.best_score = score
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                logging.info(f"Early stopping triggered. Best score: {self.best_score:.4f}")
                return True  # Stop
        else:
            self.best_score = score
            self.counter = 0

        return False  # Continue


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols


def cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx):
    """
    Stage 1: STG hyperparameter optimization using Optuna with cross-validation
    严格按照v5_STG和Cox-example的数据处理方式
    """
    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return 0.5

    # Optuna超参数建议
    n_layers = trial.suggest_int('n_layers', 1, 4)
    hidden_dims = []
    for i in range(n_layers):
        dim = trial.suggest_int(f'hidden_dim_{i + 1}', 1, 64)
        hidden_dims.append(dim)

    activation = trial.suggest_categorical('activation', ['relu', 'selu', 'tanh', 'sigmoid'])
    optimizer = trial.suggest_categorical('optimizer', ['Adam', 'SGD', 'Adagrad'])
    learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-1, log=True)
    sigma = trial.suggest_float('sigma', 0.1, 2.0, log=True)
    lam = trial.suggest_float('lam', 1e-3, 10, log=True)

    # 早停参数
    patience = trial.suggest_int('patience', 20, 50)

    logging.info(f"=== Starting STG cross-validation optimization ===")
    logging.info(f"Hidden dims: {hidden_dims}, activation: {activation}, optimizer: {optimizer}")

    # 交叉验证设置 - 确保足够的样本
    cv_splits = params.get('n_splits', 5)

    # 检查数据集大小，如果太小则减少fold数
    min_samples_per_fold = 20  # 每个fold最少需要20个样本
    if len(df_trainval) < cv_splits * min_samples_per_fold:
        cv_splits = max(2, len(df_trainval) // min_samples_per_fold)
        logging.warning(f"Reducing CV folds to {cv_splits} due to small dataset size")

    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
        # random_state=params['seed'] + round_idx
    )

    cv_scores = []

    for fold_idx, (train_idx, val_idx) in enumerate(cv.split(df_trainval, df_trainval[event_col])):
        try:
            # 分割数据
            train_fold = df_trainval.iloc[train_idx].copy()
            val_fold = df_trainval.iloc[val_idx].copy()

            # 检查数据质量
            train_events = train_fold[event_col].sum()
            val_events = val_fold[event_col].sum()

            if train_events == 0 or train_events == len(train_fold):
                logging.warning(f"Fold {fold_idx}: Training set has only one type of outcome, skipping")
                continue

            if val_events == 0 or val_events == len(val_fold):
                logging.warning(f"Fold {fold_idx}: Validation set has only one type of outcome, skipping")
                continue

            if len(val_fold) < 5:  # 验证集太小
                logging.warning(f"Fold {fold_idx}: Validation set too small ({len(val_fold)} samples), skipping")
                continue

            # 严格按照v5_STG的标准化方式 - 关键修复
            # 计算训练集的均值和标准差


            # 避免除零错误

            train_fold_scaled = train_fold.copy()

            val_fold_scaled = val_fold.copy()

            # 准备STG格式的数据
            train_X = train_fold_scaled[feature_cols].to_numpy()
            train_y = {'e': train_fold_scaled[event_col].to_numpy(), 't': train_fold_scaled[duration_col].to_numpy()}

            val_X = val_fold_scaled[feature_cols].to_numpy()
            val_y = {'e': val_fold_scaled[event_col].to_numpy(), 't': val_fold_scaled[duration_col].to_numpy()}

            # prepare_data for STG
            train_data = {}
            train_data['X'], train_data['E'], train_data['T'] = stg_utils.prepare_data(train_X, train_y)
            train_data['ties'] = 'noties'

            val_data = {}
            val_data['X'], val_data['E'], val_data['T'] = stg_utils.prepare_data(val_X, val_y)
            val_data['ties'] = 'noties'

            # 再次检查prepared数据
            if len(val_data['E']) < 2 or len(np.unique(val_data['E'])) < 2:
                logging.warning(f"Fold {fold_idx}: Insufficient validation data after preparation, skipping")
                continue

            # 构建STG模型 - 按照Cox-example的方式
            model = STG(
                task_type='cox',
                input_dim=train_data['X'].shape[1],
                output_dim=1,
                hidden_dims=hidden_dims,
                activation=activation,
                optimizer=optimizer,
                learning_rate=learning_rate,
                batch_size=train_data['X'].shape[0],  # 使用全batch大小，与v5_STG一致
                feature_selection=True,
                sigma=sigma,
                lam=lam,
                # random_state=params['seed'] + round_idx ,
                device = device  # 可以根据需要改为'cuda'
            )

            # 早停机制 - 修复：使用STG的early_stop hook
            early_stop_hook = STGNativeEarlyStopping(model, val_data, patience=patience)

            # 训练模型
            max_epochs = min(params.get('max_epochs', 300), 300)  # CV时减少epochs

            model.fit(
                train_data['X'],
                {'E': train_data['E'], 'T': train_data['T']},
                nr_epochs=max_epochs,
                valid_X=val_data['X'],
                valid_y={'E': val_data['E'], 'T': val_data['T']},
                print_interval=max_epochs,  # 禁用fit内部的打印
                verbose=False,
                early_stop=early_stop_hook
            )

            # 从hook中获取最佳分数
            best_val_score = early_stop_hook.best_score if early_stop_hook.best_score is not None else 0.5

            # 只有成功训练的fold才添加到结果中
            if best_val_score > 0:
                cv_scores.append(best_val_score)
                logging.info(f"Fold {fold_idx}: Best validation C-index = {best_val_score:.4f}")

        except Exception as e:
            logging.warning(f"Error in fold {fold_idx}: {str(e)}")
            continue

    # 返回平均交叉验证C-index
    if not cv_scores:
        logging.warning("No successful CV folds, returning random level")
        return 0.5

    mean_cv_score = np.mean(cv_scores)
    logging.info(f"Trial completed: CV C-index = {mean_cv_score:.4f} (from {len(cv_scores)} successful folds)")
    return mean_cv_score


def train_final_stg_model_with_best_params(df_trainval, df_test, feature_cols, best_params, round_idx):
    """
    Stage 2: Train final STG model with best hyperparameters on entire trainval set, then evaluate on test set
    """
    if not STG_AVAILABLE:
        logging.error("STG package not available")
        return None, [], None

    logging.info("=== Training final STG model and evaluation ===")

    # 从best_params中提取STG参数
    n_layers = best_params.get('n_layers', 2)
    hidden_dims = []
    for i in range(n_layers):
        dim = best_params.get(f'hidden_dim_{i + 1}', 64)
        hidden_dims.append(dim)

    activation = best_params.get('activation', 'relu')
    optimizer = best_params.get('optimizer', 'Adam')
    learning_rate = best_params.get('learning_rate', 1e-3)
    sigma = best_params.get('sigma', 0.5)
    lam = best_params.get('lam', 0.1)
    patience = best_params.get('patience', 50)

    logging.info(f"Final model parameters - Hidden dims: {hidden_dims}, activation: {activation}")
    logging.info(f"Learning rate: {learning_rate}, sigma: {sigma}, lambda: {lam}")

    df_trainval_scaled = df_trainval.copy()

    df_test_scaled = df_test.copy()

    # 准备STG格式的数据
    trainval_X = df_trainval_scaled[feature_cols].to_numpy()
    trainval_y = {'e': df_trainval_scaled[event_col].to_numpy(), 't': df_trainval_scaled[duration_col].to_numpy()}

    test_X = df_test_scaled[feature_cols].to_numpy()
    test_y = {'e': df_test_scaled[event_col].to_numpy(), 't': df_test_scaled[duration_col].to_numpy()}

    # prepare_data for STG
    trainval_data = {}
    trainval_data['X'], trainval_data['E'], trainval_data['T'] = stg_utils.prepare_data(trainval_X, trainval_y)
    trainval_data['ties'] = 'noties'

    test_data = {}
    test_data['X'], test_data['E'], test_data['T'] = stg_utils.prepare_data(test_X, test_y)
    test_data['ties'] = 'noties'

    # 构建最终STG模型
    final_model = STG(
        task_type='cox',
        input_dim=trainval_data['X'].shape[1],
        output_dim=1,
        hidden_dims=hidden_dims,
        activation=activation,
        optimizer=optimizer,
        learning_rate=learning_rate,
        batch_size=trainval_data['X'].shape[0],
        feature_selection=True,
        sigma=sigma,
        lam=lam,
        # random_state=best_params.get('seed', 42) + round_idx,
        device= device
    )

    # 训练最终模型 - 不使用早停，以避免数据泄露
    max_epochs = best_params.get('max_epochs', 1000)
    print_interval = max(max_epochs // 10, 100)

    logging.info(f"Training final model for {max_epochs} epochs")

    # 验证测试集数据质量
    test_events_sum = test_data['E'].sum()
    test_total = len(test_data['E'])
    logging.info(f"Test set: {test_total} samples, {test_events_sum} events, {test_total - test_events_sum} censored")


    final_model.fit(
        trainval_data['X'],
        {'E': trainval_data['E'], 'T': trainval_data['T']},
        nr_epochs=max_epochs,
        valid_X=test_data['X'],
        valid_y={'E': test_data['E'], 'T': test_data['T']},
        print_interval=print_interval,
        verbose=True,
        early_stop=None
    )


    # 训练完成后在测试集上评估
    try:
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            final_model.evaluate(test_data['X'], {'E': test_data['E'], 'T': test_data['T']})
        output = f.getvalue()

        # 提取c-index
        match = re.search(r"test_CI=([0-9.]+)", output)
        if match:
            final_test_c_index = float(match.group(1))
            logging.info(f"Final test C-index: {final_test_c_index:.4f}")
        else:
            logging.warning("Could not extract final test C-index")
            final_test_c_index = 0.5
    except Exception as e:
        if "No admissable pairs" in str(e) or "No admissible pairs" in str(e):
            logging.warning(f"Final evaluation failed due to admissible pairs issue: {e}")
            logging.warning("Computing C-index manually...")

            # 手动计算C-index
            try:
                from lifelines.utils import concordance_index

                # 获取模型预测
                predictions = final_model.predict(test_data['X'])
                if hasattr(predictions, 'detach'):
                    predictions = predictions.detach().cpu().numpy()

                # 计算C-index
                final_test_c_index = concordance_index(
                    test_data['T'],
                    -predictions.flatten(),  # 注意符号
                    test_data['E']
                )
                logging.info(f"Manual final test C-index: {final_test_c_index:.4f}")
            except Exception as e2:
                logging.error(f"Manual C-index calculation also failed: {e2}")
                final_test_c_index = 0.5
        else:
            logging.error(f"Final evaluation failed: {e}")
            final_test_c_index = 0.5

    # 获取选择的特征 (STG的特征选择结果)
    try:
        # 尝试多种方法获取特征重要性
        feature_importances = None

        # 方法1: 尝试使用mode='prob'参数 (外部STG包)
        try:
            feature_importances = final_model.get_gates(mode='prob')
            logging.info("Successfully used get_gates(mode='prob')")
        except TypeError as e:
            logging.info(f"get_gates(mode='prob') failed: {e}")

            # 方法2: 尝试不带参数的get_gates() (可能是本地实现)
            try:
                feature_importances = final_model.get_gates()
                logging.info("Successfully used get_gates() without parameters")
            except Exception as e2:
                logging.warning(f"get_gates() without parameters also failed: {e2}")

        # 方法3: 如果上述方法都失败，尝试直接访问内部属性
        if feature_importances is None:
            try:
                # 尝试访问STG内部可能的属性
                if hasattr(final_model, 'mu'):
                    feature_importances = final_model.mu + 0.5
                    logging.info("Used internal mu attribute")
                elif hasattr(final_model, 'gates'):
                    feature_importances = final_model.gates
                    logging.info("Used internal gates attribute")
                else:
                    logging.warning("No suitable method found to extract feature importance")
            except Exception as e3:
                logging.warning(f"Internal attribute access failed: {e3}")

        # 转换为numpy数组并处理tensor情况
        if feature_importances is not None:
            if hasattr(feature_importances, 'detach'):
                feature_importances = feature_importances.detach().cpu().numpy()
            elif hasattr(feature_importances, 'numpy'):
                feature_importances = feature_importances.numpy()
            feature_importances = np.array(feature_importances).flatten()

        if feature_importances is not None and len(feature_importances) > 0:
            # 打印特征重要性信息用于调试
            logging.info(f"Feature importance scores shape: {feature_importances.shape}")
            logging.info(f"Feature importance range: [{feature_importances.min():.4f}, {feature_importances.max():.4f}]")
            logging.info(f"Feature importance mean: {feature_importances.mean():.4f}")
            logging.info(f"Feature importance std: {feature_importances.std():.4f}")

            # 检查是否所有值都为0
            if np.all(feature_importances == 0):
                logging.warning("All feature importance scores are 0!")
                logging.warning("This might indicate:")
                logging.warning("1. Feature selection was not properly enabled")
                logging.warning("2. The model didn't train long enough")
                logging.warning("3. Lambda (regularization) parameter is too high")
                logging.warning("Using equal weights for all features as fallback")
                feature_importances = np.ones(len(feature_cols)) / len(feature_cols)

            # 使用动态阈值策略
            threshold = params.get('threshold_gates', 0.5)

            # 如果最大值小于阈值，使用相对阈值
            if feature_importances.max() < threshold:
                # 使用均值或中位数作为阈值
                relative_threshold = max(feature_importances.mean(), np.median(feature_importances))
                logging.info(f"Using relative threshold {relative_threshold:.4f} instead of {threshold}")
                selected_indices = np.where(feature_importances >= relative_threshold)[0]
            else:
                selected_indices = np.where(feature_importances > threshold)[0]

            selected_features = [feature_cols[i] for i in selected_indices]
            logging.info(f"Features selected with threshold: {len(selected_features)}")

            if len(selected_features) == 0:
                # 如果没有特征被选择，选择最重要的前k个特征
                top_k = min(10, len(feature_cols))
                top_indices = np.argsort(feature_importances)[-top_k:]
                selected_features = [feature_cols[i] for i in top_indices]
                logging.info(f"No features above threshold, selected top {top_k} features")

                # 打印选择的特征及其重要性分数
                logging.info("Selected features and their importance scores:")
                for i, idx in enumerate(top_indices):
                    logging.info(f"  {feature_cols[idx]}: {feature_importances[idx]:.4f}")

            logging.info(f"Finally selected {len(selected_features)} features")
        else:
            logging.error("Failed to extract any feature importance scores")
            selected_features = feature_cols[:10]  # 默认选择前10个特征
            logging.info(f"Using default selection: first 10 features")

    except Exception as e:
        logging.warning(f"Could not extract feature selection results: {str(e)}")
        selected_features = feature_cols[:10]  # 默认选择前10个特征
        logging.info(f"Using default selection: first 10 features")

    return final_test_c_index, selected_features, final_model


def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting STG Round {round_idx}---")

    if not STG_AVAILABLE:
        logging.error("STG package not available. Cannot proceed.")
        return {}

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Hyperparameter optimization (using Optuna)
    # ===============================
    logging.info("=== Stage 1: Starting STG hyperparameter optimization ===")

    # Adaptive adjustment of Optuna trials
    n_trials_reduced = n_trials


    logging.info(f"Use {n_trials_reduced} Optuna trials")

    # Create Optuna study
    study = optuna.create_study(
        direction='maximize',
        sampler=TPESampler(seed=params['seed'] + round_idx)
    )

    # Define objective wrapper
    def objective_wrapper(trial):
        return cross_validation_optuna_stg(trial, df_trainval, feature_cols, params, round_idx)

    # Execute hyperparameter optimization
    study.optimize(objective_wrapper, n_trials=n_trials_reduced)

    # Get best parameters
    best_params = params.copy()
    best_params.update(study.best_params)
    best_cv_score = study.best_value

    logging.info(f"STG hyperparameter optimization completed! Best validation C-index: {best_cv_score:.4f}")
    logging.info(f"Best parameters: {study.best_params}")

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final STG model training and evaluation ===")

    final_test_c_index, selected_features, final_model = train_final_stg_model_with_best_params(
        df_trainval, df_test, feature_cols, best_params, round_idx
    )

    if final_test_c_index is not None:
        logging.info(f"Final test C-index: {final_test_c_index:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_score": best_cv_score,
            "final_test_c_index": final_test_c_index,
            "best_params": best_params,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final STG model training failed")
        return {}


def main(params):
    if not STG_AVAILABLE:
        print("ERROR: STG package not available. Please install the stg package to use this script.")
        exit(1)

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # Simplified logging setup: only main process saves logs
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"STG_Optuna_{timestamp}.log")

        # Configure dual output to file and console
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # Set format
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Get root logger and add handlers
        logger = logging.getLogger()
        logger.handlers.clear()  # Clear existing handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info("=== STG Survival Analysis with Optuna Optimization ===")
        logging.info(f"Logging to: {log_filename}")
        logging.info(f"MPI processes: {size}")
        logging.info(f"Parameters: {params}")

    else:
        # Non-main processes: only console output, no file logging
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
        console_handler.setFormatter(formatter)

        logger = logging.getLogger()
        logger.handlers.clear()
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

    # Create results directory
    os.makedirs(params['result_dir'], exist_ok=True)

    # Record start time
    start_time = time.time()

    PATH = params['PATH']
    df_trainval, df_test, feature_cols = data_processing(PATH)

    # Run multiple rounds across MPI processes
    local_results = dd(list)
    rounds_per_process = params['times'] // size
    remainder = params['times'] % size

    # Distribute rounds among processes
    if rank < remainder:
        local_rounds = rounds_per_process + 1
        start_round = rank * (rounds_per_process + 1)
    else:
        local_rounds = rounds_per_process
        start_round = rank * rounds_per_process + remainder

    logging.info(f"Process {rank}: Running {local_rounds} rounds starting from round {start_round}")

    for i in range(local_rounds):
        round_idx = start_round + i
        logging.info(f"Process {rank}: Starting round {round_idx + 1}/{params['times']}")

        try:
            result = objective(
                n_trials=params.get('n_trials', 30),
                params=params,
                round_idx=round_idx,
                logger=logging,
                PATH=params['PATH'],
                df_trainval=df_trainval,
                df_test=df_test,
                feature_cols=feature_cols
            )

            if result:
                # 将结果按指标分类存储到字典中
                for k, v in result.items():
                    local_results[k].append(v)
                logging.info(f"Round {round_idx + 1} completed successfully")
            else:
                logging.warning(f"Round {round_idx + 1} failed")

        except Exception as e:
            logging.error(f"Error in round {round_idx + 1}: {str(e)}")

    # Gather results from all processes
    all_results = comm.gather(local_results, root=0)

    # Process and save results (only main process)
    if rank == 0:
        # Flatten results
        final_results_list = dd(list)
        for process_results in all_results:
            for k, v in process_results.items():
                final_results_list[k].extend(v)

        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                if metric_name == 'best_params':
                    continue

                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                        logging.warning("1. Increasing test set size")
                        logging.warning("2. Using more Optuna trials")
                        logging.warning("3. Fixing data splits across runs")
                        logging.warning("4. Ensemble methods")
                    logging.info("=" * 70)

            # 保存最佳参数示例
            if 'best_params' in final_results_list and final_results_list['best_params']:
                best_example = final_results_list['best_params'][0]
                logging.info("EXAMPLE OF OPTIMIZED STG PARAMETERS:")
                stg_params = ['n_layers', 'activation', 'optimizer', 'learning_rate', 'sigma', 'lam', 'patience']
                for key in stg_params:
                    if key in best_example:
                        logging.info(f"  {key}: {best_example[key]}")

                # 显示隐藏层维度
                n_layers = best_example.get('n_layers', 0)
                hidden_dims_str = []
                for i in range(n_layers):
                    dim = best_example.get(f'hidden_dim_{i + 1}')
                    if dim is not None:
                        hidden_dims_str.append(str(dim))
                if hidden_dims_str:
                    logging.info(f"  hidden_dims: [{', '.join(hidden_dims_str)}]")
                logging.info("=" * 70)

        else:
            logging.error("No successful results obtained!")

        total_time = time.time() - start_time
        logging.info(f"Total execution time: {total_time:.2f} seconds")
        logging.info("=== Execution completed ===")

if __name__ == "__main__":
    import time
    from datetime import datetime

    # Create logs folder
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    params = {
        'PATH': r'processed_equipment_data_all.csv',
        'seed': 9,  # Fixed base seed
        'n_splits': 8,  # Cross-validation folds
        'times': 5,
        'result_dir': 'results',
        'test_size': 0.1,
        # STG specific parameters
        'max_epochs': 1000,  # Maximum training epochs
        'n_trials': 200,  # Number of Optuna trials
        'threshold_gates': 0.001,
    }

    main(params)


# 运行命令:
# mpiexec -n 4 python ZZ_STG_v1.py
