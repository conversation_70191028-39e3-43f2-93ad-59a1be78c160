import pandas as pd
import numpy as np
from sklearn.model_selection import StratifiedKFold, train_test_split

# 加载数据
df = pd.read_csv('For_2025_08_05/StableCox_main/processed_equipment_data.csv')
print('原始数据形状:', df.shape)

# 模拟数据分割过程
df_trainval, df_test = train_test_split(df, test_size=0.1, random_state=9, stratify=df['event'])

print('训练验证集形状:', df_trainval.shape)
print('测试集形状:', df_test.shape)

# 检查交叉验证分割
skf = StratifiedKFold(n_splits=8, shuffle=True, random_state=9)
X_trainval, y_trainval = df_trainval[['duration']], df_trainval['event']

print('\n各个fold的数据分布:')
for fold, (train_idx, val_idx) in enumerate(skf.split(X_trainval, y_trainval)):
    train_data_fold = df_trainval.iloc[train_idx]
    val_data_fold = df_trainval.iloc[val_idx]
    
    train_events = train_data_fold[train_data_fold['event'] == 1]['duration']
    val_events = val_data_fold[val_data_fold['event'] == 1]['duration']
    
    print(f'Fold {fold}:')
    print(f'  训练集: {len(train_data_fold)} 样本, 事件数: {len(train_events)}, 最大时间: {train_data_fold["duration"].max():.0f}')
    print(f'  验证集: {len(val_data_fold)} 样本, 事件数: {len(val_events)}, 最大时间: {val_data_fold["duration"].max():.0f}')
    if len(train_events) > 0:
        print(f'  训练集事件最大时间: {train_events.max():.0f}')
    if len(val_events) > 0:
        print(f'  验证集事件最大时间: {val_events.max():.0f}')
    
    # 检查问题情况
    if len(val_events) > 0 and len(train_events) > 0:
        max_val_event = val_events.max()
        max_train_time = train_data_fold['duration'].max()
        if max_val_event >= max_train_time * 0.85:
            print(f'  ⚠️  潜在问题: 验证集事件时间 {max_val_event:.0f} 接近训练集最大时间 {max_train_time:.0f}')
    print()
