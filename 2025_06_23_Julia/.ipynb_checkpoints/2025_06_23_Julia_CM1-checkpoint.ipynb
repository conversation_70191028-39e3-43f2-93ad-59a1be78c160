#%%
using Pkg
Pkg.add("JuMP")
#%%
Pkg.add("GLPK")
#%%
# Decisions: quantities of A and B (x and y)
using JuMP
using GLPK
#%%
m = Model(GLPK.Optimizer)
#%%
#@variable(m, x, lower_bound=0) 
@variable(m,  0 ≤ x)

@variable(m, y, lower_bound=0) # 可以单独删除一个x或者一个y，但是平时还是直接重新加载model就可以了
#%%
m
#%%
@constraint(m, 10*x + 5*y ≤ 2000)
@constraint(m, x + 4*y ≤ 480)
#%%
m
#%%
@objective(m, Max, 1*x + 2*y)
#%%
JuMP.optimize!(m)
#%%
value.(x)
#%%
value.(y)
#%%
objective_value(m)
#%% md
# Travelling salesman problem(TSP) 
#%%
# @variable(m,  z, Bin) # Binary 
#%%
TSP = Model(GLPK.Optimizer)
#%%
N = 4
#%%
A = 1:N
#%%
d = [0 10 5 15; 
    7 0 9 20; 
    5 12 0 10; 
    10 8 14 0]; # 这里就像C++一样，但是在C++中 “；”是必须的，这里再Julia中是不显示结果的
#%%
@variable(TSP, x[A, A], Bin)
#%%
@variable(TSP, u[A], Int)
#%%
@expression(TSP, TotalDist, sum(d[i,j] * x[i,j] for i∈A, j∈A)) #将大的objective拆成不同的expression
#%%
for j ∈ A 
    @constraint(TSP, sum(x[i,j] for i ∈ A if i≠j ) == 1)
end
#%%
@constraint(TSP, [i∈A], sum(x[i,j] for j in A if i≠j) == 1)
#%%
@constraint(TSP, [i∈2:N, j∈2:N, i≠j], u[i] - u[j] + (N-1)*x[i,j] ≤ (N-2) )
#%%
@constraint(TSP, u[1] == 1)
#%%
@constraint(TSP, [i∈2:N], 2≤u[i]≤N )
#%%
@objective(TSP, Min, TotalDist)
#%%
JuMP.optimize!(TSP)
#%%
value.(x)
#%%
value.(u)
#%%
value.(TotalDist)
#%%
objective_value(TSP)